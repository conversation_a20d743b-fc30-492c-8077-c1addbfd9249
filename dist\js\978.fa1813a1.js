"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[978],{50978:function(t,a,e){e.r(a),e.d(a,{default:function(){return v}});var n=function(){var t=this,a=t._self._c;return a("el-card",{attrs:{shadow:"never",header:"最近1小时访问情况","body-style":"padding: 14px 5px 0 0;"}},[a("v-chart",{ref:"visitHourChart",staticStyle:{height:"323px"},attrs:{option:t.visitHourChartOption}})],1)},s=[],r=e(68023),i=e(91416),o=e(92854),c=e(31281),d=e(82739),u=e(76395),m=e(59634),p=e(51430),h=e(16816);(0,r.D)([i.N,o.N,c.N,d.N,u.N]);var l={components:{VChart:m.ZP},mixins:[(0,h.i)(["visitHourChart"])],data(){return{visitHourChartOption:{}}},created(){this.getVisitHourData()},methods:{getVisitHourData(){(0,p.CY)().then((t=>{this.visitHourChartOption={tooltip:{trigger:"axis"},legend:{data:["浏览量","访问量"],right:20},xAxis:[{type:"category",boundaryGap:!1,data:t.map((t=>t.time))}],yAxis:[{type:"value"}],series:[{name:"浏览量",type:"line",smooth:!0,symbol:"none",areaStyle:{opacity:.5},data:t.map((t=>t.views))},{name:"访问量",type:"line",smooth:!0,symbol:"none",areaStyle:{opacity:.5},data:t.map((t=>t.visits))}]}})).catch((t=>{this.$message.error(t.message)}))}}},y=l,g=e(1001),f=(0,g.Z)(y,n,s,!1,null,null,null),v=f.exports},51430:function(t,a,e){e.d(a,{CY:function(){return i},KK:function(){return o},Zs:function(){return r},fy:function(){return s}});e(21703);var n=e(18684);async function s(){const t=await n.Z.get("https://cdn.eleadmin.com/20200610/analysis-pay-num.json");return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(){const t=await n.Z.get("https://cdn.eleadmin.com/20200610/analysis-saleroom.json");return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function i(){const t=await n.Z.get("https://cdn.eleadmin.com/20200610/analysis-visits.json");return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function o(){const t=await n.Z.get("https://cdn.eleadmin.com/20200610/analysis-hot-search.json");return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}}}]);