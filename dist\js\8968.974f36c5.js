"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[8968,2389,3581],{74952:function(e,t,a){a.d(t,{Z:function(){return h}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-search"},[t("hl-form",e._b({scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"handle-btns"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQueryClick}},[e._v("搜索")]),t("el-button",{on:{click:e.handleResetClick}},[e._v("重置")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:"formData"}},"hl-form",e.searchFormConfig,!1))],1)},s=[],i=(a(33948),function(){var e=this,t=e._self._c;return t("div",{staticClass:"hl-form"},[t("div",{staticClass:"header"},[e._t("header")],2),t("el-form",{attrs:{"label-width":e.labelWidth,model:e.formData,rules:e.rules}},[t("el-row",[e._l(e.formItems,(function(a){return[t("el-col",e._b({key:a.label},"el-col",e.colLayout,!1),[a.isHidden?e._e():t("el-form-item",{style:e.itemStyle,attrs:{label:a.label,rules:a.rules}},["input"===a.type||"password"===a.type?[t("el-input",e._b({attrs:{placeholder:a.placeholder,"show-password":"password"===a.type},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-input",a.otherOptions,!1))]:"select"===a.type?[t("el-select",e._b({staticStyle:{width:"100%"},attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-select",a.otherOptions,!1),e._l(a.options,(function(o){var s,i,r,n;return t("el-option",{key:null!==(s=o[a.optionValue])&&void 0!==s?s:o.value,attrs:{value:null!==(i=o[a.optionValue])&&void 0!==i?i:o.value,label:null!==(r=o[a.optionLabel])&&void 0!==r?r:o.title}},[e._v(e._s(null!==(n=o[a.optionLabel])&&void 0!==n?n:o.title))])})),1)]:"datepicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"twoDatePicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"section"===a.type?[t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[0]}`],callback:function(t){e.$set(e.formData,`${a.field[0]}`,t)},expression:"formData[`${item.field[0]}`]"}},"el-input",a.otherOptions,!1))],1),t("el-col",{staticClass:"line",attrs:{span:2}},[e._v("-")]),t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[1]}`],callback:function(t){e.$set(e.formData,`${a.field[1]}`,t)},expression:"formData[`${item.field[1]}`]"}},"el-input",a.otherOptions,!1))],1)]:e._e()],2)],1)]})),e._t("footer")],2)],1)],1)}),r=[],n={props:{value:{type:Object,required:!0},formItems:{type:Array,default:()=>[]},rules:{type:Object,default:()=>{}},labelWidth:{type:String,default:"100px"},itemStyle:{type:Object,default:()=>({padding:"10px 40px"})},colLayout:{type:Object,default:()=>({xl:6,lg:8,md:12,sm:24,xs:24})}},data(){return{formData:{...this.value},pickerOptions:{shortcuts:[{text:"最近一周",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},watch:{formData:{handler(e){this.$emit("input",e)},deep:!0}}},l=n,c=a(1001),d=(0,c.Z)(l,i,r,!1,null,"3585fc65",null),u=d.exports,p={components:{HlForm:u},props:{searchFormConfig:{type:Object,reuqired:!0}},data(){return{formOriginData:{},formData:{}}},created(){var e,t;const a=null!==(e=null===(t=this.searchFormConfig)||void 0===t?void 0:t.formItems)&&void 0!==e?e:[];for(const o of a)Array.isArray(o.field)?o.field.forEach((e=>{this.formOriginData[e]=""})):this.formOriginData[o.field]="";this.formData=this.formOriginData},methods:{handleResetClick(){for(const e in this.formOriginData)this.formData[`${e}`]=this.formOriginData[e];this.$emit("resetBtnClick")},handleQueryClick(){const e={...this.formData};for(const t in e)""===e[t]&&delete e[t];this.$emit("queryBtnClick",e)}}},f=p,m=(0,c.Z)(f,o,s,!1,null,"a0a65568",null),h=m.exports},2389:function(e,t,a){a.r(t),a.d(t,{default:function(){return d}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"add-terminal"},[t("el-form",{ref:"ruleFormRef",staticClass:"el-form",attrs:{"label-position":"right",rules:e.rules,"label-width":"120px",model:e.formOptions}},[t("el-form-item",{attrs:{label:"CPS名称",prop:"cpsName"}},[t("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入cps名称"},model:{value:e.formOptions.cpsName,callback:function(t){e.$set(e.formOptions,"cpsName",t)},expression:"formOptions.cpsName"}})],1),t("el-form-item",{attrs:{label:"CPS编码",prop:"cpsNumber"}},[t("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入cps编码"},model:{value:e.formOptions.cpsNumber,callback:function(t){e.$set(e.formOptions,"cpsNumber",t)},expression:"formOptions.cpsNumber"}})],1)],1),t("div",{staticClass:"btn-submit"},[e._t("default")],2)],1)},s=[],i=a(63581),r={components:{},props:{addModuleInfo:{type:Object,default:()=>{}}},data(){return{rules:i.rules,formOptions:{cpsName:"",cpsNumber:""}}},watch:{addModuleInfo(e){this.initialize(e)}},created(){this.initialize(this.addModuleInfo)},methods:{initialize(e){this.fileList=[],Object.keys(e).length?this.formOptions={...e}:this.formOptions=this.$options.data().formOptions},handleSubmit(){let e=!1;return this.$refs["ruleFormRef"].validate((t=>{e=!!t})),{isSubmit:e,formOptions:this.formOptions}}}},n=r,l=a(1001),c=(0,l.Z)(n,o,s,!1,null,"62a39db1",null),d=c.exports},88968:function(e,t,a){a.r(t),a.d(t,{default:function(){return f}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("el-card",{attrs:{shadow:"never"}},[t("pageSearch",{attrs:{searchFormConfig:e.searchFormConfig},on:{queryBtnClick:e.reload}}),t("ele-pro-table",{key:e.itemKey,ref:"table",attrs:{columns:e.columns,datasource:e.datasource,"cache-key":"playeCityModuleTable"},scopedSlots:e._u([{key:"toolbar",fn:function(){return[e.$hasPermission("hnzhsl:hnslGoodsCpsH5:save")?t("el-button",{staticClass:"ele-btn-icon",attrs:{size:"small",type:"primary",icon:"el-icon-plus"},on:{click:function(t){e.addModuleInfo={},e.isShowNewConfig=!0}}},[e._v(" 新建 ")]):e._e()]},proxy:!0},{key:"status",fn:function({row:a}){return[t("el-tag",{attrs:{type:1===a.status?"success":"danger"}},[e._v(e._s(1===a.status?"上架":"下架"))])]}},{key:"action",fn:function({row:a}){return[e.$hasPermission("hnzhsl:hnslGoodsCpsH5:remove")?t("el-link",{attrs:{type:"danger",underline:!1,icon:"el-icon-delete"},on:{click:function(t){return e.remove(a)}}},[e._v(" 删除 ")]):e._e(),e.$hasPermission("hnzhsl:hnslGoodsCpsH5:update")?t("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handleModify(a)}}},[e._v(" 修改 ")]):e._e()]}}])})],1),t("ele-modal",{attrs:{width:"780px",title:(Object.keys(e.addModuleInfo).length?"修改":"新增")+"csp模板",visible:e.isShowNewConfig},on:{"update:visible":function(t){e.isShowNewConfig=t}}},[t("addCsp",{ref:"cardRef",attrs:{addModuleInfo:e.addModuleInfo}},[t("el-button",{staticClass:"btn-add back submit",attrs:{type:"primary"},on:{click:function(t){e.isShowNewConfig=!1}}},[e._v("返回")]),t("el-button",{staticClass:"btn-add submit",attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)},s=[],i=a(41428),r=a(63581),n=a(2389),l=a(74952),c={components:{addCsp:n["default"],pageSearch:l.Z},data(){return{columns:r.columns,isShowNewConfig:!1,cardRef:null,itemKey:null,addModuleInfo:{},searchFormConfig:r.searchFormConfig}},methods:{handleModify(e){this.addModuleInfo={...e},this.isShowNewConfig=!0},remove(e){this.$confirm("确定删除该数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{const t=this.$messageLoading("请求中..");(0,i.Jh)(e.id).then((()=>{t.close(),this.$message.success("删除成功"),this.reload()})).catch((e=>{t.close(),this.$message.error(e.message)}))}))},async handleSubmit(){const{isSubmit:e,formOptions:t}=this.$refs.cardRef.handleSubmit();if(!e)return;const a={...t};Object.keys(this.addModuleInfo).length?(await(0,i.w6)({...a,id:this.addModuleInfo.id}),this.$message.success("修改成功")):(await(0,i.GG)(a),this.$message.success("新增成功")),this.reload(),this.isShowNewConfig=!1},datasource({page:e,limit:t,where:a,order:o}){return(0,i.ZS)({page:e,limit:t,...a,...o})},reload(e){this.$refs.table.reload({page:1,where:e})}}},d=c,u=a(1001),p=(0,u.Z)(d,o,s,!1,null,"b6b0cab0",null),f=p.exports},41428:function(e,t,a){a.d(t,{Ew:function(){return i},GG:function(){return r},Jh:function(){return n},ZS:function(){return s},w6:function(){return l}});a(21703);var o=a(18684);async function s(e){const t=await o.Z.post("/hnzhsl/hnslGoodsCpsH5/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function i(e){const t=await o.Z.post("/hnzhsl/hnslGoodsCpsH5/list",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await o.Z.post("/hnzhsl/hnslGoodsCpsH5/save",e,{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function n(e){const t=await o.Z.post("/hnzhsl/hnslGoodsCpsH5/remove/"+e,{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function l(e){const t=await o.Z.post("/hnzhsl/hnslGoodsCpsH5/update",e,{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}},63581:function(e,t,a){a.r(t),a.d(t,{columns:function(){return o},rules:function(){return i},searchFormConfig:function(){return s}});const o=[{width:45,type:"index",columnKey:"expand",align:"center",slot:"expand"},{prop:"id",label:"id",align:"center",showOverflowTooltip:!0},{prop:"cpsName",label:"cps名称",align:"center",showOverflowTooltip:!0},{prop:"cpsNumber",label:"cps编码",align:"center",showOverflowTooltip:!0},{columnKey:"action",label:"操作",width:220,align:"center",resizable:!1,slot:"action",showOverflowTooltip:!0}],s={labelWidth:"100px",itemStyle:{padding:"4px"},colLayout:{span:6},formItems:[{field:"cpsName",type:"input",label:"cps名称",placeholder:"请输入cps名称"}]},i={cpsName:[{required:!0,message:"请输入cps名称",trigger:"blur"}],cpsNumber:[{required:!0,message:"请输入cps编码",trigger:"blur"}]}}}]);