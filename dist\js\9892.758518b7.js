"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9892,9793,2132],{74952:function(e,t,a){a.d(t,{Z:function(){return f}});var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-search"},[t("hl-form",e._b({scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"handle-btns"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQueryClick}},[e._v("搜索")]),t("el-button",{on:{click:e.handleResetClick}},[e._v("重置")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:"formData"}},"hl-form",e.searchFormConfig,!1))],1)},l=[],o=(a(33948),function(){var e=this,t=e._self._c;return t("div",{staticClass:"hl-form"},[t("div",{staticClass:"header"},[e._t("header")],2),t("el-form",{attrs:{"label-width":e.labelWidth,model:e.formData,rules:e.rules}},[t("el-row",[e._l(e.formItems,(function(a){return[t("el-col",e._b({key:a.label},"el-col",e.colLayout,!1),[a.isHidden?e._e():t("el-form-item",{style:e.itemStyle,attrs:{label:a.label,rules:a.rules}},["input"===a.type||"password"===a.type?[t("el-input",e._b({attrs:{placeholder:a.placeholder,"show-password":"password"===a.type},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-input",a.otherOptions,!1))]:"select"===a.type?[t("el-select",e._b({staticStyle:{width:"100%"},attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-select",a.otherOptions,!1),e._l(a.options,(function(r){var l,o,s,i;return t("el-option",{key:null!==(l=r[a.optionValue])&&void 0!==l?l:r.value,attrs:{value:null!==(o=r[a.optionValue])&&void 0!==o?o:r.value,label:null!==(s=r[a.optionLabel])&&void 0!==s?s:r.title}},[e._v(e._s(null!==(i=r[a.optionLabel])&&void 0!==i?i:r.title))])})),1)]:"datepicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"twoDatePicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"section"===a.type?[t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[0]}`],callback:function(t){e.$set(e.formData,`${a.field[0]}`,t)},expression:"formData[`${item.field[0]}`]"}},"el-input",a.otherOptions,!1))],1),t("el-col",{staticClass:"line",attrs:{span:2}},[e._v("-")]),t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[1]}`],callback:function(t){e.$set(e.formData,`${a.field[1]}`,t)},expression:"formData[`${item.field[1]}`]"}},"el-input",a.otherOptions,!1))],1)]:e._e()],2)],1)]})),e._t("footer")],2)],1)],1)}),s=[],i={props:{value:{type:Object,required:!0},formItems:{type:Array,default:()=>[]},rules:{type:Object,default:()=>{}},labelWidth:{type:String,default:"100px"},itemStyle:{type:Object,default:()=>({padding:"10px 40px"})},colLayout:{type:Object,default:()=>({xl:6,lg:8,md:12,sm:24,xs:24})}},data(){return{formData:{...this.value},pickerOptions:{shortcuts:[{text:"最近一周",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},watch:{formData:{handler(e){this.$emit("input",e)},deep:!0}}},n=i,c=a(1001),d=(0,c.Z)(n,o,s,!1,null,"3585fc65",null),u=d.exports,p={components:{HlForm:u},props:{searchFormConfig:{type:Object,reuqired:!0}},data(){return{formOriginData:{},formData:{}}},created(){var e,t;const a=null!==(e=null===(t=this.searchFormConfig)||void 0===t?void 0:t.formItems)&&void 0!==e?e:[];for(const r of a)Array.isArray(r.field)?r.field.forEach((e=>{this.formOriginData[e]=""})):this.formOriginData[r.field]="";this.formData=this.formOriginData},methods:{handleResetClick(){for(const e in this.formOriginData)this.formData[`${e}`]=this.formOriginData[e];this.$emit("resetBtnClick")},handleQueryClick(){const e={...this.formData};for(const t in e)""===e[t]&&delete e[t];this.$emit("queryBtnClick",e)}}},h=p,m=(0,c.Z)(h,r,l,!1,null,"a0a65568",null),f=m.exports},89892:function(e,t,a){a.r(t),a.d(t,{default:function(){return C}});var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("el-card",{attrs:{shadow:"never"}},[t("pageSearch",{ref:"searchValue",attrs:{searchFormConfig:e.searchFormConfig},on:{queryBtnClick:e.reload}}),t("ele-pro-table",{ref:"table",attrs:{columns:e.columns,datasource:e.datasource,selection:e.selection,"cache-key":"playeCityModuleTable"},on:{"update:selection":function(t){e.selection=t}},scopedSlots:e._u([{key:"toolbar",fn:function(){return[e.$hasPermission("hnzhsl:hnslH5Order:outputOrder")?t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.bulkExportClick}},[e._v("批量导出")]):e._e(),e.$hasPermission("hnzhsl:hnslH5Order:update")?t("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(t){return e.adminUpdate()}}},[e._v("admin修改")]):e._e()]},proxy:!0},{key:"action",fn:function({row:a}){return[t("el-link",{attrs:{type:1==a.status?"danger":"primary",size:"small",underline:!1},on:{click:function(t){return e.changeStatus(a)}}},[e._v(e._s(1==a.status?"禁用":"允许"))]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.checkClick(a,2)}}},[e._v("详情")])]}}])})],1),t("ele-modal",{attrs:{width:"1000px",title:"订单信息",visible:e.isOrderShow},on:{"update:visible":function(t){e.isOrderShow=t}},scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticStyle:{"text-align":"center"}},[t("el-button",{on:{click:function(t){e.isOrderShow=!1}}},[e._v("取消")]),1==e.lookOrder?t("el-button",{attrs:{type:"primary"},on:{click:e.radiusClick}},[e._v("保存")]):e._e()],1)]},proxy:!0}])},[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",[t("template",{slot:"label"},[e._v("订单编号")]),e._v(" "+e._s(e.orderInfo.orderId)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("下单人")]),e._v(" "+e._s(e.orderInfo.userHhrName)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("所属城市")]),e._v(" "+e._s(e.cityNameMap[e.orderInfo.citycode])+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("联系电话")]),e._v(" "+e._s(e.orderInfo.customerContactPhone)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("下单时间")]),e._v(" "+e._s(e.orderInfo.createdDate)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("套餐名称")]),e._v(" "+e._s(e.orderInfo.goodsName)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("ICCID号")]),e._v(" "+e._s(e.orderInfo.iccid)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("订购号码")]),e._v(" "+e._s(e.orderInfo.customerPhone)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("订单状态")]),t("el-select",{attrs:{clearable:"",placeholder:"默认不展示",value:e.orderDetailInfo(this.orderInfo.orderStatus)},on:{change:function(t){return e.changeOrder(t)}}},e._l(e.orderStatusList,(function(a,r){return t("el-option",{key:a.id,attrs:{label:a.value,value:r,disabled:e.isDisabled}})})),1)],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("订单金额")]),e._v(" "+e._s(e.orderInfo.orderPrice)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("CRM订单号")]),e._v(" "+e._s(e.orderInfo.crmOrderId)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("BPS订单号")]),e._v(" "+e._s(e.orderInfo.bpsOrderId)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("激活时间")]),e._v(" "+e._s(e.orderInfo.orderActivateDate)+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("BPS正式同步结果")]),e._v(" "+e._s(1==e.orderInfo.bpsFormalSynchro?"已同步":"未同步")+" ")],2),t("el-descriptions-item",[t("template",{slot:"label"},[e._v("CRM过费结果")]),e._v(" "+e._s(1==e.orderInfo.crmSureSynchro?"已过费":"未过费")+" ")],2),1==e.lookOrder?t("el-descriptions-item",[t("template",{slot:"label"},[e._v("重新同步BPS和过费")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(t){return e.resynchronization(1)}}},[e._v("开始同步")])],2):e._e(),1==e.lookOrder?t("el-descriptions-item",[t("template",{slot:"label"},[e._v("修改过费状态")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(t){return e.resynchronization(2)}}},[e._v("确认过费")])],2):e._e(),1==e.lookOrder?t("el-descriptions-item",[t("template",{slot:"label"},[e._v("重新提交CRM")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(t){return e.resynchronization(3)}}},[e._v("确认提交")])],2):e._e(),1==e.lookOrder?t("el-descriptions-item",[t("template",{slot:"label"},[e._v("同步支付记录")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(t){return e.resynchronization(4)}}},[e._v("确认同步")])],2):e._e()],1)],1)],1)},l=[],o=(a(21703),a(18684));async function s(e){const t=await o.Z.post("/hnzhsl/hnslOrderH5/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function i(e){const t=await o.Z.post("/hnzhsl/hnslOrderH5/update",e);return 0===t.data.code?t.data:Promise.reject(new Error(t.data.message))}async function n(e){const t=await o.Z.post("/hnzhsl/hnslOrderH5/outputOrder",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function c(e){const t=await o.Z.post("/hnzhsl/hnslOrderH5/outQrUserOrder",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function d(e){const t=await o.Z.post("/hnzhsl/hnslRechargeOrder/outResetOrder",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function u(e){const t=await o.Z.get("/hnzhsl/hnslOrderH5/"+e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function p(e){const t=await o.Z.post("/hnzhsl/hnslOrderH5/decode/resynchronization",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function h(e){const t=await o.Z.post("/hnzhsl/hnslOrderH5/updateCrm",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function m(e){const t=await o.Z.post("/hnzhsl/hnslOrderH5/decode/submitCrm",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function f(e){const t=await o.Z.post("/hnzhsl/hnslOrderH5/synchronizePayToBps",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function v(e){const t=await o.Z.post("/hnzhsl/hnslOrderH5/disableOrder",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}var y=a(59793),g=a(2132),b=a(48504),_=a(74952);const w=e=>{const t={1:"激活成功",2:"支付成功",3:"待支付",4:"订单超时",5:"取消订单",6:"受理失败",7:"已退款"};return t[e]||""};var k={components:{pageSearch:_.Z},computed:{getSrc(){return e=>{const t=`identityCardImage${e+1}`;return this.baseImage[e]||this.imageUrl+this.orderInfo[t]}}},data(){return{lookOrder:0,isDisabled:!1,baseImage:Array(9).fill(null),imageUrl:"https://lst.hn.189.cn/hnzhsl/smallFile/image2.do?fileName=",selectedValue:null,isShowImport:!1,columns:y.columns,searchFormConfig:y.searchFormConfig,cardRef:null,isOrderShow:!1,orderInfo:{},cityNameMap:g.cityNameMap,selection:[],orderStatusList:[{id:1,value:"激活成功"},{id:2,value:"支付成功"},{id:3,value:"待支付"},{id:4,value:"已受理"},{id:5,value:"订单超时"},{id:6,value:"取消订单"},{id:7,value:"受理失败"},{id:8,value:"已退款"}]}},methods:{changeStatus(e){const t={id:e.id,status:e.status?0:1};v(t).then((e=>{console.log("res",e),this.reload(),this.$message.success("操作成功")})).catch((e=>{this.$message.error(e.message)}))},async rechargeRecordClick(){var e=this.$refs.searchValue.formData;const t=this.$messageLoading("导出中..");d(e).then((e=>{t.close(),window.open(`${b.JV}?name=${e.fileName}`),this.isShowApprove=!1})).catch((e=>{t.close(),this.$message.error(e.message),this.isShowApprove=!1}))},async replaceClick(){0!=this.selection.length?this.selection.length>1?this.$message.error("请选择一条数据!"):(this.orderInfo=this.selection[0],u(this.orderInfo.id).then((e=>{this.orderInfo=e,this.baseImage.id=e.id})).catch((e=>{this.$message.error(e)})),this.isImageShow=!0):this.$message.error("请先选择数据!")},async bulkExportClick(){var e=this.$refs.searchValue.formData;const t=await n(e);window.open(`${b.JV}?name=${t.fileName}`)},async personClick(){var e=this.$refs.searchValue.formData;e.saflType=2,await c(e).then((e=>{window.open(`${b.JV}?name=${e.fileName}`)})).catch((e=>{this.$message.error(e.message)}))},checkClick(e){this.lookOrder=0,this.isDisabled=!0,this.orderInfo=e,this.isOrderShow=!0},datasource({page:e,limit:t,where:a,order:r}){return s({page:e,limit:t,...a,...r})},reload(e){e&&(e.createdDateList&&e.createdDateList.length>0?(e.beginTime=e.createdDateList[0],e.endTime=e.createdDateList[1]):(e.beginTime=null,e.endTime=null)),this.$refs.table.reload({page:1,where:e})},orderDetailInfo(e){return w(e)},adminUpdate(){0!=this.selection.length?this.selection.length>1?this.$message.error("请选择一条数据!"):(this.lookOrder=1,this.isDisabled=!1,this.isOrderShow=!0,this.orderInfo=this.selection[0]):this.$message.error("请先选择数据!")},resynchronization(e){switch(console.log("orderInfo",this.orderInfo),e){case 1:p(this.orderInfo).then((e=>{console.log("res",e),0==e.code&&this.$message.success("同步BPS成功!")})).catch((e=>{this.$message.error(e)}));break;case 2:h(this.orderInfo).then((e=>{console.log("res",e),0==e.code&&this.$message.success("修改过费状态成功!")})).catch((e=>{this.$message.error(e.message)}));break;case 3:m(this.orderInfo).then((e=>{console.log("res",e),0==e.code&&this.$message.success("提交CRM成功!")})).catch((e=>{this.$message.error(e.message)}));break;case 4:f(this.orderInfo).then((e=>{console.log("res",e),this.$message.success("同步支付记录成功!")})).catch((e=>{this.$message.error(e.message)}));break}},radiusClick(){var e={orderStatus:this.orderInfo.orderStatus,id:this.orderInfo.id};i(e).then((e=>{this.isOrderShow=!1,this.$message.success(e.message)})).catch((e=>{this.$message.error(e.message)}))},changeOrder(e){this.orderInfo.orderStatus=this.orderStatusList[e].id}}},O=k,$=a(1001),D=(0,$.Z)(O,r,l,!1,null,"20aa9afb",null),C=D.exports},48504:function(e,t,a){a.d(t,{JV:function(){return l},cn:function(){return o}});var r=a(18816);const l=`${r.CT}/download/exportDaoUsers`,o=`${r.CT}/download/template`;r.CT,r.CT,r.CT,r.CT},59793:function(e,t,a){a.r(t),a.d(t,{columns:function(){return r},searchFormConfig:function(){return l}});const r=[{width:45,type:"selection",columnKey:"expand",align:"center",slot:"expand"},{prop:"orderId",label:"订单号",align:"center",showOverflowTooltip:!0},{prop:"customerName",label:"客户姓名",align:"center",showOverflowTooltip:!0},{prop:"channelName",label:"渠道名称",align:"center",showOverflowTooltip:!0},{prop:"goodsName",label:"商品名称",align:"center",showOverflowTooltip:!0},{prop:"createdDate",label:"创建时间",align:"center",showOverflowTooltip:!0},{prop:"status",label:"订单状态",align:"center",showOverflowTooltip:!0,formatter:e=>1==e.orderStatus?"激活成功":2==e.orderStatus?"支付成功":3==e.orderStatus?"待支付":4==e.orderStatus?"订单超时":5==e.orderStatus?"取消订单":6==e.orderStatus?"受理失败":7==e.orderStatus?"已退款":void 0},{prop:"schoolChannelType",label:"渠道类型",align:"center",showOverflowTooltip:!0,formatter:e=>1==e.schoolChannelType?"全渠道":2==e.schoolChannelType?"电渠互联网卡渠道":3==e.schoolChannelType?"分公司渠道运营渠道":"其他"},{columnKey:"action",label:"操作",width:220,align:"center",resizable:!1,slot:"action",showOverflowTooltip:!0}],l={labelWidth:"100px",itemStyle:{padding:"10px"},colLayout:{span:4},formItems:[{field:"saflType",type:"select",label:"订单类型",placeholder:"请选择订单类型",options:[{title:"全部",value:""},{title:"号卡新装",value:1}]},{field:"orderStatus",type:"select",label:"状态",placeholder:"请选择",options:[{title:"全部",value:""},{title:"激活成功",value:1},{title:"支付成功",value:2},{title:"待支付",value:3},{title:"订单超时",value:4},{title:"取消订单",value:5},{title:"受理失败",value:8},{title:"已退款",value:9}]},{field:"customerName",type:"input",label:"身份证|姓名|预占号码",placeholder:"身份证|姓名|预占号码"},{field:"schoolName",type:"input",label:"渠道",placeholder:"请输入渠道名称"},{field:"createdDateList",type:"twoDatePicker",label:"创建时间",otherOptions:{placeholder:"请选择创建时间",type:"datetimerange","value-format":"yyyy-MM-dd HH:mm"}},{field:"orderId",type:"input",label:"订单号",placeholder:"请输入订单号"},{field:"bpsOrderId",type:"input",label:"BPS订单号",placeholder:"请输入订单号"},{field:"schoolChannelType",type:"select",label:"渠道类型",placeholder:"请选择渠道类型",options:[{title:"全部",value:""},{title:"全渠道",value:1},{title:"分公司渠道运营渠道",value:2},{title:"电渠互联网卡渠道",value:3},{title:"其他",value:4}]},{field:"citycode",type:"select",label:"地市",placeholder:"请选择地市",options:[{value:"",title:"全省"},{value:"731",title:"长沙市"},{value:"733",title:"株洲市"},{value:"732",title:"湘潭市"},{value:"734",title:"衡阳市"},{value:"739",title:"邵阳市"},{value:"730",title:"岳阳市"},{value:"736",title:"常德市"},{value:"744",title:"张家界市"},{value:"737",title:"益阳市"},{value:"735",title:"郴州市"},{value:"746",title:"永州市"},{value:"745",title:"怀化市"},{value:"738",title:"娄底市"},{value:"743",title:"湘西市"}]},{field:"goodsName",type:"input",label:"商品名称",placeholder:"请输入商品名称"},{field:"crmOrderId",type:"input",label:"CRM订单号",placeholder:"请输入CRM订单号"}]}},2132:function(e,t,a){a.r(t),a.d(t,{cityNameMap:function(){return r}});const r={700:"全省",730:"岳阳",731:"长沙",732:"湘潭",733:"株洲",734:"衡阳",735:"郴州",736:"常德",737:"益阳",738:"娄底",739:"邵阳",743:"湘西",744:"张家界",745:"怀化",746:"永州"}}}]);