"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9906],{29906:function(e,t,a){a.r(t),a.d(t,{default:function(){return d}});a(74916),a(15306);var s=function(){var e=this,t=e._self._c;return t("ele-modal",{attrs:{width:"680px",visible:e.visible,"append-to-body":!0,"close-on-click-modal":!0,"custom-class":"ele-dialog-form",title:e.isUpdate?"修改模块":"添加模块"},on:{"update:visible":e.updateVisible},scopedSlots:e._u([{key:"footer",fn:function(){return[t("el-button",{on:{click:function(t){return e.updateVisible(!1)}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.save}},[e._v(" 保存 ")])]},proxy:!0}])},[t("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"82px"}},[t("el-row",{attrs:{gutter:15}},[t("el-col",e._b({},"el-col",e.styleResponsive?{sm:12}:{span:12},!1),[t("el-form-item",{attrs:{label:"模块类型:",prop:"moduleTypeName"}},[t("el-select",{staticClass:"ele-block",attrs:{clearable:"",placeholder:"请选择模块类型","value-key":"id"},on:{change:e.changeOption},model:{value:e.form.moduleTypeName,callback:function(t){e.$set(e.form,"moduleTypeName",t)},expression:"form.moduleTypeName"}},e._l(e.modules,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e}})})),1)],1),t("el-form-item",{attrs:{label:"模块名称:",prop:"moduleName"}},[t("el-input",{attrs:{clearable:"",maxlength:100,placeholder:"请输入模块名称"},model:{value:e.form.moduleName,callback:function(t){e.$set(e.form,"moduleName",t)},expression:"form.moduleName"}})],1),t("el-form-item",{attrs:{label:"模块标识:",prop:"moduleCode"}},[t("el-input",{attrs:{clearable:"",maxlength:100,placeholder:"请输入模块标识"},on:{input:function(t){e.form.moduleCode=e.form.moduleCode.replace(/[^\d]/g,"")}},model:{value:e.form.moduleCode,callback:function(t){e.$set(e.form,"moduleCode",t)},expression:"form.moduleCode"}})],1)],1),t("el-col",e._b({},"el-col",e.styleResponsive?{sm:12}:{span:12},!1),[t("el-form-item",{attrs:{label:"跳转路由:",prop:"url"}},[t("el-input",{attrs:{clearable:"",maxlength:11,placeholder:"请输入跳转路由"},model:{value:e.form.url,callback:function(t){e.$set(e.form,"url",t)},expression:"form.url"}})],1),t("el-form-item",{attrs:{label:"状态:"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}})],1)],1)],1)],1)],1)},o=[],r=(a(21703),a(94678)),l={components:{},props:{visible:Boolean,data:Object},data(){const e={id:null,moduleTypeCode:"",moduleTypeName:"",moduleName:"",moduleCode:"",url:"",status:1};return{modules:[{id:1,name:"新装"},{id:2,name:"存量"},{id:3,name:"辅助"}],defaultForm:e,form:{...e},rules:{moduleCode:[{required:!0,trigger:"blur",validator:(e,t,a)=>{var s;if(!t)return a(new Error("请输入模块标识"));(0,r.mL)("moduleCode",t,null===(s=this.data)||void 0===s?void 0:s.moduleCode).then((()=>{a(new Error("模块标识已经存在"))})).catch((()=>{a()}))}}],moduleName:[{required:!0,message:"请输入模块名称",trigger:"blur"}],moduleTypeName:[{required:!0,message:"请选择模块类型",trigger:"blur"}],url:[{required:!0,message:"请输入跳转路由",trigger:"blur"}]},loading:!1,isUpdate:!1}},computed:{styleResponsive(){return this.$store.state.theme.styleResponsive}},methods:{changeOption(e){this.form.moduleTypeCode=e.id,this.form.moduleTypeName=e.name},save(){this.$refs.form.validate((e=>{if(!e)return!1;this.loading=!0;const t={...this.form},a=this.isUpdate?r.DE:r.WM;a(t).then((e=>{this.loading=!1,this.$message.success(e),this.updateVisible(!1),this.$emit("done")})).catch((e=>{this.loading=!1,this.$message.error(e.message)}))}))},updateVisible(e){this.$emit("update:visible",e)}},watch:{visible(e){e?this.data?(this.$util.assignObject(this.form,{...this.data,status:1}),this.isUpdate=!0):this.isUpdate=!1:(this.$refs.form.clearValidate(),this.form={...this.defaultForm})}}},n=l,i=a(1001),u=(0,i.Z)(n,s,o,!1,null,null,null),d=u.exports},94678:function(e,t,a){a.d(t,{DE:function(){return n},L9:function(){return d},Ne:function(){return r},QM:function(){return i},R$:function(){return u},WM:function(){return l},YN:function(){return o},mL:function(){return m}});a(21703);var s=a(18684);async function o(e){const t=await s.Z.post("/hnzsx/hnzsxAppModule/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await s.Z.post("/hnzsx/hnzsxAppModule/list",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function l(e){const t=await s.Z.post("/hnzsx/hnzsxAppModule/save",e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function n(e){const t=await s.Z.post("/hnzsx/hnzsxAppModule/update",e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function i(e){const t=await s.Z.post("/hnzsx/hnzsxAppModule/status",e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function u(e){const t=await s.Z.post("/hnzsx/hnzsxAppModule/"+e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function d(e){const t=await s.Z.post("/hnzsx/hnzsxAppModule/removeBatch",{data:e});return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function m(e,t,a){const o=await s.Z.get("/hnzsx/hnzsxAppModule/existence",{params:{field:e,value:t,id:a}});return 0===o.data.code?o.data.message:Promise.reject(new Error(o.data.message))}}}]);