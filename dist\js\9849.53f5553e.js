"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9849],{39849:function(e,l,o){o.r(l),o.d(l,{columns:function(){return t},searchFormConfig:function(){return a}});const t=[{width:45,type:"selection",columnKey:"expand",align:"center",slot:"expand"},{prop:"goodsNumber",label:"销售品编码",align:"center",showOverflowTooltip:!0},{prop:"limitType",label:"限制类型",align:"center",showOverflowTooltip:!0,formatter:e=>1===e.limitType?"生卡数量限制":"无"},{prop:"limitOrderCount",label:"限制数量",align:"center",showOverflowTooltip:!0},{prop:"schoolCode",label:"渠道编码",align:"center",showOverflowTooltip:!0},{slot:"status",prop:"status",label:"状态",align:"center",showOverflowTooltip:!0,formatter:e=>0==e.status?"禁用":"启用"},{prop:"createdDate",label:"创建时间",align:"center",showOverflowTooltip:!0},{columnKey:"action",label:"操作",width:260,align:"center",resizable:!1,slot:"action",showOverflowTooltip:!0}],a={labelWidth:"100px",itemStyle:{padding:"4px"},colLayout:{span:6},formItems:[{field:"goodsNumber",type:"input",label:"商品编码",placeholder:"请输入销售品名称"},{field:"status",type:"select",label:"状态",placeholder:"请选择状态",options:[{title:"全部",value:""},{title:"禁用",value:0},{title:"启用",value:1}]},{field:"schoolCode",type:"input",label:"渠道编码",placeholder:"请输入渠道编码"}]}}}]);