"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9466,3527,3574,5106,3970,8008],{23527:function(t,e,n){n.r(e),n.d(e,{default:function(){return p}});var i=function(){var t=this,e=t._self._c;return e("el-card",{attrs:{shadow:"never",header:"进阶示例"}},[e("el-button",{attrs:{size:"small"},on:{click:t.printDataTable}},[t._v("打印数据表格")]),e("el-button",{attrs:{size:"small"},on:{click:t.printAnyTable}},[t._v("打印复杂表格")]),e("el-button",{attrs:{size:"small"},on:{click:t.printPdfUrl}},[t._v("打印 pdf")])],1)},l=[],a=n(60307),r={data(){return{users:[{key:1,username:"张小三",amount:18,province:"浙江",city:"杭州",zone:"西湖区",street:"西溪街道",address:"西溪花园30栋1单元"},{key:2,username:"李小四",amount:39,province:"江苏",city:"苏州",zone:"姑苏区",street:"丝绸路",address:"天墅之城9幢2单元"},{key:3,username:"王小五",amount:8,province:"江西",city:"南昌",zone:"青山湖区",street:"艾溪湖办事处",address:"中兴和园1幢3单元"},{key:4,username:"赵小六",amount:16,province:"福建",city:"泉州",zone:"丰泽区",street:"南洋街道",address:"南洋村6幢1单元"},{key:5,username:"孙小七",amount:12,province:"湖北",city:"武汉",zone:"武昌区",street:"武昌大道",address:"两湖花园16幢2单元"},{key:6,username:"周小八",amount:11,province:"安徽",city:"黄山",zone:"黄山区",street:"汤口镇",address:"温泉村21号"}]}},methods:{printDataTable(){const t=(0,a.uw)(this.users,[[{field:"username",width:150,rowspan:2,title:"联系人"},{align:"center",colspan:3,title:"地址"},{field:"amount",width:120,rowspan:2,title:"金额",align:"center"}],[{field:"province",width:120,title:"省"},{field:"city",width:120,title:"市"},{width:200,title:"区",templet:t=>`<span style="color: red;">${t.zone}</span>`}]]);(0,a.Un)({html:"<p>提供数据和cols配置自动生成复杂表格, 非常的方便</p>"+t,loading:!1})},printAnyTable(){const t='\n        <h2 style="text-align: center;color: #333;">XXXXX班级课程表</h2>\n        <table class="ele-printer-table">\n          <colgroup>\n            <col width="130px"/>\n          </colgroup>\n          <tr>\n            <th style="position: relative;">\n              <div style="position: absolute;right: 20px;top: 10px;line-height: normal;">星期</div>\n              <div style="position: absolute;left: 20px;bottom: 10px;line-height: normal;">时间</div>\n              <div\n                style="border-top: 1px solid #000; width:141px;height: 0;position: absolute;left: 0;top: 0;transform: rotate(22deg);transform-origin: 0 0;">\n              </div>\n            </th>\n            <th>周一</th>\n            <th>周二</th>\n            <th>周三</th>\n            <th>周四</th>\n            <th>周五</th>\n          </tr>\n          <tr>\n            <td>8:00-10:00</td>\n            <td>HTML5网页设计<br/>曲丽丽 - 441教室</td>\n            <td>数据库原理及应用<br/>严良 - 716机房</td>\n            <td>JavaSE初级程序设计<br/>肖萧 - 715机房</td>\n            <td></td>\n            <td>JavaScript程序设计<br/>董娜 - 733机房</td>\n          </tr>\n          <tr>\n            <td>10:30-12:30</td>\n            <td></td>\n            <td>JavaScript程序设计<br/>董娜 - 733机房</td>\n            <td></td>\n            <td>锋利的jQuery<br/>程咏 - 303教室</td>\n            <td>JavaEE应用开发<br/>周星 - 303教室</td>\n          </tr>\n          <tr>\n            <td colspan="6" style="height: auto;">午休</td>\n          </tr>\n          <tr>\n            <td>13:30-15:30</td>\n            <td>JavaSE初级程序设计<br/>肖萧 - 715机房</td>\n            <td></td>\n            <td>HTML5网页设计<br/>曲丽丽 - 441教室</td>\n            <td></td>\n            <td></td>\n          </tr>\n          <tr>\n            <td>16:00-18:00</td>\n            <td></td>\n            <td>JavaEE应用开发<br/>周星 - 303教室</td>\n            <td></td>\n            <td>数据库原理及应用<br/>严良 - 716机房</td>\n            <td></td>\n          </tr>\n        </table>\n        <style>\n          th, td {\n            text-align: center;\n            line-height: 35px;\n          }\n          td {\n            height: 100px;\n          }\n        </style>\n        ';(0,a.Un)({html:t,horizontal:!0,title:".",loading:!1})},printPdfUrl(){(0,a.e_)({url:"https://cdn.eleadmin.com/20200610/20200708224450.pdf"})}}},d=r,o=n(1001),s=(0,o.Z)(d,i,l,!1,null,null,null),p=s.exports},53574:function(t,e,n){n.r(e),n.d(e,{default:function(){return p}});var i=function(){var t=this,e=t._self._c;return e("el-card",{attrs:{shadow:"never",header:"打印指定区域"}},[e("div",{ref:"printRef",staticClass:"demo-print-group"},[e("div",{staticClass:"demo-print-div"},[e("div",[t._v("示例示例示例示例示例")]),e("div",{staticClass:"demo-hide-2",staticStyle:{"font-size":"12px","margin-top":"8px"}},[t._v(" 此段内容不打印 ")])]),e("div",{staticClass:"demo-print-right ele-bg-white"},[e("div",[e("el-tag",{attrs:{size:"mini"}},[t._v("示例")]),e("el-tag",{attrs:{size:"mini",type:"success"}},[t._v("示例")]),e("el-tag",{attrs:{size:"mini",type:"warning"}},[t._v("示例")])],1),e("div",{staticStyle:{"margin-top":"12px"}},[e("el-input",{model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)])]),e("div",{staticStyle:{"margin-top":"20px"}},[e("el-button",{attrs:{size:"small"},on:{click:t.print}},[t._v("打印")])],1)])},l=[],a=n(60307),r={data(){return{value:"示例示例示例"}},methods:{print(){(0,a.sn)(this.$refs.printRef,{hide:[".demo-hide-2"]})}}},d=r,o=n(1001),s=(0,o.Z)(d,i,l,!1,null,"2bb95c66",null),p=s.exports},75106:function(t,e,n){n.r(e),n.d(e,{default:function(){return p}});var i=function(){var t=this,e=t._self._c;return e("el-card",{attrs:{shadow:"never",header:"打印任意内容"}},[e("el-form",{attrs:{"label-width":"64px"}},[e("el-form-item",{attrs:{label:"loading"}},[e("el-radio-group",{model:{value:t.option.loading,callback:function(e){t.$set(t.option,"loading",e)},expression:"option.loading"}},[e("el-radio",{attrs:{label:!1}},[t._v("不显示")]),e("el-radio",{attrs:{label:!0}},[t._v("显示")])],1)],1)],1),e("el-button",{attrs:{size:"small"},on:{click:t.printAnyHtml}},[t._v("打印任意内容")]),e("el-button",{attrs:{size:"small"},on:{click:t.printAddHeader}},[t._v("设置页眉页脚")]),e("el-button",{attrs:{size:"small"},on:{click:t.printImage}},[t._v("打印图片")])],1)},l=[],a=n(60307),r={data(){return{option:{loading:!1}}},methods:{printAnyHtml(){(0,a.Un)({...this.option,html:['<h1 style="color: #1890ff;">EleAdmin 后台管理模板</h1>','<div style="color: #F51D2C;">通用型后台管理模板, 界面美观、开箱即用</div>'].join("")})},printAddHeader(){(0,a.Un)({...this.option,margin:0,html:['<div style="padding: 0 60px;">',Array(38).join("<h3>EleAdmin 后台管理模板</h3>"),"</div>"].join(""),header:'\n          <div style="display: flex;font-size: 12px;padding: 15px 30px 25px;">\n            <div>我是页眉左侧</div>\n            <div style="flex: 1;text-align: center;">我是页眉</div>\n            <div>我是页眉右侧</div>\n          </div>',footer:'\n          <div style="display: flex;font-size: 12px;padding: 15px 30px 25px;">\n            <div>我是页脚左侧</div>\n            <div style="flex: 1;text-align: center;">我是页脚</div>\n            <div>我是页脚右侧</div>\n          </div>',style:"<style> h3 {color: red;} </style>"})},printImage(){(0,a.Un)({...this.option,margin:0,html:'<img src="https://cdn.eleadmin.com/20200610/LrCTN2j94lo9N7wEql7cBr1Ux4rHMvmZ.jpg" style="width: 100%;"/>'})}}},d=r,o=n(1001),s=(0,o.Z)(d,i,l,!1,null,null,null),p=s.exports},93970:function(t,e,n){n.r(e),n.d(e,{default:function(){return p}});var i=function(){var t=this,e=t._self._c;return e("el-card",{attrs:{shadow:"never",header:"分页打印"}},[e("el-button",{attrs:{size:"small"},on:{click:t.printAnyPage}},[t._v("分页打印")]),e("el-button",{attrs:{size:"small"},on:{click:t.printPageAddHeader}},[t._v("设置页眉页脚")])],1)},l=[],a=n(60307),r={methods:{printAnyPage(){(0,a.ej)({pages:["<h3>我是第一页</h3>","<h3>我是第二页</h3>","<h3>我是第三页</h3>","<h3>我是第四页</h3>","<h3>我是第五页</h3>"],style:"<style> h3 { color: red; } </style>",loading:!1})},printPageAddHeader(){(0,a.ej)({pages:["<h3>我是第一页</h3>","<h3>我是第二页</h3>","<h3>我是第三页</h3>","<h3>我是第四页</h3>","<h3>我是第五页</h3>"],margin:0,padding:"20px 60px",header:'\n          <div style="display: flex;font-size: 12px;padding: 15px 30px;">\n            <div>我是页眉左侧</div>\n            <div style="flex: 1;text-align: center;">我是页眉</div>\n            <div>我是页眉右侧</div>\n          </div>',footer:'\n          <div style="display: flex;font-size: 12px;padding: 15px 30px;">\n            <div>我是页脚左侧</div>\n            <div style="flex: 1;text-align: center;">我是页脚</div>\n            <div>我是页脚右侧</div>\n          </div>',style:"<style> h3 { color: red; } </style>",loading:!1})}}},d=r,o=n(1001),s=(0,o.Z)(d,i,l,!1,null,null,null),p=s.exports},78008:function(t,e,n){n.r(e),n.d(e,{default:function(){return p}});var i=function(){var t=this,e=t._self._c;return e("el-card",{attrs:{shadow:"never",header:"打印当前页面"}},[e("el-form",{staticStyle:{"max-width":"320px"},attrs:{"label-width":"80px",size:"small"}},[e("el-form-item",{attrs:{label:"纸张方向"}},[e("el-select",{staticClass:"ele-block",attrs:{clearable:"",placeholder:"不设置"},model:{value:t.option.horizontal,callback:function(e){t.$set(t.option,"horizontal",e)},expression:"option.horizontal"}},[e("el-option",{attrs:{label:"横向",value:!0}}),e("el-option",{attrs:{label:"纵向",value:!1}})],1)],1),e("el-form-item",{attrs:{label:"页面间距"}},[e("el-select",{staticClass:"ele-block",attrs:{clearable:"",placeholder:"不设置"},model:{value:t.option.margin,callback:function(e){t.$set(t.option,"margin",e)},expression:"option.margin"}},[e("el-option",{attrs:{label:"0px",value:"0px"}}),e("el-option",{attrs:{label:"50px",value:"50px"}}),e("el-option",{attrs:{label:"100px",value:"100px"}})],1)],1),e("el-form-item",{attrs:{label:"页面标题"}},[e("el-input",{attrs:{clearable:"",placeholder:"不设置"},model:{value:t.option.title,callback:function(e){t.$set(t.option,"title",e)},expression:"option.title"}})],1)],1),e("el-button",{attrs:{size:"small"},on:{click:t.print}},[t._v("打印")]),e("el-button",{attrs:{size:"small"},on:{click:t.printHide}},[t._v("打印隐藏指定内容")]),e("div",{staticStyle:{"margin-top":"15px"}},[e("span",{staticClass:"ele-text-primary ele-printer-hide"},[t._v(" 此段内容在所有打印时隐藏, 打印完复原。 ")]),e("span",{staticClass:"ele-text-danger demo-hide-1"},[t._v(" 此段内容在指定打印时才隐藏。 ")])])],1)},l=[],a=n(60307),r={data(){return{option:{horizontal:null,margin:null,title:""}}},methods:{print(){(0,a.ox)(this.option1)},printHide(){(0,a.ox)({...this.option1,hide:[".demo-hide-1"]})}}},d=r,o=n(1001),s=(0,o.Z)(d,i,l,!1,null,null,null),p=s.exports},69466:function(t,e,n){n.r(e),n.d(e,{default:function(){return v}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ele-body"},[e("print-this"),e("print-div"),e("print-html"),e("print-page"),e("print-advanced")],1)},l=[],a=n(78008),r=n(53574),d=n(75106),o=n(93970),s=n(23527),p={name:"ExtensionPrinter",components:{PrintThis:a["default"],PrintDiv:r["default"],PrintHtml:d["default"],PrintPage:o["default"],PrintAdvanced:s["default"]}},c=p,h=n(1001),u=(0,h.Z)(c,i,l,!1,null,null,null),v=u.exports}}]);