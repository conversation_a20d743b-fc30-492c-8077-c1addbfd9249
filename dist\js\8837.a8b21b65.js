"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[8837,6425,7442,4169],{76425:function(e,t,i){i.r(t),i.d(t,{default:function(){return c}});var s=function(){var e=this,t=e._self._c;return t("ele-pro-table",{ref:"table",attrs:{columns:e.columns,datasource:e.datasource,selection:e.selection,"tool-class":"ele-toolbar-actions"},on:{"update:selection":function(t){e.selection=t}},scopedSlots:e._u([{key:"toolbar",fn:function(){return[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.read}},[e._v(" 标记已读 ")]),t("el-button",{attrs:{size:"small",type:"danger"},on:{click:e.removeBatch}},[e._v(" 删除消息 ")])]},proxy:!0},{key:"status",fn:function({row:i}){return[t("span",{class:["ele-text-danger","ele-text-info"][i.status]},[e._v(" "+e._s(["未读","已读"][i.status])+" ")])]}},{key:"action",fn:function({row:i}){return[t("el-link",{attrs:{type:"primary",underline:!1,icon:"el-icon-chat-dot-square"},on:{click:function(t){return e.view(i)}}},[e._v(" 回复 ")]),t("el-popconfirm",{staticClass:"ele-action",attrs:{title:"确定要删除此消息吗?"},on:{confirm:function(t){return e.remove(i)}},scopedSlots:e._u([{key:"reference",fn:function(){return[t("el-link",{attrs:{type:"danger",underline:!1,icon:"el-icon-delete"}},[e._v(" 删除 ")])]},proxy:!0}],null,!0)})]}}])})},n=[],l=i(68357),a={data(){return{columns:[{columnKey:"selection",type:"selection",width:45,align:"center",fixed:"left"},{columnKey:"index",type:"index",width:45,align:"center",showOverflowTooltip:!0,fixed:"left"},{prop:"title",label:"私信内容",showOverflowTooltip:!0,minWidth:110},{prop:"time",label:"发送时间",align:"center",showOverflowTooltip:!0,width:140},{prop:"status",label:"状态",align:"center",showOverflowTooltip:!0,width:80,slot:"status"},{columnKey:"action",label:"操作",align:"center",showOverflowTooltip:!0,width:140,resizable:!1,slot:"action"}],selection:[]}},methods:{datasource({page:e,limit:t,where:i,order:s}){return(0,l.g2)({...i,...s,page:e,limit:t})},reload(e){this.$refs.table.reload({page:1,where:e})},view(e){this.$message.info(e.title)},remove(e){console.log(e),this.$message.info("点击了删除"),this.updateUnReadNum()},removeBatch(){this.selection.length?(this.$message.info("点击了删除"),this.updateUnReadNum()):this.$message.error("请至少选择一条数据")},read(){this.selection.length?(this.selection.forEach((e=>{e.status=1})),this.updateUnReadNum()):this.$message.error("请至少选择一条数据")},updateUnReadNum(){this.$emit("update-data")}}},o=a,r=i(1001),u=(0,r.Z)(o,s,n,!1,null,null,null),c=u.exports},57442:function(e,t,i){i.r(t),i.d(t,{default:function(){return c}});var s=function(){var e=this,t=e._self._c;return t("ele-pro-table",{ref:"table",attrs:{columns:e.columns,datasource:e.datasource,selection:e.selection,"tool-class":"ele-toolbar-actions"},on:{"update:selection":function(t){e.selection=t}},scopedSlots:e._u([{key:"toolbar",fn:function(){return[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.read}},[e._v(" 批量确认 ")]),t("el-button",{attrs:{size:"small",type:"danger"},on:{click:e.removeBatch}},[e._v(" 删除通知 ")])]},proxy:!0},{key:"status",fn:function({row:i}){return[t("span",{class:["ele-text-danger","ele-text-info"][i.status]},[e._v(" "+e._s(["未确认","已确认"][i.status])+" ")])]}},{key:"action",fn:function({row:i}){return[t("el-link",{attrs:{type:"primary",underline:!1,icon:"el-icon-view"},on:{click:function(t){return e.view(i)}}},[e._v(" 确认 ")]),t("el-popconfirm",{staticClass:"ele-action",attrs:{title:"确定要删除此通知吗?"},on:{confirm:function(t){return e.remove(i)}},scopedSlots:e._u([{key:"reference",fn:function(){return[t("el-link",{attrs:{type:"danger",underline:!1,icon:"el-icon-delete"}},[e._v(" 删除 ")])]},proxy:!0}],null,!0)})]}}])})},n=[],l=i(68357),a={data(){return{columns:[{columnKey:"selection",type:"selection",width:45,align:"center",fixed:"left"},{columnKey:"index",type:"index",width:45,align:"center",showOverflowTooltip:!0,fixed:"left"},{prop:"title",label:"通知标题",showOverflowTooltip:!0,minWidth:110},{prop:"time",label:"通知时间",align:"center",showOverflowTooltip:!0,width:140},{prop:"status",label:"状态",align:"center",showOverflowTooltip:!0,width:80,slot:"status"},{columnKey:"action",label:"操作",align:"center",showOverflowTooltip:!0,width:140,resizable:!1,slot:"action"}],selection:[]}},methods:{datasource({page:e,limit:t,where:i,order:s}){return(0,l.S4)({...i,...s,page:e,limit:t})},reload(e){this.$refs.table.reload({page:1,where:e})},view(e){this.$message.info(e.title)},remove(e){console.log(e),this.$message.info("点击了删除"),this.updateUnReadNum()},removeBatch(){this.selection.length?(this.$message.info("点击了删除"),this.updateUnReadNum()):this.$message.error("请至少选择一条数据")},read(){this.selection.length?(this.selection.forEach((e=>{e.status=1})),this.updateUnReadNum()):this.$message.error("请至少选择一条数据")},updateUnReadNum(){this.$emit("update-data")}}},o=a,r=i(1001),u=(0,r.Z)(o,s,n,!1,null,null,null),c=u.exports},34169:function(e,t,i){i.r(t),i.d(t,{default:function(){return c}});var s=function(){var e=this,t=e._self._c;return t("ele-pro-table",{ref:"table",attrs:{columns:e.columns,datasource:e.datasource,selection:e.selection,"tool-class":"ele-toolbar-actions"},on:{"update:selection":function(t){e.selection=t}},scopedSlots:e._u([{key:"toolbar",fn:function(){return[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.read}},[e._v(" 批量完成 ")]),t("el-button",{attrs:{size:"small",type:"danger"},on:{click:e.removeBatch}},[e._v(" 删除待办 ")])]},proxy:!0},{key:"status",fn:function({row:i}){return[t("span",{class:["ele-text-danger","ele-text-info"][i.status]},[e._v(" "+e._s(["未完成","已完成"][i.status])+" ")])]}},{key:"action",fn:function({row:i}){return[t("el-link",{attrs:{type:"primary",underline:!1,icon:"el-icon-finished"},on:{click:function(t){return e.view(i)}}},[e._v(" 完成 ")]),t("el-popconfirm",{staticClass:"ele-action",attrs:{title:"确定要取消此待办吗?"},on:{confirm:function(t){return e.remove(i)}},scopedSlots:e._u([{key:"reference",fn:function(){return[t("el-link",{attrs:{type:"danger",underline:!1,icon:"el-icon-circle-close"}},[e._v(" 取消 ")])]},proxy:!0}],null,!0)})]}}])})},n=[],l=i(68357),a={data(){return{columns:[{columnKey:"selection",type:"selection",width:45,align:"center",fixed:"left"},{columnKey:"index",type:"index",width:45,align:"center",showOverflowTooltip:!0,fixed:"left"},{prop:"title",label:"待办内容",showOverflowTooltip:!0,minWidth:110},{prop:"time",label:"结束时间",align:"center",showOverflowTooltip:!0,width:140},{prop:"status",label:"状态",align:"center",showOverflowTooltip:!0,width:80,slot:"status"},{columnKey:"action",label:"操作",align:"center",showOverflowTooltip:!0,width:140,resizable:!1,slot:"action"}],selection:[]}},methods:{datasource({page:e,limit:t,where:i,order:s}){return(0,l.cR)({...i,...s,page:e,limit:t})},reload(e){this.$refs.table.reload({page:1,where:e})},view(e){this.$message.info(e.title)},remove(e){console.log(e),this.$message.info("点击了删除"),this.updateUnReadNum()},removeBatch(){this.selection.length?(this.$message.info("点击了删除"),this.updateUnReadNum()):this.$message.error("请至少选择一条数据")},read(){this.selection.length?(this.selection.forEach((e=>{e.status=1})),this.updateUnReadNum()):this.$message.error("请至少选择一条数据")},updateUnReadNum(){this.$emit("update-data")}}},o=a,r=i(1001),u=(0,r.Z)(o,s,n,!1,null,null,null),c=u.exports},28837:function(e,t,i){i.r(t),i.d(t,{default:function(){return p}});var s=function(){var e=this,t=e._self._c;return t("div",{class:["ele-body",{"demo-message-responsive":e.styleResponsive}]},[t("el-card",{attrs:{shadow:"never","body-style":"padding: 0;"}},[t("div",{staticClass:"ele-cell ele-cell-align-top ele-user-message"},[t("el-menu",{staticClass:"ele-scrollbar-hide",attrs:{mode:e.mode,"default-active":e.active}},[t("el-menu-item",{attrs:{index:"notice"}},[t("router-link",{attrs:{to:"/user/message?type=notice"}},[e.unReadNotice?t("el-badge",{staticClass:"ele-badge-static",attrs:{value:e.unReadNotice}}):e._e(),t("span",[e._v("系统通知")])],1)],1),t("el-menu-item",{attrs:{index:"letter"}},[t("router-link",{attrs:{to:"/user/message?type=letter"}},[e.unReadLetter?t("el-badge",{staticClass:"ele-badge-static",attrs:{value:e.unReadLetter}}):e._e(),t("span",[e._v("用户私信")])],1)],1),t("el-menu-item",{attrs:{index:"todo"}},[t("router-link",{attrs:{to:"/user/message?type=todo"}},[e.unReadTodo?t("el-badge",{staticClass:"ele-badge-static",attrs:{value:e.unReadTodo}}):e._e(),t("span",[e._v("代办事项")])],1)],1)],1),t("div",{staticClass:"ele-cell-content",staticStyle:{"overflow-x":"hidden"}},[t("transition",{attrs:{name:"slide-right",mode:"out-in"}},["notice"===e.active?t("message-notice",{on:{"update-data":e.queryUnReadNum}}):"letter"===e.active?t("message-letter",{on:{"update-data":e.queryUnReadNum}}):t("message-todo",{on:{"update-data":e.queryUnReadNum}})],1)],1)],1)])],1)},n=[],l=i(57442),a=i(76425),o=i(34169),r=i(68357),u={name:"UserMessage",components:{MessageNotice:l["default"],MessageLetter:a["default"],MessageTodo:o["default"]},data(){return{active:null,unReadNotice:0,unReadLetter:0,unReadTodo:0}},computed:{mode(){return this.styleResponsive&&this.$store.state.theme.screenWidth<768?"horizontal":"vertical"},styleResponsive(){return this.$store.state.theme.styleResponsive}},watch:{$route:{handler(e){var t;"/user/message"===e.path&&(this.active=(null===e||void 0===e||null===(t=e.query)||void 0===t?void 0:t.type)||"notice")},immediate:!0}},created(){this.queryUnReadNum()},methods:{queryUnReadNum(){(0,r.Nt)().then((e=>{this.unReadNotice=e.notice,this.unReadLetter=e.letter,this.unReadTodo=e.todo})).catch((e=>{this.$message.error(e.message)}))}}},c=u,d=i(1001),m=(0,d.Z)(c,s,n,!1,null,"25e26f28",null),p=m.exports},68357:function(e,t,i){async function s(){return{count:10,list:[{id:21,title:"EleAdmin新版本发布，欢迎体验",time:"2020-07-24 11:35",status:0},{id:22,title:"EleAdmin新版本发布，欢迎体验",time:"2020-07-24 11:35",status:0},{id:23,title:"EleAdmin新版本发布，欢迎体验",time:"2020-07-24 11:35",status:1},{id:24,title:"EleAdmin新版本发布，欢迎体验",time:"2020-07-24 11:35",status:1},{id:25,title:"EleAdmin新版本发布，欢迎体验",time:"2020-07-24 11:35",status:1},{id:26,title:"EleAdmin新版本发布，欢迎体验",time:"2020-07-24 11:35",status:1},{id:27,title:"EleAdmin新版本发布，欢迎体验",time:"2020-07-24 11:35",status:1},{id:28,title:"EleAdmin新版本发布，欢迎体验",time:"2020-07-24 11:35",status:1},{id:29,title:"EleAdmin新版本发布，欢迎体验",time:"2020-07-24 11:35",status:1},{id:30,title:"EleAdmin新版本发布，欢迎体验",time:"2020-07-24 11:35",status:1}]}}async function n(){return{count:10,list:[{id:11,title:"Jasmine给你发来了一条私信",time:"2020-07-24 11:35",status:0},{id:12,title:"Jasmine给你发来了一条私信",time:"2020-07-24 11:35",status:0},{id:13,title:"Jasmine给你发来了一条私信",time:"2020-07-24 11:35",status:0},{id:14,title:"Jasmine给你发来了一条私信",time:"2020-07-24 11:35",status:1},{id:15,title:"Jasmine给你发来了一条私信",time:"2020-07-24 11:35",status:1},{id:16,title:"Jasmine给你发来了一条私信",time:"2020-07-24 11:35",status:1},{id:17,title:"Jasmine给你发来了一条私信",time:"2020-07-24 11:35",status:1},{id:18,title:"Jasmine给你发来了一条私信",time:"2020-07-24 11:35",status:1},{id:19,title:"Jasmine给你发来了一条私信",time:"2020-07-24 11:35",status:1},{id:20,title:"Jasmine给你发来了一条私信",time:"2020-07-24 11:35",status:1}]}}async function l(){return{count:10,list:[{id:1,title:"你有两条任务待完成，不要忘了哦~",time:"2020-07-24 11:35",status:0},{id:2,title:"你有两条任务待完成，不要忘了哦~",time:"2020-07-24 11:35",status:0},{id:3,title:"你有两条任务待完成，不要忘了哦~",time:"2020-07-24 11:35",status:0},{id:4,title:"你有两条任务待完成，不要忘了哦~",time:"2020-07-24 11:35",status:0},{id:5,title:"你有两条任务待完成，不要忘了哦~",time:"2020-07-24 11:35",status:1},{id:6,title:"你有两条任务待完成，不要忘了哦~",time:"2020-07-24 11:35",status:1},{id:7,title:"你有两条任务待完成，不要忘了哦~",time:"2020-07-24 11:35",status:1},{id:8,title:"你有两条任务待完成，不要忘了哦~",time:"2020-07-24 11:35",status:1},{id:9,title:"你有两条任务待完成，不要忘了哦~",time:"2020-07-24 11:35",status:1},{id:10,title:"你有两条任务待完成，不要忘了哦~",time:"2020-07-24 11:35",status:1}]}}async function a(){return{notice:2,letter:3,todo:4}}i.d(t,{Nt:function(){return a},S4:function(){return s},cR:function(){return l},g2:function(){return n}})}}]);