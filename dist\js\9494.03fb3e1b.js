"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9494,2462,6151,8243,2335,2458],{56151:function(e,t,a){a.r(t),a.d(t,{default:function(){return c}});a(74916);var l=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{"max-width":"200px"}},[t("el-input",{attrs:{clearable:"",size:"small",placeholder:"输入关键字搜索","prefix-icon":"el-icon-search"},on:{change:e.search},model:{value:e.where.keywords,callback:function(t){e.$set(e.where,"keywords",t)},expression:"where.keywords"}})],1)},o=[],r={data(){return{where:{keywords:""}}},methods:{search(){this.$emit("search",this.where)}}},s=r,n=a(1001),i=(0,n.Z)(s,l,o,!1,null,null,null),c=i.exports},32462:function(e,t,a){a.r(t),a.d(t,{default:function(){return u}});a(74916);var l=function(){var e=this,t=e._self._c;return t("el-card",{attrs:{shadow:"never",header:"可搜索"}},[t("div",{staticStyle:{"max-width":"260px"}},[t("ele-table-select",{ref:"select",attrs:{multiple:!0,clearable:!0,placeholder:"请选择","value-key":"userId","label-key":"nickname","table-config":e.tableConfig,"popper-width":580,"init-value":e.initValue},scopedSlots:e._u([{key:"roles",fn:function({row:a}){return e._l(a.roles,(function(a){return t("el-tag",{key:a.roleId,attrs:{size:"mini",type:"primary","disable-transitions":!0}},[e._v(" "+e._s(a.roleName)+" ")])}))}},{key:"toolbar",fn:function(){return[t("demo-advanced-search",{on:{search:e.search}})]},proxy:!0}]),model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1),t("div",{staticStyle:{"margin-top":"12px"}},[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.setInitValue}},[e._v(" 回显数据 ")])],1)])},o=[],r=a(59477),s=a(56151),n={components:{DemoAdvancedSearch:s["default"]},data(){return{value:[],tableConfig:{datasource({page:e,limit:t,where:a,order:l}){return(0,r.I0)({...a,...l,page:e,limit:t})},columns:[{columnKey:"selection",type:"selection",width:45,align:"center",reserveSelection:!0},{columnKey:"index",type:"index",width:45,align:"center",showOverflowTooltip:!0},{prop:"username",label:"用户账号",sortable:"custom",showOverflowTooltip:!0,minWidth:110},{prop:"nickname",label:"用户名",sortable:"custom",showOverflowTooltip:!0,minWidth:110},{prop:"sexName",label:"性别",sortable:"custom",showOverflowTooltip:!0,minWidth:80},{columnKey:"roles",label:"角色",showOverflowTooltip:!0,minWidth:110,slot:"roles"}],pageSize:5,pageSizes:[5,10,15,20],rowClickChecked:!0,rowClickCheckedIntelligent:!1,toolkit:["reload","columns"],size:"small",toolStyle:{padding:"0 10px"}},initValue:void 0}},methods:{setInitValue(){this.initValue=[{userId:3,nickname:"管理员"},{userId:5,nickname:"用户四"},{userId:6,nickname:"用户五"}]},search(e){this.$refs.select.reload({where:e,page:1})}}},i=n,c=a(1001),d=(0,c.Z)(i,l,o,!1,null,null,null),u=d.exports},62335:function(e,t,a){a.r(t),a.d(t,{default:function(){return d}});var l=function(){var e=this,t=e._self._c;return t("el-card",{attrs:{shadow:"never",header:"表格后端分页"}},[t("div",{staticStyle:{"max-width":"260px"}},[t("ele-table-select",{attrs:{clearable:!0,placeholder:"请选择",disabled:e.disabled,"value-key":"userId","label-key":"nickname","table-config":e.tableConfig,"popper-width":580,"init-value":e.initValue},on:{select:e.onSelect},scopedSlots:e._u([{key:"roles",fn:function({row:a}){return e._l(a.roles,(function(a){return t("el-tag",{key:a.roleId,attrs:{size:"mini",type:"primary","disable-transitions":!0}},[e._v(" "+e._s(a.roleName)+" ")])}))}}]),model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1),t("div",{staticClass:"ele-cell",staticStyle:{"margin-top":"15px"}},[t("div",{staticStyle:{"line-height":"22px"}},[e._v(" 禁用：")]),t("div",{staticClass:"ele-cell-content"},[t("el-radio-group",{model:{value:e.disabled,callback:function(t){e.disabled=t},expression:"disabled"}},[t("el-radio",{attrs:{label:!1}},[e._v("否")]),t("el-radio",{attrs:{label:!0}},[e._v("是")])],1)],1)]),t("div",{staticStyle:{"margin-top":"12px"}},[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.setInitValue}},[e._v(" 回显数据 ")])],1)])},o=[],r=a(59477),s={data(){return{value:void 0,tableConfig:{datasource({page:e,limit:t,where:a,order:l}){return(0,r.I0)({...a,...l,page:e,limit:t})},columns:[{columnKey:"index",type:"index",width:45,align:"center",showOverflowTooltip:!0,fixed:"left"},{prop:"username",label:"用户账号",sortable:"custom",showOverflowTooltip:!0,minWidth:110},{prop:"nickname",label:"用户名",sortable:"custom",showOverflowTooltip:!0,minWidth:110},{prop:"sexName",label:"性别",sortable:"custom",showOverflowTooltip:!0,minWidth:80},{columnKey:"roles",label:"角色",showOverflowTooltip:!0,minWidth:110,slot:"roles"}],toolbar:!1,pageSize:5,pageSizes:[5,10,15,20],size:"small"},disabled:!1,initValue:void 0}},methods:{setInitValue(){this.initValue={userId:1,nickname:"管理员"}},onSelect(e){console.log("item:",e)}}},n=s,i=a(1001),c=(0,i.Z)(n,l,o,!1,null,null,null),d=c.exports},18243:function(e,t,a){a.r(t),a.d(t,{default:function(){return d}});var l=function(){var e=this,t=e._self._c;return t("el-card",{attrs:{shadow:"never",header:"基础用法"}},[t("div",{staticStyle:{"max-width":"260px"}},[t("ele-table-select",{attrs:{clearable:!0,placeholder:"请选择","value-key":"userId","label-key":"nickname","table-config":e.tableConfig,"popper-width":580},scopedSlots:e._u([{key:"roles",fn:function({row:a}){return e._l(a.roles,(function(a){return t("el-tag",{key:a.roleId,attrs:{size:"mini",type:"primary","disable-transitions":!0}},[e._v(" "+e._s(a.roleName)+" ")])}))}}]),model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1),t("div",{staticStyle:{"margin-top":"12px"}},[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.setInitValue}},[e._v(" 回显数据 ")])],1)])},o=[],r=a(59477),s={data(){return{value:void 0,tableConfig:{datasource:[],columns:[{columnKey:"index",type:"index",width:45,align:"center",showOverflowTooltip:!0,fixed:"left"},{prop:"username",label:"用户账号",sortable:"custom",showOverflowTooltip:!0,minWidth:110},{prop:"nickname",label:"用户名",sortable:"custom",showOverflowTooltip:!0,minWidth:110},{prop:"sexName",label:"性别",sortable:"custom",showOverflowTooltip:!0,minWidth:80},{columnKey:"roles",label:"角色",showOverflowTooltip:!0,minWidth:110,slot:"roles"}],toolbar:!1,pageSize:5,pageSizes:[5,10,15,20],size:"small"}}},methods:{setInitValue(){this.value=1}},created(){(0,r.yw)().then((e=>{this.tableConfig.datasource=e}))}},n=s,i=a(1001),c=(0,i.Z)(n,l,o,!1,null,null,null),d=c.exports},62458:function(e,t,a){a.r(t),a.d(t,{default:function(){return d}});var l=function(){var e=this,t=e._self._c;return t("el-card",{attrs:{shadow:"never",header:"多选"}},[t("div",{staticStyle:{"max-width":"260px"}},[t("ele-table-select",{attrs:{multiple:!0,clearable:!0,placeholder:"请选择",disabled:e.disabled,"value-key":"userId","label-key":"nickname","table-config":e.tableConfig,"popper-width":580},scopedSlots:e._u([{key:"roles",fn:function({row:a}){return e._l(a.roles,(function(a){return t("el-tag",{key:a.roleId,attrs:{size:"mini",type:"primary","disable-transitions":!0}},[e._v(" "+e._s(a.roleName)+" ")])}))}}]),model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1),t("div",{staticClass:"ele-cell",staticStyle:{"margin-top":"15px"}},[t("div",{staticStyle:{"line-height":"22px"}},[e._v(" 禁用：")]),t("div",{staticClass:"ele-cell-content"},[t("el-radio-group",{model:{value:e.disabled,callback:function(t){e.disabled=t},expression:"disabled"}},[t("el-radio",{attrs:{label:!1}},[e._v("否")]),t("el-radio",{attrs:{label:!0}},[e._v("是")])],1)],1)]),t("div",{staticStyle:{"margin-top":"12px"}},[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.setInitValue}},[e._v(" 回显数据 ")])],1)])},o=[],r=a(59477),s={data(){return{value:void 0,tableConfig:{datasource:[],columns:[{columnKey:"selection",type:"selection",width:45,align:"center",reserveSelection:!0},{columnKey:"index",type:"index",width:45,align:"center",showOverflowTooltip:!0},{prop:"username",label:"用户账号",sortable:"custom",showOverflowTooltip:!0,minWidth:110},{prop:"nickname",label:"用户名",sortable:"custom",showOverflowTooltip:!0,minWidth:110},{prop:"sexName",label:"性别",sortable:"custom",showOverflowTooltip:!0,minWidth:80},{columnKey:"roles",label:"角色",showOverflowTooltip:!0,minWidth:110,slot:"roles"}],toolbar:!1,pageSize:5,pageSizes:[5,10,15,20],rowClickChecked:!0,rowClickCheckedIntelligent:!1,size:"small"},disabled:!1}},methods:{setInitValue(){this.value=[3,5,6]}},created(){(0,r.yw)().then((e=>{this.tableConfig.datasource=e}))}},n=s,i=a(1001),c=(0,i.Z)(n,l,o,!1,null,null,null),d=c.exports},99494:function(e,t,a){a.r(t),a.d(t,{default:function(){return p}});var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("demo-basic"),t("demo-basic-page"),t("demo-multiple"),t("demo-advanced")],1)},o=[],r=a(18243),s=a(62335),n=a(62458),i=a(32462),c={name:"ExtensionTableSelect",components:{DemoBasic:r["default"],DemoBasicPage:s["default"],DemoMultiple:n["default"],DemoAdvanced:i["default"]}},d=c,u=a(1001),m=(0,u.Z)(d,l,o,!1,null,null,null),p=m.exports},59477:function(e,t,a){a.d(t,{I0:function(){return o},Nq:function(){return i},OL:function(){return u},PR:function(){return s},Zy:function(){return m},bz:function(){return d},cn:function(){return n},kX:function(){return c},mL:function(){return f},we:function(){return p},yw:function(){return r}});a(21703);var l=a(18684);async function o(e){const t=await l.Z.post("/system/user/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await l.Z.get("/system/user/list",{params:e});return 0===t.data.code&&t.data.data?t.data.data:Promise.reject(new Error(t.data.message))}async function s(e){const t=await l.Z.get("/system/user/"+e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function n(e){const t=await l.Z.post("/system/user/add",e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function i(e){const t=await l.Z.post("/system/user/update",e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function c(e){const t=await l.Z.post("/system/user/"+e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function d(e){const t=await l.Z.post("/system/user/batch",{data:e});return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function u(e){const t=await l.Z.post("/system/user/status",e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function m(e,t="123456"){const a=await l.Z.post("/system/user/password",{userId:e,password:t});return 0===a.data.code?a.data.message:Promise.reject(new Error(a.data.message))}async function p(e){const t=new FormData;t.append("file",e);const a=await l.Z.post("/system/user/import",t);return 0===a.data.code?a.data.message:Promise.reject(new Error(a.data.message))}async function f(e,t,a){const o=await l.Z.get("/system/user/existence",{params:{field:e,value:t,id:a}});return 0===o.data.code?o.data.message:Promise.reject(new Error(o.data.message))}}}]);