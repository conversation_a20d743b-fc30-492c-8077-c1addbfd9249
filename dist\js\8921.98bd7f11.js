"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[8921,2145],{74952:function(e,t,l){l.d(t,{Z:function(){return h}});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-search"},[t("hl-form",e._b({scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"handle-btns"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQueryClick}},[e._v("搜索")]),t("el-button",{on:{click:e.handleResetClick}},[e._v("重置")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:"formData"}},"hl-form",e.searchFormConfig,!1))],1)},o=[],r=(l(33948),function(){var e=this,t=e._self._c;return t("div",{staticClass:"hl-form"},[t("div",{staticClass:"header"},[e._t("header")],2),t("el-form",{attrs:{"label-width":e.labelWidth,model:e.formData,rules:e.rules}},[t("el-row",[e._l(e.formItems,(function(l){return[t("el-col",e._b({key:l.label},"el-col",e.colLayout,!1),[l.isHidden?e._e():t("el-form-item",{style:e.itemStyle,attrs:{label:l.label,rules:l.rules}},["input"===l.type||"password"===l.type?[t("el-input",e._b({attrs:{placeholder:l.placeholder,"show-password":"password"===l.type},model:{value:e.formData[`${l.field}`],callback:function(t){e.$set(e.formData,`${l.field}`,t)},expression:"formData[`${item.field}`]"}},"el-input",l.otherOptions,!1))]:"select"===l.type?[t("el-select",e._b({staticStyle:{width:"100%"},attrs:{placeholder:l.placeholder},model:{value:e.formData[`${l.field}`],callback:function(t){e.$set(e.formData,`${l.field}`,t)},expression:"formData[`${item.field}`]"}},"el-select",l.otherOptions,!1),e._l(l.options,(function(a){var o,r,i,n;return t("el-option",{key:null!==(o=a[l.optionValue])&&void 0!==o?o:a.value,attrs:{value:null!==(r=a[l.optionValue])&&void 0!==r?r:a.value,label:null!==(i=a[l.optionLabel])&&void 0!==i?i:a.title}},[e._v(e._s(null!==(n=a[l.optionLabel])&&void 0!==n?n:a.title))])})),1)]:"datepicker"===l.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},model:{value:e.formData[`${l.field}`],callback:function(t){e.$set(e.formData,`${l.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",l.otherOptions,!1))]:"twoDatePicker"===l.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.formData[`${l.field}`],callback:function(t){e.$set(e.formData,`${l.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",l.otherOptions,!1))]:"section"===l.type?[t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:l.placeholder},model:{value:e.formData[`${l.field[0]}`],callback:function(t){e.$set(e.formData,`${l.field[0]}`,t)},expression:"formData[`${item.field[0]}`]"}},"el-input",l.otherOptions,!1))],1),t("el-col",{staticClass:"line",attrs:{span:2}},[e._v("-")]),t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:l.placeholder},model:{value:e.formData[`${l.field[1]}`],callback:function(t){e.$set(e.formData,`${l.field[1]}`,t)},expression:"formData[`${item.field[1]}`]"}},"el-input",l.otherOptions,!1))],1)]:e._e()],2)],1)]})),e._t("footer")],2)],1)],1)}),i=[],n={props:{value:{type:Object,required:!0},formItems:{type:Array,default:()=>[]},rules:{type:Object,default:()=>{}},labelWidth:{type:String,default:"100px"},itemStyle:{type:Object,default:()=>({padding:"10px 40px"})},colLayout:{type:Object,default:()=>({xl:6,lg:8,md:12,sm:24,xs:24})}},data(){return{formData:{...this.value},pickerOptions:{shortcuts:[{text:"最近一周",onClick(e){const t=new Date,l=new Date;l.setTime(l.getTime()-6048e5),e.$emit("pick",[l,t])}},{text:"最近一个月",onClick(e){const t=new Date,l=new Date;l.setTime(l.getTime()-2592e6),e.$emit("pick",[l,t])}},{text:"最近三个月",onClick(e){const t=new Date,l=new Date;l.setTime(l.getTime()-7776e6),e.$emit("pick",[l,t])}}]}}},watch:{formData:{handler(e){this.$emit("input",e)},deep:!0}}},s=n,c=l(1001),d=(0,c.Z)(s,r,i,!1,null,"3585fc65",null),p=d.exports,u={components:{HlForm:p},props:{searchFormConfig:{type:Object,reuqired:!0}},data(){return{formOriginData:{},formData:{}}},created(){var e,t;const l=null!==(e=null===(t=this.searchFormConfig)||void 0===t?void 0:t.formItems)&&void 0!==e?e:[];for(const a of l)Array.isArray(a.field)?a.field.forEach((e=>{this.formOriginData[e]=""})):this.formOriginData[a.field]="";this.formData=this.formOriginData},methods:{handleResetClick(){for(const e in this.formOriginData)this.formData[`${e}`]=this.formOriginData[e];this.$emit("resetBtnClick")},handleQueryClick(){const e={...this.formData};for(const t in e)""===e[t]&&delete e[t];this.$emit("queryBtnClick",e)}}},f=u,m=(0,c.Z)(f,a,o,!1,null,"a0a65568",null),h=m.exports},68921:function(e,t,l){l.r(t),l.d(t,{default:function(){return u}});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("el-card",{attrs:{shadow:"never"}},[t("pageSearch",{attrs:{searchFormConfig:e.searchFormConfig},on:{queryBtnClick:e.reload}}),t("ele-pro-table",{ref:"table",attrs:{columns:e.columns,datasource:e.datasource,"cache-key":"playeCityModuleTable"},scopedSlots:e._u([{key:"toolbar",fn:function(){return[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.deriveClick}},[e._v(" 导出 ")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.deriveAllClick}},[e._v(" 全部导出 ")])]},proxy:!0}])})],1),t("arpproverDialog",{attrs:{visible:e.isShowApproverDialog},on:{"update:visible":function(t){e.isShowApproverDialog=t},select:e.handleApproverSelect}})],1)},o=[],r=l(23742),i=(l(40846),l(42145)),n=l(74952),s=(l(84722),{components:{pageSearch:n.Z},data(){return{columns:i.columns,searchFormConfig:i.searchFormConfig,cardRef:null,isShowApproverDialog:!1,downloadType:1}},methods:{async deriveClick(){this.isShowApproverDialog=!0,this.downloadType=1},async deriveAllClick(){this.isShowApproverDialog=!0,this.downloadType=2},async handleApproverSelect(e){if(1==this.downloadType){const t={status:"",appSettingId:e};await(0,r.d)(t)}else{const t={status:"",appSettingId:e,queryType:"ALL"};await(0,r.d)(t)}},datasource({page:e,limit:t,where:l,order:a}){return(0,r.o)({page:e,limit:t,...l,...a})},reload(e){this.$refs.table.reload({page:1,where:e})}}}),c=s,d=l(1001),p=(0,d.Z)(c,a,o,!1,null,"7de00e03",null),u=p.exports},23742:function(e,t,l){l.d(t,{d:function(){return r},o:function(){return o}});l(21703);var a=l(18684);async function o(e){const t=await a.Z.post("/hnzhsl/hnslSendOrders/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await a.Z.post("/hnzhsl/hnslSendOrders/outputListTable",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}},40846:function(e,t,l){l.d(t,{JV:function(){return o},cn:function(){return r},km:function(){return i}});var a=l(18816);const o=`${a.CT}/download/exportDaoUsers`,r=`${a.CT}/download/template`,i=`${a.CT}/download/downloadFile`;a.CT,a.CT},84722:function(e,t,l){l.d(t,{L:function(){return a}});l(33948),l(60285),l(41637),l(74916),l(15306);function a(e){if(e)try{const l=new Blob([e.data]),a=window.URL.createObjectURL(l);let o="download";const r=e.headers["content-disposition"];if(r){const e=/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,l=e.exec(r);if(null!=l&&l[1]){o=l[1].replace(/['"]/g,"");try{o=decodeURIComponent(o)}catch(t){console.warn("文件名解码失败，使用原始文件名",t)}}}const i=document.createElement("a");return i.href=a,i.setAttribute("download",o),document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(a),document.body.removeChild(i),!0}catch(l){return console.error("下载文件时发生错误:",l),!1}else console.error("下载失败：响应对象为空")}},42145:function(e,t,l){l.r(t),l.d(t,{columns:function(){return a},searchFormConfig:function(){return o}});const a=[{width:45,type:"selection",columnKey:"expand",align:"center",slot:"expand"},{prop:"ORDERS_NAME",label:"活动名称",align:"center",showOverflowTooltip:!0},{prop:"cityCode",label:"地市",align:"center",showOverflowTooltip:!0},{prop:"SCHOOL_NAME",label:"学校",align:"center",showOverflowTooltip:!0},{prop:"ORDERS_DATE",label:"账期",align:"center",showOverflowTooltip:!0}],o={labelWidth:"100px",itemStyle:{padding:"4px"},colLayout:{span:6},formItems:[{field:"ordersName",type:"input",label:"活动名称",placeholder:"请输入活动名称"},{field:"cityCode",type:"select",label:"地市",placeholder:"请选择地市",options:[{value:"731",title:"长沙市"},{value:"733",title:"株洲市"},{value:"732",title:"湘潭市"},{value:"734",title:"衡阳市"},{value:"739",title:"邵阳市"},{value:"730",title:"岳阳市"},{value:"736",title:"常德市"},{value:"744",title:"张家界市"},{value:"737",title:"益阳市"},{value:"735",title:"郴州市"},{value:"746",title:"永州市"},{value:"745",title:"怀化市"},{value:"738",title:"娄底市"},{value:"743",title:"湘西市"},{value:"700",title:"全省"}]},{field:"schoolName",type:"input",label:"学校",placeholder:"请输入学校名称"},{field:"ordersDate",type:"datepicker",label:"账期",otherOptions:{placeholder:"请选择创建时间","value-format":"yyyy-MM-dd"}}]}}}]);