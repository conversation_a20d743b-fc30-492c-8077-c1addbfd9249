"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9464],{9464:function(e,a,t){t.r(a),t.d(a,{default:function(){return l}});var n=function(){var e=this,a=e._self._c;return a("div",{staticClass:"ele-body"},[a("el-card",{staticStyle:{"margin-bottom":"15px"},attrs:{shadow:"never","body-style":"padding: 40px 15px;"}},[a("div",{staticClass:"top-search-group"},[a("el-input",{attrs:{placeholder:"请输入内容"},model:{value:e.keyword,callback:function(a){e.keyword=a},expression:"keyword"}}),a("el-button",{attrs:{type:"primary"}},[e._v("搜索")])],1)]),a("div",[a("el-row",{attrs:{gutter:15}},e._l(e.data,(function(t,n){return a("el-col",e._b({key:n},"el-col",e.styleResponsive?{lg:6,md:8,sm:12}:{span:6},!1),[a("el-card",{staticClass:"project-list-item",attrs:{shadow:"hover"}},[a("div",{staticClass:"project-list-cover"},[a("img",{attrs:{src:t.cover,alt:""}})]),a("div",{staticClass:"project-list-body"},[a("h6",{staticClass:"ele-elip"},[a("b",[e._v(e._s(t.title))])]),a("div",{staticClass:"project-list-desc"},[e._v(e._s(t.content))]),a("div",{staticClass:"project-list-time"},[a("div",{staticClass:"project-list-time-text"},[e._v(e._s(t.time))]),a("ele-avatar-list",{attrs:{data:t.users,size:22}})],1)])])],1)})),1),a("el-pagination",{staticClass:"ele-pagination-circle",attrs:{"current-page":e.page.page,"page-size":e.page.limit,total:e.count,background:!0,layout:"prev, pager, next","pager-count":5},on:{"size-change":a=>(e.page.limit=a)&&e.query(),"current-change":a=>(e.page.page=a)&&e.query()}})],1)],1)},c=[],s={name:"ListCardProject",data(){return{keyword:"",page:{page:1,limit:8},count:40,data:[{title:"ElementUI",content:"Element，一套为开发者、设计师和产品经理准备的基于 Vue 2.0 的组件库，提供了配套设计资源，帮助你的网站快速成型。",time:"2 小时前",cover:"https://cdn.eleadmin.com/20200610/RZ8FQmZfHkcffMlTBCJllBFjEhEsObVo.jpg",users:[{name:"SunSmile",avatar:"https://cdn.eleadmin.com/20200609/c184eef391ae48dba87e3057e70238fb.jpg"},{name:"酷酷的大叔",avatar:"https://cdn.eleadmin.com/20200609/2d98970a51b34b6b859339c96b240dcd.jpg"},{name:"Jasmine",avatar:"https://cdn.eleadmin.com/20200609/948344a2a77c47a7a7b332fe12ff749a.jpg"}]},{title:"Vue.js",content:"Vue 是一套用于构建用户界面的渐进式框架。与其它大型框架不同的是，Vue 被设计为可以自底向上逐层应用。",time:"4 小时前",cover:"https://cdn.eleadmin.com/20200610/WLXm7gp1EbLDtvVQgkeQeyq5OtDm00Jd.jpg",users:[{name:"SunSmile",avatar:"https://cdn.eleadmin.com/20200609/c184eef391ae48dba87e3057e70238fb.jpg"},{name:"酷酷的大叔",avatar:"https://cdn.eleadmin.com/20200609/2d98970a51b34b6b859339c96b240dcd.jpg"},{name:"Jasmine",avatar:"https://cdn.eleadmin.com/20200609/948344a2a77c47a7a7b332fe12ff749a.jpg"}]},{title:"Vuex",content:"Vuex 是一个专为 Vue.js 应用程序开发的状态管理模式。它采用集中式存储管理应用的所有组件的状态，并以相应的规则保证状态以一种可预测的方式发生变化。",time:"12 小时前",cover:"https://cdn.eleadmin.com/20200610/4Z0QR2L0J1XStxBh99jVJ8qLfsGsOgjU.jpg",users:[{name:"SunSmile",avatar:"https://cdn.eleadmin.com/20200609/c184eef391ae48dba87e3057e70238fb.jpg"},{name:"酷酷的大叔",avatar:"https://cdn.eleadmin.com/20200609/2d98970a51b34b6b859339c96b240dcd.jpg"},{name:"Jasmine",avatar:"https://cdn.eleadmin.com/20200609/948344a2a77c47a7a7b332fe12ff749a.jpg"}]},{title:"Vue Router",content:"Vue Router 是 Vue.js 官方的路由管理器。它和 Vue.js 的核心深度集成，让构建单页面应用变得易如反掌。",time:"14 小时前",cover:"https://cdn.eleadmin.com/20200610/ttkIjNPlVDuv4lUTvRX8GIlM2QqSe0jg.jpg",users:[{name:"SunSmile",avatar:"https://cdn.eleadmin.com/20200609/c184eef391ae48dba87e3057e70238fb.jpg"},{name:"酷酷的大叔",avatar:"https://cdn.eleadmin.com/20200609/2d98970a51b34b6b859339c96b240dcd.jpg"},{name:"Jasmine",avatar:"https://cdn.eleadmin.com/20200609/948344a2a77c47a7a7b332fe12ff749a.jpg"}]},{title:"Sass",content:"Sass 是世界上最成熟、稳定、强大的专业级 CSS 扩展语言。",time:"10 小时前",cover:"https://cdn.eleadmin.com/20200610/fAenQ8nvRjL7x0i0jEfuDBZHvJfHf3v6.jpg",users:[{name:"SunSmile",avatar:"https://cdn.eleadmin.com/20200609/c184eef391ae48dba87e3057e70238fb.jpg"},{name:"酷酷的大叔",avatar:"https://cdn.eleadmin.com/20200609/2d98970a51b34b6b859339c96b240dcd.jpg"},{name:"Jasmine",avatar:"https://cdn.eleadmin.com/20200609/948344a2a77c47a7a7b332fe12ff749a.jpg"}]},{title:"Axios",content:"Axios 是一个基于 promise 的 HTTP 库，可以用在浏览器和 node.js 中。",time:"16 小时前",cover:"https://cdn.eleadmin.com/20200610/LrCTN2j94lo9N7wEql7cBr1Ux4rHMvmZ.jpg",users:[{name:"SunSmile",avatar:"https://cdn.eleadmin.com/20200609/c184eef391ae48dba87e3057e70238fb.jpg"},{name:"酷酷的大叔",avatar:"https://cdn.eleadmin.com/20200609/2d98970a51b34b6b859339c96b240dcd.jpg"},{name:"Jasmine",avatar:"https://cdn.eleadmin.com/20200609/948344a2a77c47a7a7b332fe12ff749a.jpg"}]},{title:"Webpack",content:"webpack 是一个模块打包器。webpack 的主要目标是将 JavaScript 文件打包在一起，打包后的文件用于在浏览器中使用。",time:"6 小时前",cover:"https://cdn.eleadmin.com/20200610/yeKvhT20lMU0f1T3Y743UlGEOLLnZSnp.jpg",users:[{name:"SunSmile",avatar:"https://cdn.eleadmin.com/20200609/c184eef391ae48dba87e3057e70238fb.jpg"},{name:"酷酷的大叔",avatar:"https://cdn.eleadmin.com/20200609/2d98970a51b34b6b859339c96b240dcd.jpg"},{name:"Jasmine",avatar:"https://cdn.eleadmin.com/20200609/948344a2a77c47a7a7b332fe12ff749a.jpg"}]},{title:"Node.js",content:"Node.js 是一个基于 Chrome V8 引擎的 JavaScript 运行环境。Node.js 使用了一个事件驱动、非阻塞式 I/O 的模型，使其轻量又高效。",time:"8 小时前",cover:"https://cdn.eleadmin.com/20200610/CyrCNmTJfv7D6GFAg39bjT3eRkkRm5dI.jpg",users:[{name:"SunSmile",avatar:"https://cdn.eleadmin.com/20200609/c184eef391ae48dba87e3057e70238fb.jpg"},{name:"酷酷的大叔",avatar:"https://cdn.eleadmin.com/20200609/2d98970a51b34b6b859339c96b240dcd.jpg"},{name:"Jasmine",avatar:"https://cdn.eleadmin.com/20200609/948344a2a77c47a7a7b332fe12ff749a.jpg"}]}]}},computed:{styleResponsive(){return this.$store.state.theme.styleResponsive}},methods:{query(){}}},m=s,d=t(1001),i=(0,d.Z)(m,n,c,!1,null,"00491686",null),l=i.exports}}]);