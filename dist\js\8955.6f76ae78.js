"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[8955],{28955:function(e,t,l){l.r(t),l.d(t,{default:function(){return c}});var a=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:"查看规则配置",visible:e.dialogVisible,width:"750px","destroy-on-close":!0,"custom-class":"rule-view-dialog"},on:{"update:visible":function(t){e.dialogVisible=t},open:e.onOpen}},[t("el-descriptions",{attrs:{column:2,border:"",size:"medium"}},[t("el-descriptions-item",{attrs:{label:"规则编码"}},[t("el-tag",{attrs:{effect:"plain",size:"medium"}},[e._v(e._s(e.form.ruleCode))])],1),t("el-descriptions-item",{attrs:{label:"规则名称"}},[e._v(" "+e._s(e.form.ruleName)+" ")]),t("el-descriptions-item",{attrs:{label:"规则类型"}},[t("el-tag",{attrs:{type:"primary",effect:"plain"}},[t("i",{staticClass:"el-icon-setting"}),e._v(" "+e._s(e.getRuleTypeName(e.form.ruleType))+" ")])],1),t("el-descriptions-item",{attrs:{label:"状态"}},[t("el-tag",{attrs:{type:1===e.form.status?"success":"info",size:"medium"}},[t("i",{class:1===e.form.status?"el-icon-check":"el-icon-close"}),e._v(" "+e._s(1===e.form.status?"有效":"无效")+" ")])],1),t("el-descriptions-item",{attrs:{span:2,label:"规则描述"}},[t("span",{staticClass:"description-text"},[e._v(e._s(e.form.ruleDesc||"-"))])])],1),t("div",{staticClass:"rule-content-section"},[t("div",{staticClass:"section-header"},[t("i",{staticClass:"el-icon-document"}),e._v(" 规则内容 ")]),"TEMPLATE_CONFIG"===e.form.ruleType&&e.form.ruleContent?[t("div",{staticClass:"template-content-view"},[t("el-card",{staticClass:"info-card",attrs:{shadow:"never"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("i",{staticClass:"el-icon-link"}),t("b",[e._v("关联模板：")]),e._v(e._s(e.templateName)+" ")])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("i",{staticClass:"el-icon-location"}),t("b",[e._v("所属地市：")]),e._v(e._s(e.getCityName(e.templateContent.lanId))+" ")])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("i",{staticClass:"el-icon-money"}),t("b",[e._v("预存金额：")]),e._v(e._s(e.templateContent.deposit)+" 元 "),e.templateContent.depositIsMin?t("el-tag",{attrs:{size:"mini",type:"warning",effect:"plain"}},[e._v("最低预存")]):e._e(),t("div",{staticClass:"config-badge",class:!1!==e.templateContent.depositRequired?"required":"optional",staticStyle:{display:"inline-block","margin-left":"8px"}},[t("i",{class:!1!==e.templateContent.depositRequired?"el-icon-star-on":"el-icon-circle-check"}),e._v(" "+e._s(!1!==e.templateContent.depositRequired?"必选":"可选")+" ")])],1)])],1)],1),t("div",{staticClass:"sales-section"},[t("div",{staticClass:"section-header"},[t("i",{staticClass:"el-icon-shopping-cart-2"}),e._v(" 销售品列表 ")]),e.templateContent.salesProducts&&e.templateContent.salesProducts.length>0?t("div",{staticClass:"sales-table-container"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.templateContent.salesProducts,border:"",size:"small","header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266"}}},[t("el-table-column",{attrs:{prop:"prodName",label:"销售品名称","min-width":"120"}}),t("el-table-column",{attrs:{prop:"roleName",label:"角色名称","min-width":"100"}}),t("el-table-column",{attrs:{label:"配置",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(l){return[t("div",{staticClass:"config-badge",class:"required"===l.row.config||parseInt(l.row.minNum||0)>0?"required":"optional"},[t("i",{class:"required"===l.row.config||parseInt(l.row.minNum||0)>0?"el-icon-star-on":"el-icon-circle-check"}),e._v(" "+e._s("required"===l.row.config||parseInt(l.row.minNum||0)>0?"必选":"可选")+" ")])]}}],null,!1,3413684505)}),t("el-table-column",{attrs:{label:"数量限制",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(l){return[t("el-tag",{attrs:{size:"small",effect:"plain",type:"info"}},[e._v(e._s(l.row.minNum||"0")+" - "+e._s(l.row.maxNum||"不限"))])]}}],null,!1,2095570114)}),t("el-table-column",{attrs:{prop:"prodId",label:"ID",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(l){return[t("el-tag",{attrs:{size:"small",effect:"plain"}},[e._v(e._s(l.row.prodId))])]}}],null,!1,1430842357)})],1)],1):t("div",{staticClass:"no-data"},[t("i",{staticClass:"el-icon-shopping-bag-1"}),e._v(" 暂无销售品数据 ")])])],1)]:[t("pre",{staticClass:"json-content"},[e._v(e._s(e.formattedContent))])]],2),t("el-descriptions",{staticClass:"bottom-info",attrs:{column:2,border:"",size:"medium"}},[t("el-descriptions-item",{attrs:{label:"创建人"}},[t("i",{staticClass:"el-icon-user"}),e._v(" "+e._s(e.form.createdBy||"-")+" ")]),t("el-descriptions-item",{attrs:{label:"创建时间"}},[t("i",{staticClass:"el-icon-time"}),e._v(" "+e._s(e.formatDate(e.form.createdDate))+" ")]),t("el-descriptions-item",{attrs:{label:"修改人"}},[t("i",{staticClass:"el-icon-edit"}),e._v(" "+e._s(e.form.updatedBy||"-")+" ")]),t("el-descriptions-item",{attrs:{label:"修改时间"}},[t("i",{staticClass:"el-icon-date"}),e._v(" "+e._s(e.formatDate(e.form.updatedDate))+" ")])],1),t("div",{attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("关闭")])],1)],1)},s=[],i=(l(38862),{name:"ViewDialog",props:{visible:{type:Boolean,default:!1},data:Object},data(){return{form:{ruleCode:"",ruleName:"",ruleType:"",ruleContent:"",ruleDesc:"",status:1,createdBy:"",createdDate:"",updatedBy:"",updatedDate:""},dialogVisible:!1,ruleTypeOptions:[{label:"模板配置",value:"TEMPLATE_CONFIG"},{label:"商品配置",value:"GOODS_CONFIG"},{label:"订单配置",value:"ORDER_CONFIG"},{label:"系统配置",value:"SYSTEM_CONFIG"}],cityOptions:[{value:"730",label:"岳阳"},{value:"731",label:"长沙"},{value:"732",label:"湘潭"},{value:"733",label:"株洲"},{value:"734",label:"衡阳"},{value:"735",label:"郴州"},{value:"736",label:"常德"},{value:"737",label:"益阳"},{value:"738",label:"娄底"},{value:"739",label:"邵阳"},{value:"743",label:"湘西"},{value:"744",label:"张家界"},{value:"745",label:"怀化"},{value:"746",label:"永州"}],templateContent:{},templateName:""}},computed:{formattedContent(){if(!this.form.ruleContent)return"-";try{const e=JSON.parse(this.form.ruleContent);return JSON.stringify(e,null,2)}catch(e){return this.form.ruleContent}}},watch:{visible(e){this.dialogVisible=e},dialogVisible(e){this.$emit("update:visible",e)}},methods:{async onOpen(){if(this.data)if(Object.keys(this.form).forEach((e=>{this.form[e]=this.data[e]||""})),"TEMPLATE_CONFIG"===this.form.ruleType&&this.form.ruleContent)try{this.templateContent=JSON.parse(this.form.ruleContent),this.templateName=this.templateContent.templateId||"-"}catch(e){this.templateContent={},this.templateName="-"}else this.templateContent={},this.templateName=""},getRuleTypeName(e){const t=this.ruleTypeOptions.find((t=>t.value===e));return t?t.label:e||"-"},getCityName(e){const t=this.cityOptions.find((t=>t.value===e));return t?t.label:e||"-"},getOptionText(e){return"required"===e?"必选":"optional"===e?"可选":"-"},formatDate(e){if(!e)return"-";const t="string"===typeof e?new Date(e):e;if(isNaN(t.getTime()))return"-";const l=e=>e<10?"0"+e:e;return`${t.getFullYear()}-${l(t.getMonth()+1)}-${l(t.getDate())} ${l(t.getHours())}:${l(t.getMinutes())}:${l(t.getSeconds())}`}}}),n=i,o=l(1001),r=(0,o.Z)(n,a,s,!1,null,"290cdbe3",null),c=r.exports}}]);