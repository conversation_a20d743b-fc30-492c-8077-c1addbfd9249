"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[970,7931,1783,9430,9081,7573],{57931:function(e,l,a){a.r(l),a.d(l,{default:function(){return s}});var t=function(){var e=this,l=e._self._c;return l("el-card",{attrs:{shadow:"never",header:"可搜索"}},[l("div",{staticStyle:{"max-width":"260px"}},[l("ele-tree-select",{attrs:{data:e.data,multiple:!0,clearable:!0,filterable:!0,placeholder:"请选择","default-expand-all":!0},model:{value:e.value,callback:function(l){e.value=l},expression:"value"}})],1)])},i=[],d=a(7573),n={data(){return{value:[],data:d["default"]}}},u=n,c=a(1001),r=(0,c.Z)(u,t,i,!1,null,null,null),s=r.exports},51783:function(e,l,a){a.r(l),a.d(l,{default:function(){return s}});var t=function(){var e=this,l=e._self._c;return l("el-card",{attrs:{shadow:"never",header:"基础用法"}},[l("div",{staticStyle:{"max-width":"260px"}},[l("ele-tree-select",{attrs:{data:e.data,clearable:!0,placeholder:"请选择",disabled:e.disabled,"default-expand-all":!0,"expand-on-click-node":e.expandOnClickNode},model:{value:e.value,callback:function(l){e.value=l},expression:"value"}})],1),l("div",{staticClass:"ele-cell",staticStyle:{"margin-top":"15px"}},[l("div",{staticStyle:{"line-height":"22px"}},[e._v("  "),l("em"),l("em"),l("em"),e._v("禁用： ")]),l("div",{staticClass:"ele-cell-content"},[l("el-radio-group",{model:{value:e.disabled,callback:function(l){e.disabled=l},expression:"disabled"}},[l("el-radio",{attrs:{label:!1}},[e._v("否")]),l("el-radio",{attrs:{label:!0}},[e._v("是")])],1)],1)]),l("div",{staticClass:"ele-cell",staticStyle:{"margin-top":"15px"}},[l("div",{staticStyle:{"line-height":"22px"}},[e._v(" 只能选子级：")]),l("div",{staticClass:"ele-cell-content"},[l("el-radio-group",{model:{value:e.expandOnClickNode,callback:function(l){e.expandOnClickNode=l},expression:"expandOnClickNode"}},[l("el-radio",{attrs:{label:!1}},[e._v("否")]),l("el-radio",{attrs:{label:!0}},[e._v("是")])],1)],1)])])},i=[],d=a(7573),n={data(){return{value:void 0,data:d["default"],disabled:!1,expandOnClickNode:!1}},watch:{expandOnClickNode(){this.value=""}}},u=n,c=a(1001),r=(0,c.Z)(u,t,i,!1,null,null,null),s=r.exports},59430:function(e,l,a){a.r(l),a.d(l,{default:function(){return r}});var t=function(){var e=this,l=e._self._c;return l("el-card",{attrs:{shadow:"never",header:"懒加载"}},[l("div",{staticStyle:{"margin-bottom":"12px"}},[e._v("单选：")]),l("div",{staticStyle:{"max-width":"260px"}},[l("ele-tree-select",{attrs:{clearable:!0,lazy:!0,placeholder:"请选择",load:e.loadNode,"init-value":e.initValue},model:{value:e.value,callback:function(l){e.value=l},expression:"value"}})],1),l("div",{staticStyle:{margin:"12px 0"}},[e._v("多选：")]),l("div",{staticStyle:{"max-width":"260px"}},[l("ele-tree-select",{attrs:{multiple:!0,clearable:!0,lazy:!0,placeholder:"请选择",load:e.loadNode,"init-value":e.initValue2},model:{value:e.value2,callback:function(l){e.value2=l},expression:"value2"}})],1),l("div",{staticStyle:{"margin-top":"12px"}},[l("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.setInitValue}},[e._v(" 回显数据 ")])],1)])},i=[],d={data(){return{value:void 0,initValue:void 0,value2:[],initValue2:void 0}},methods:{loadNode(e,l){setTimeout((()=>{if(e.data)if("1"===e.data.value)l([{label:"用户管理",value:"2"},{label:"角色管理",value:"5"}]);else if("2"===e.data.value)l([{label:"添加用户",value:"3"},{label:"修改用户",value:"4"}]);else if("5"===e.data.value)l([{label:"添加角色",value:"6"},{label:"修改角色",value:"7"}]);else{if("8"===e.data.value)return void l([{label:"登录日志",value:"9"},{label:"操作日志",value:"10"}]);l([])}else l([{label:"系统管理",value:"1"},{label:"日志管理",value:"8"}])}),500)},setInitValue(){this.initValue={value:"3",label:"添加用户"},this.initValue2=[{value:"3",label:"添加用户"},{label:"修改角色",value:"7"},{label:"登录日志",value:"9"}]}}},n=d,u=a(1001),c=(0,u.Z)(n,t,i,!1,null,null,null),r=c.exports},39081:function(e,l,a){a.r(l),a.d(l,{default:function(){return s}});var t=function(){var e=this,l=e._self._c;return l("el-card",{attrs:{shadow:"never",header:"多选"}},[l("div",{staticStyle:{"max-width":"260px"}},[l("ele-tree-select",{attrs:{data:e.data,multiple:!0,clearable:!0,placeholder:"请选择",disabled:e.disabled,"default-expand-all":!0,"check-strictly":e.checkStrictly,expandOnClickNode:""},model:{value:e.value,callback:function(l){e.value=l},expression:"value"}})],1),l("div",{staticClass:"ele-cell",staticStyle:{"margin-top":"15px"}},[l("div",{staticStyle:{"line-height":"22px"}},[e._v(" "),l("em"),l("em"),e._v("禁用：")]),l("div",{staticClass:"ele-cell-content"},[l("el-radio-group",{model:{value:e.disabled,callback:function(l){e.disabled=l},expression:"disabled"}},[l("el-radio",{attrs:{label:!1}},[e._v("否")]),l("el-radio",{attrs:{label:!0}},[e._v("是")])],1)],1)]),l("div",{staticClass:"ele-cell",staticStyle:{"margin-top":"15px"}},[l("div",{staticStyle:{"line-height":"22px"}},[e._v(" 父子联动：")]),l("div",{staticClass:"ele-cell-content"},[l("el-radio-group",{model:{value:e.checkStrictly,callback:function(l){e.checkStrictly=l},expression:"checkStrictly"}},[l("el-radio",{attrs:{label:!1}},[e._v("是")]),l("el-radio",{attrs:{label:!0}},[e._v("否")])],1)],1)])])},i=[],d=a(7573),n={data(){return{value:[],data:d["default"],disabled:!1,checkStrictly:!1}}},u=n,c=a(1001),r=(0,c.Z)(u,t,i,!1,null,null,null),s=r.exports},60970:function(e,l,a){a.r(l),a.d(l,{default:function(){return b}});var t=function(){var e=this,l=e._self._c;return l("div",{staticClass:"ele-body"},[l("demo-basic"),l("demo-multiple"),l("demo-lazy"),l("demo-advanced")],1)},i=[],d=a(51783),n=a(39081),u=a(59430),c=a(57931),r={name:"ExtensionTreeSelect",components:{DemoBasic:d["default"],DemoMultiple:n["default"],DemoLazy:u["default"],DemoAdvanced:c["default"]}},s=r,v=a(1001),o=(0,v.Z)(s,t,i,!1,null,null,null),b=o.exports},7573:function(e,l,a){a.r(l),l["default"]=[{label:"系统管理",value:"1",children:[{label:"用户管理",value:"2",children:[{label:"添加用户",value:"3"},{label:"修改用户",value:"4"}]},{label:"角色管理",value:"5",children:[{label:"添加角色",value:"6",disabled:!1},{label:"修改角色",value:"7"}]}]},{label:"日志管理",value:"8",children:[{label:"登录日志",value:"9"},{label:"操作日志",value:"10",disabled:!1}]},{label:"列表页面",value:"11",children:[{label:"基础列表",value:"12"},{label:"卡片列表",value:"13",children:[{label:"项目",value:"14"},{label:"应用",value:"15"},{label:"文章",value:"16"}]}]}]}}]);