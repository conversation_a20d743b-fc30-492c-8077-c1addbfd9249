"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9584],{9584:function(e,t,a){a.r(t),a.d(t,{default:function(){return c}});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("el-card",{attrs:{shadow:"never"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t("edit-form",{attrs:{data:e.user}})],1)])],1)},i=[],r=a(47898),n=a(59477),d={name:"ListBasicEdit",components:{EditForm:r["default"]},data(){return{loading:!0,user:void 0}},created(){this.query()},methods:{query(){const{query:e}=this.$route;e.id&&(0,n.PR)(Number(e.id)).then((e=>{this.loading=!1,this.user=e})).catch((e=>{this.$message.error(e.message)}))}}},u=d,o=a(1001),l=(0,o.Z)(u,s,i,!1,null,null,null),c=l.exports}}]);