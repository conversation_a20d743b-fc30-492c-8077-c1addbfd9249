"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[921],{29266:function(t,e,n){n.d(e,{D:function(){return u},KZ:function(){return l},XD:function(){return p},Zi:function(){return d},_W:function(){return v},bX:function(){return c},eq:function(){return h},ke:function(){return a}});var i=n(33051),r=n(32234),o=(0,r.Yf)();function a(t,e,n,r,o){var a;if(e&&e.ecModel){var s=e.ecModel.getUpdatePayload();a=s&&s.animation}var u=e&&e.isAnimationEnabled(),l="update"===t;if(u){var h=void 0,c=void 0,f=void 0;r?(h=(0,i.pD)(r.duration,200),c=(0,i.pD)(r.easing,"cubicOut"),f=0):(h=e.getShallow(l?"animationDurationUpdate":"animationDuration"),c=e.getShallow(l?"animationEasingUpdate":"animationEasing"),f=e.getShallow(l?"animationDelayUpdate":"animationDelay")),a&&(null!=a.duration&&(h=a.duration),null!=a.easing&&(c=a.easing),null!=a.delay&&(f=a.delay)),(0,i.mf)(f)&&(f=f(n,o)),(0,i.mf)(h)&&(h=h(n));var p={duration:h||0,delay:f,easing:c};return p}return null}function s(t,e,n,r,o,s,u){var l,h=!1;(0,i.mf)(o)?(u=s,s=o,o=null):(0,i.Kn)(o)&&(s=o.cb,u=o.during,h=o.isFrom,l=o.removeOpt,o=o.dataIndex);var c="leave"===t;c||e.stopAnimation("leave");var f=a(t,r,o,c?l||{}:null,r&&r.getAnimationDelayParams?r.getAnimationDelayParams(e,o):null);if(f&&f.duration>0){var p=f.duration,d=f.delay,v=f.easing,g={duration:p,delay:d||0,easing:v,done:s,force:!!s||!!u,setToFinal:!c,scope:t,during:u};h?e.animateFrom(n,g):e.animateTo(n,g)}else e.stopAnimation(),!h&&e.attr(n),u&&u(1),s&&s()}function u(t,e,n,i,r,o){s("update",t,e,n,i,r,o)}function l(t,e,n,i,r,o){s("enter",t,e,n,i,r,o)}function h(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++){var n=t.animators[e];if("leave"===n.scope)return!0}return!1}function c(t,e,n,i,r,o){h(t)||s("leave",t,e,n,i,r,o)}function f(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),c(t,{style:{opacity:0}},e,n,i)}function p(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse((function(t){t.isGroup||f(t,e,n,i)})):f(t,e,n,i)}function d(t){o(t).oldStyle=t.style}function v(t){return o(t).oldStyle}},95682:function(t,e,n){n.d(e,{Z:function(){return r}});var i=n(32234);function r(){var t=(0,i.Yf)();return function(e){var n=t(e),i=e.pipelineContext,r=!!n.large,o=!!n.progressiveRender,a=n.large=!(!i||!i.large),s=n.progressiveRender=!(!i||!i.progressiveRender);return!(r===a&&o===s)&&"reset"}}},47329:function(t,e,n){var i=n(33051),r=n(38154),o=n(46496),a=n(96498),s=n(29266),u=n(18490),l=n(61158),h=n(270),c=n(32234),f=(0,c.Yf)(),p=i.d9,d=i.ak,v=function(){function t(){this._dragging=!1,this.animationThreshold=15}return t.prototype.render=function(t,e,n,o){var a=e.get("value"),s=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,o||this._lastValue!==a||this._lastStatus!==s){this._lastValue=a,this._lastStatus=s;var u=this._group,l=this._handle;if(!s||"hide"===s)return u&&u.hide(),void(l&&l.hide());u&&u.show(),l&&l.show();var h={};this.makeElOption(h,a,t,e,n);var c=h.graphicKey;c!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=c;var f=this._moveAnimation=this.determineAnimation(t,e);if(u){var p=i.WA(g,e,f);this.updatePointerEl(u,h,p),this.updateLabelEl(u,h,p,e)}else u=this._group=new r.Z,this.createPointerEl(u,h,t,e),this.createLabelEl(u,h,t,e),n.getZr().add(u);x(u,e,!0),this._renderHandle(a)}},t.prototype.remove=function(t){this.clear(t)},t.prototype.dispose=function(t){this.clear(t)},t.prototype.determineAnimation=function(t,e){var n=e.get("animation"),i=t.axis,r="category"===i.type,o=e.get("snap");if(!o&&!r)return!1;if("auto"===n||null==n){var a=this.animationThreshold;if(r&&i.getBandWidth()>a)return!0;if(o){var s=u.r(t).seriesDataCount,l=i.getExtent();return Math.abs(l[0]-l[1])/s>a}return!1}return!0===n},t.prototype.makeElOption=function(t,e,n,i,r){},t.prototype.createPointerEl=function(t,e,n,i){var r=e.pointer;if(r){var a=f(t).pointerEl=new o[r.type](p(e.pointer));t.add(a)}},t.prototype.createLabelEl=function(t,e,n,i){if(e.label){var r=f(t).labelEl=new a.ZP(p(e.label));t.add(r),m(r,i)}},t.prototype.updatePointerEl=function(t,e,n){var i=f(t).pointerEl;i&&e.pointer&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},t.prototype.updateLabelEl=function(t,e,n,i){var r=f(t).labelEl;r&&(r.setStyle(e.label.style),n(r,{x:e.label.x,y:e.label.y}),m(r,i))},t.prototype._renderHandle=function(t){if(!this._dragging&&this.updateHandleTransform){var e,n=this._axisPointerModel,r=this._api.getZr(),a=this._handle,s=n.getModel("handle"),u=n.get("status");if(!s.get("show")||!u||"hide"===u)return a&&r.remove(a),void(this._handle=null);this._handle||(e=!0,a=this._handle=o.createIcon(s.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){l.sT(t.event)},onmousedown:d(this._onHandleDragMove,this,0,0),drift:d(this._onHandleDragMove,this),ondragend:d(this._onHandleDragEnd,this)}),r.add(a)),x(a,n,!1),a.setStyle(s.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var c=s.get("size");i.kJ(c)||(c=[c,c]),a.scaleX=c[0]/2,a.scaleY=c[1]/2,h.T9(this,"_doDispatchAxisPointer",s.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,e)}},t.prototype._moveHandleToValue=function(t,e){g(this._axisPointerModel,!e&&this._moveAnimation,this._handle,_(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},t.prototype._onHandleDragMove=function(t,e){var n=this._handle;if(n){this._dragging=!0;var i=this.updateHandleTransform(_(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=i,n.stopAnimation(),n.attr(_(i)),f(n).lastProp=null,this._doDispatchAxisPointer()}},t.prototype._doDispatchAxisPointer=function(){var t=this._handle;if(t){var e=this._payloadInfo,n=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:n.axis.dim,axisIndex:n.componentIndex}]})}},t.prototype._onHandleDragEnd=function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},t.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null),h.ZH(this,"_doDispatchAxisPointer")},t.prototype.doClear=function(){},t.prototype.buildLabel=function(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}},t}();function g(t,e,n,i){y(f(n).lastProp,i)||(f(n).lastProp=i,e?s.D(n,i,t):(n.stopAnimation(),n.attr(i)))}function y(t,e){if(i.Kn(t)&&i.Kn(e)){var n=!0;return i.S6(e,(function(e,i){n=n&&y(t[i],e)})),!!n}return t===e}function m(t,e){t[e.get(["label","show"])?"show":"hide"]()}function _(t){return{x:t.x||0,y:t.y||0,rotation:t.rotation||0}}function x(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse((function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)}))}e["Z"]=v},92448:function(t,e,n){n.d(e,{Z:function(){return o}});var i=n(33051),r=n(32234);function o(t,e){var n,o=[],a=t.seriesIndex;if(null==a||!(n=e.getSeriesByIndex(a)))return{point:[]};var s=n.getData(),u=r.gO(s,t);if(null==u||u<0||i.kJ(u))return{point:[]};var l=s.getItemGraphicEl(u),h=n.coordinateSystem;if(n.getTooltipPosition)o=n.getTooltipPosition(u)||[];else if(h&&h.dataToPoint)if(t.isStacked){var c=h.getBaseAxis(),f=h.getOtherAxis(c),p=f.dim,d=c.dim,v="x"===p||"radius"===p?1:0,g=s.mapDimension(d),y=[];y[v]=s.get(g,u),y[1-v]=s.get(s.getCalculationInfo("stackResultDimension"),u),o=h.dataToPoint(y)||[]}else o=h.dataToPoint(s.getValues(i.UI(h.dimensions,(function(t){return s.mapDimension(t)})),u))||[];else if(l){var m=l.getBoundingRect().clone();m.applyTransform(l.transform),o=[m.x+m.width/2,m.y+m.height/2]}return{point:o,el:l}}},56996:function(t,e,n){n.d(e,{E:function(){return d},z:function(){return u}});var i=n(33051),r=n(66387),o=n(32234),a=(0,o.Yf)(),s=i.S6;function u(t,e,n){if(!r.Z.node){var i=e.getZr();a(i).records||(a(i).records={}),l(i,e);var o=a(i).records[t]||(a(i).records[t]={});o.handler=n}}function l(t,e){function n(n,i){t.on(n,(function(n){var r=p(e);s(a(t).records,(function(t){t&&i(t,n,r.dispatchAction)})),h(r.pendings,e)}))}a(t).initialized||(a(t).initialized=!0,n("click",i.WA(f,"click")),n("mousemove",i.WA(f,"mousemove")),n("globalout",c))}function h(t,e){var n,i=t.showTip.length,r=t.hideTip.length;i?n=t.showTip[i-1]:r&&(n=t.hideTip[r-1]),n&&(n.dispatchAction=null,e.dispatchAction(n))}function c(t,e,n){t.handler("leave",null,n)}function f(t,e,n,i){e.handler(t,n,i)}function p(t){var e={showTip:[],hideTip:[]},n=function(i){var r=e[i.type];r?r.push(i):(i.dispatchAction=n,t.dispatchAction(i))};return{dispatchAction:n,pendings:e}}function d(t,e){if(!r.Z.node){var n=e.getZr(),i=(a(n).records||{})[t];i&&(a(n).records[t]=null)}}},3380:function(t,e,n){n.d(e,{N:function(){return z}});var i=n(11726),r=n(70655),o=n(47329),a=n(75539),s=n(49069),u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.ZT)(e,t),e.prototype.makeElOption=function(t,e,n,i,r){var o=n.axis,u=o.grid,c=i.get("type"),f=l(u,o).getOtherAxis(o).getGlobalExtent(),p=o.toGlobalCoord(o.dataToCoord(e,!0));if(c&&"none"!==c){var d=a.fk(i),v=h[c](o,p,f);v.style=d,t.graphicKey=v.type,t.pointer=v}var g=s.bK(u.model,n);a.gf(e,t,g,n,i,r)},e.prototype.getHandleTransform=function(t,e,n){var i=s.bK(e.axis.grid.model,e,{labelInside:!1});i.labelMargin=n.get(["handle","margin"]);var r=a.Zh(e.axis,t,i);return{x:r[0],y:r[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,e,n,i){var r=n.axis,o=r.grid,a=r.getGlobalExtent(!0),s=l(o,r).getOtherAxis(r).getGlobalExtent(),u="x"===r.dim?0:1,h=[t.x,t.y];h[u]+=e[u],h[u]=Math.min(a[1],h[u]),h[u]=Math.max(a[0],h[u]);var c=(s[1]+s[0])/2,f=[c,c];f[u]=h[u];var p=[{verticalAlign:"middle"},{align:"center"}];return{x:h[0],y:h[1],rotation:t.rotation,cursorPoint:f,tooltipOption:p[u]}},e}(o.Z);function l(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var h={line:function(t,e,n){var i=a.BL([e,n[0]],[e,n[1]],c(t));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),r=n[1]-n[0];return{type:"Rect",shape:a.uE([e-i/2,n[0]],[i,r],c(t))}}};function c(t){return"x"===t.dim?0:1}var f=u,p=n(98071),d=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return(0,r.ZT)(e,t),e.type="axisPointer",e.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},e}(p.Z),v=d,g=n(56996),y=n(33166),m=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return(0,r.ZT)(e,t),e.prototype.render=function(t,e,n){var i=e.getComponent("tooltip"),r=t.get("triggerOn")||i&&i.get("triggerOn")||"mousemove|click";g.z("axisPointer",n,(function(t,e,n){"none"!==r&&("leave"===t||r.indexOf(t)>=0)&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})}))},e.prototype.remove=function(t,e){g.E("axisPointer",e)},e.prototype.dispose=function(t,e){g.E("axisPointer",e)},e.type="axisPointer",e}(y.Z),_=m,x=n(33051),w=n(18490),S=n(32234),b=n(92448),T=(0,S.Yf)();function M(t,e,n){var i=t.currTrigger,r=[t.x,t.y],o=t,a=t.dispatchAction||(0,x.ak)(n.dispatchAction,n),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){R(r)&&(r=(0,b.Z)({seriesIndex:o.seriesIndex,dataIndex:o.dataIndex},e).point);var u=R(r),l=o.axesInfo,h=s.axesInfo,c="leave"===i||R(r),f={},p={},d={list:[],map:{}},v={showPointer:(0,x.WA)(C,p),showTooltip:(0,x.WA)(I,d)};(0,x.S6)(s.coordSysMap,(function(t,e){var n=u||t.containPoint(r);(0,x.S6)(s.coordSysAxesInfo[e],(function(t,e){var i=t.axis,o=Z(l,t);if(!c&&n&&(!l||o)){var a=o&&o.value;null!=a||u||(a=i.pointToData(r)),null!=a&&k(t,a,v,!1,f)}}))}));var g={};return(0,x.S6)(h,(function(t,e){var n=t.linkGroup;n&&!p[e]&&(0,x.S6)(n.axesInfo,(function(e,i){var r=p[i];if(e!==t&&r){var o=r.value;n.mapper&&(o=t.axis.scale.parse(n.mapper(o,L(e),L(t)))),g[t.key]=o}}))})),(0,x.S6)(g,(function(t,e){k(h[e],t,v,!0,f)})),P(p,h,f),A(d,r,t,a),O(h,a,n),f}}function k(t,e,n,i,r){var o=t.axis;if(!o.scale.isBlank()&&o.containData(e))if(t.involveSeries){var a=D(e,t),s=a.payloadBatch,u=a.snapToValue;s[0]&&null==r.seriesIndex&&(0,x.l7)(r,s[0]),!i&&t.snap&&o.containData(u)&&null!=u&&(e=u),n.showPointer(t,e,s),n.showTooltip(t,a,u)}else n.showPointer(t,e)}function D(t,e){var n=e.axis,i=n.dim,r=t,o=[],a=Number.MAX_VALUE,s=-1;return(0,x.S6)(e.seriesModels,(function(e,u){var l,h,c=e.getData().mapDimensionsAll(i);if(e.getAxisTooltipData){var f=e.getAxisTooltipData(c,t,n);h=f.dataIndices,l=f.nestestValue}else{if(h=e.getData().indicesOfNearest(c[0],t,"category"===n.type?.5:null),!h.length)return;l=e.getData().get(c[0],h[0])}if(null!=l&&isFinite(l)){var p=t-l,d=Math.abs(p);d<=a&&((d<a||p>=0&&s<0)&&(a=d,s=p,r=l,o.length=0),(0,x.S6)(h,(function(t){o.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})})))}})),{payloadBatch:o,snapToValue:r}}function C(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function I(t,e,n,i){var r=n.payloadBatch,o=e.axis,a=o.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var u=e.coordSys.model,l=w.zm(u),h=t.map[l];h||(h=t.map[l]={coordSysId:u.id,coordSysIndex:u.componentIndex,coordSysType:u.type,coordSysMainType:u.mainType,dataByAxis:[]},t.list.push(h)),h.dataByAxis.push({axisDim:o.dim,axisIndex:a.componentIndex,axisType:a.type,axisId:a.id,value:i,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:r.slice()})}}function P(t,e,n){var i=n.axesInfo=[];(0,x.S6)(e,(function(e,n){var r=e.axisPointerModel.option,o=t[n];o?(!e.useHandle&&(r.status="show"),r.value=o.value,r.seriesDataIndices=(o.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&i.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})}))}function A(t,e,n,i){if(!R(e)&&t.list.length){var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}else i({type:"hideTip"})}function O(t,e,n){var i=n.getZr(),r="axisPointerLastHighlights",o=T(i)[r]||{},a=T(i)[r]={};(0,x.S6)(t,(function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&(0,x.S6)(n.seriesDataIndices,(function(t){var e=t.seriesIndex+" | "+t.dataIndex;a[e]=t}))}));var s=[],u=[];(0,x.S6)(o,(function(t,e){!a[e]&&u.push(t)})),(0,x.S6)(a,(function(t,e){!o[e]&&s.push(t)})),u.length&&n.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:u}),s.length&&n.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function Z(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}function L(t){var e=t.axis.model,n={},i=n.axisDim=t.axis.dim;return n.axisIndex=n[i+"AxisIndex"]=e.componentIndex,n.axisName=n[i+"AxisName"]=e.name,n.axisId=n[i+"AxisId"]=e.id,n}function R(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function z(t){i.Z.registerAxisPointerClass("CartesianAxisPointer",f),t.registerComponentModel(v),t.registerComponentView(_),t.registerPreprocessor((function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!(0,x.kJ)(e)&&(t.axisPointer.link=[e])}})),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,(function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=(0,w.KM)(t,e)})),t.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},M)}},18490:function(t,e,n){n.d(e,{KM:function(){return o},iG:function(){return c},np:function(){return p},r:function(){return f},zm:function(){return v}});var i=n(12312),r=n(33051);function o(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return a(n,t,e),n.seriesInvolved&&u(n,t),n}function a(t,e,n){var i=e.getComponent("tooltip"),o=e.getComponent("axisPointer"),a=o.get("link",!0)||[],u=[];(0,r.S6)(n.getCoordinateSystems(),(function(n){if(n.axisPointerEnabled){var h=v(n.model),c=t.coordSysAxesInfo[h]={};t.coordSysMap[h]=n;var f=n.model,p=f.getModel("tooltip",i);if((0,r.S6)(n.getAxes(),(0,r.WA)(_,!1,null)),n.getTooltipAxes&&i&&p.get("show")){var g="axis"===p.get("trigger"),y="cross"===p.get(["axisPointer","type"]),m=n.getTooltipAxes(p.get(["axisPointer","axis"]));(g||y)&&(0,r.S6)(m.baseAxes,(0,r.WA)(_,!y||"cross",g)),y&&(0,r.S6)(m.otherAxes,(0,r.WA)(_,"cross",!1))}}function _(i,r,h){var f=h.model.getModel("axisPointer",o),g=f.get("show");if(g&&("auto"!==g||i||d(f))){null==r&&(r=f.get("triggerTooltip")),f=i?s(h,p,o,e,i,r):f;var y=f.get("snap"),m=v(h.model),_=r||y||"category"===h.type,x=t.axesInfo[m]={key:m,axis:h,coordSys:n,axisPointerModel:f,triggerTooltip:r,involveSeries:_,snap:y,useHandle:d(f),seriesModels:[],linkGroup:null};c[m]=x,t.seriesInvolved=t.seriesInvolved||_;var w=l(a,h);if(null!=w){var S=u[w]||(u[w]={axesInfo:{}});S.axesInfo[m]=x,S.mapper=a[w].mapper,x.linkGroup=S}}}}))}function s(t,e,n,o,a,s){var u=e.getModel("axisPointer"),l=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],h={};(0,r.S6)(l,(function(t){h[t]=(0,r.d9)(u.get(t))})),h.snap="category"!==t.type&&!!s,"cross"===u.get("type")&&(h.type="line");var c=h.label||(h.label={});if(null==c.show&&(c.show=!1),"cross"===a){var f=u.get(["label","show"]);if(c.show=null==f||f,!s){var p=h.lineStyle=u.get("crossStyle");p&&(0,r.ce)(c,p.textStyle)}}return t.model.getModel("axisPointer",new i.Z(h,n,o))}function u(t,e){e.eachSeries((function(e){var n=e.coordinateSystem,i=e.get(["tooltip","trigger"],!0),o=e.get(["tooltip","show"],!0);n&&"none"!==i&&!1!==i&&"item"!==i&&!1!==o&&!1!==e.get(["axisPointer","show"],!0)&&(0,r.S6)(t.coordSysAxesInfo[v(n.model)],(function(t){var i=t.axis;n.getAxis(i.dim)===i&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())}))}))}function l(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(h(o[i+"AxisId"],n.id)||h(o[i+"AxisIndex"],n.componentIndex)||h(o[i+"AxisName"],n.name))return r}}function h(t,e){return"all"===t||(0,r.kJ)(t)&&(0,r.cq)(t,e)>=0||t===e}function c(t){var e=f(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=i.parse(a));var s=d(n);null==o&&(r.status=s?"show":"hide");var u=i.getExtent().slice();u[0]>u[1]&&u.reverse(),(null==a||a>u[1])&&(a=u[1]),a<u[0]&&(a=u[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function f(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[v(t)]}function p(t){var e=f(t);return e&&e.axisPointerModel}function d(t){return!!t.get(["handle","show"])}function v(t){return t.type+"||"+t.id}},75539:function(t,e,n){n.d(e,{$_:function(){return f},BL:function(){return y},Rj:function(){return _},Zh:function(){return v},fk:function(){return c},gf:function(){return g},gk:function(){return d},uE:function(){return m}});var i=n(33051),r=n(46496),o=n(80423),a=n(78988),s=n(32892),u=n(34093),l=n(58608),h=n(36006);function c(t){var e,n=t.get("type"),i=t.getModel(n+"Style");return"line"===n?(e=i.getLineStyle(),e.fill=null):"shadow"===n&&(e=i.getAreaStyle(),e.stroke=null),e}function f(t,e,n,i,r){var s=n.get("value"),u=d(s,e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get(["label","precision"]),formatter:n.get(["label","formatter"])}),l=n.getModel("label"),c=a.MY(l.get("padding")||0),f=l.getFont(),v=o.lP(u,f),g=r.position,y=v.width+c[1]+c[3],m=v.height+c[0]+c[2],_=r.align;"right"===_&&(g[0]-=y),"center"===_&&(g[0]-=y/2);var x=r.verticalAlign;"bottom"===x&&(g[1]-=m),"middle"===x&&(g[1]-=m/2),p(g,y,m,i);var w=l.get("backgroundColor");w&&"auto"!==w||(w=e.get(["axisLine","lineStyle","color"])),t.label={x:g[0],y:g[1],style:(0,h.Lr)(l,{text:u,font:f,fill:l.getTextColor(),padding:c,backgroundColor:w}),z2:10}}function p(t,e,n,i){var r=i.getWidth(),o=i.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+n,o)-n,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function d(t,e,n,r,o){t=e.scale.parse(t);var a=e.scale.getLabel({value:t},{precision:o.precision}),s=o.formatter;if(s){var l={value:u.DX(e,{value:t}),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};i.S6(r,(function(t){var e=n.getSeriesByIndex(t.seriesIndex),i=t.dataIndexInside,r=e&&e.getDataParams(i);r&&l.seriesData.push(r)})),i.HD(s)?a=s.replace("{value}",a):i.mf(s)&&(a=s(l))}return a}function v(t,e,n){var i=s.Ue();return s.U1(i,i,n.rotation),s.Iu(i,i,n.position),r.applyTransform([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}function g(t,e,n,i,r,o){var a=l.Z.innerTextLayout(n.rotation,0,n.labelDirection);n.labelMargin=r.get(["label","margin"]),f(e,i,r,o,{position:v(i.axis,t,n),align:a.textAlign,verticalAlign:a.textVerticalAlign})}function y(t,e,n){return n=n||0,{x1:t[n],y1:t[1-n],x2:e[n],y2:e[1-n]}}function m(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}function _(t,e,n,i,r,o){return{cx:t,cy:e,r0:n,r:i,startAngle:r,endAngle:o,clockwise:!0}}},58608:function(t,e,n){var i=n(33051),r=n(38154),o=n(22095),a=n(96498),s=n(46496),u=n(30106),l=n(36006),h=n(12312),c=n(85669),f=n(41525),p=n(32892),d=n(45280),v=n(34093),g=n(54162),y=Math.PI,m=function(){function t(t,e){this.group=new r.Z,this.opt=e,this.axisModel=t,(0,i.ce)(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var n=new r.Z({x:e.position[0],y:e.position[1],rotation:e.rotation});n.updateTransform(),this._transformGroup=n}return t.prototype.hasBuilder=function(t){return!!_[t]},t.prototype.add=function(t){_[t](this.opt,this.axisModel,this.group,this._transformGroup)},t.prototype.getGroup=function(){return this.group},t.innerTextLayout=function(t,e,n){var i,r,o=(0,c.wW)(e-t);return(0,c.mW)(o)?(r=n>0?"top":"bottom",i="center"):(0,c.mW)(o-y)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=o>0&&o<y?n>0?"right":"left":n>0?"left":"right"),{rotation:o,textAlign:i,textVerticalAlign:r}},t.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},t.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},t}(),_={axisLine:function(t,e,n,r){var a=e.get(["axisLine","show"]);if("auto"===a&&t.handleAutoShown&&(a=t.handleAutoShown("axisLine")),a){var s=e.axis.getExtent(),u=r.transform,l=[s[0],0],h=[s[1],0];u&&((0,d.Ne)(l,l,u),(0,d.Ne)(h,h,u));var c=(0,i.l7)({lineCap:"round"},e.getModel(["axisLine","lineStyle"]).getLineStyle()),p=new o.Z({subPixelOptimize:!0,shape:{x1:l[0],y1:l[1],x2:h[0],y2:h[1]},style:c,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1});p.anid="line",n.add(p);var v=e.get(["axisLine","symbol"]);if(null!=v){var g=e.get(["axisLine","symbolSize"]);(0,i.HD)(v)&&(v=[v,v]),((0,i.HD)(g)||(0,i.hj)(g))&&(g=[g,g]);var y=(0,f.Cq)(e.get(["axisLine","symbolOffset"])||0,g),m=g[0],_=g[1];(0,i.S6)([{rotate:t.rotation+Math.PI/2,offset:y[0],r:0},{rotate:t.rotation-Math.PI/2,offset:y[1],r:Math.sqrt((l[0]-h[0])*(l[0]-h[0])+(l[1]-h[1])*(l[1]-h[1]))}],(function(e,i){if("none"!==v[i]&&null!=v[i]){var r=(0,f.th)(v[i],-m/2,-_/2,m,_,c.stroke,!0),o=e.r+e.offset;r.attr({rotation:e.rotate,x:l[0]+o*Math.cos(t.rotation),y:l[1]-o*Math.sin(t.rotation),silent:!0,z2:11}),n.add(r)}}))}}},axisTickLabel:function(t,e,n,r){var o=k(n,r,e,t),a=C(n,r,e,t);if(w(e,a,o),D(n,r,e,t.tickDirection),e.get(["axisLabel","hideOverlap"])){var s=(0,g.VT)((0,i.UI)(a,(function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}})));(0,g.yl)(s)}},axisName:function(t,e,n,r){var o=(0,i.Jv)(t.axisName,e.get("name"));if(o){var h,c,f=e.get("nameLocation"),p=t.nameDirection,d=e.getModel("nameTextStyle"),v=e.get("nameGap")||0,g=e.axis.getExtent(),_=g[0]>g[1]?-1:1,w=["start"===f?g[0]-_*v:"end"===f?g[1]+_*v:(g[0]+g[1])/2,T(f)?t.labelOffset+p*v:0],S=e.get("nameRotate");null!=S&&(S=S*y/180),T(f)?h=m.innerTextLayout(t.rotation,null!=S?S:t.rotation,p):(h=x(t.rotation,f,S||0,g),c=t.axisNameAvailableWidth,null!=c&&(c=Math.abs(c/Math.sin(h.rotation)),!isFinite(c)&&(c=null)));var b=d.getFont(),M=e.get("nameTruncate",!0)||{},k=M.ellipsis,D=(0,i.Jv)(t.nameTruncateMaxWidth,M.maxWidth,c),C=new a.ZP({x:w[0],y:w[1],rotation:h.rotation,silent:m.isLabelSilent(e),style:(0,l.Lr)(d,{text:o,font:b,overflow:"truncate",width:D,ellipsis:k,fill:d.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:d.get("align")||h.textAlign,verticalAlign:d.get("verticalAlign")||h.textVerticalAlign}),z2:1});if(s.setTooltipConfig({el:C,componentModel:e,itemName:o}),C.__fullText=o,C.anid="name",e.get("triggerEvent")){var I=m.makeAxisEventDataBase(e);I.targetType="axisName",I.name=o,(0,u.A)(C).eventData=I}r.add(C),C.updateTransform(),n.add(C),C.decomposeTransform()}}};function x(t,e,n,i){var r,o,a=(0,c.wW)(n-t),s=i[0]>i[1],u="start"===e&&!s||"start"!==e&&s;return(0,c.mW)(a-y/2)?(o=u?"bottom":"top",r="center"):(0,c.mW)(a-1.5*y)?(o=u?"top":"bottom",r="center"):(o="middle",r=a<1.5*y&&a>y/2?u?"left":"right":u?"right":"left"),{rotation:a,textAlign:r,textVerticalAlign:o}}function w(t,e,n){if(!(0,v.WY)(t.axis)){var i=t.get(["axisLabel","showMinLabel"]),r=t.get(["axisLabel","showMaxLabel"]);e=e||[],n=n||[];var o=e[0],a=e[1],s=e[e.length-1],u=e[e.length-2],l=n[0],h=n[1],c=n[n.length-1],f=n[n.length-2];!1===i?(S(o),S(l)):b(o,a)&&(i?(S(a),S(h)):(S(o),S(l))),!1===r?(S(s),S(c)):b(u,s)&&(r?(S(u),S(f)):(S(s),S(c)))}}function S(t){t&&(t.ignore=!0)}function b(t,e){var n=t&&t.getBoundingRect().clone(),i=e&&e.getBoundingRect().clone();if(n&&i){var r=p.yR([]);return p.U1(r,r,-t.rotation),n.applyTransform(p.dC([],r,t.getLocalTransform())),i.applyTransform(p.dC([],r,e.getLocalTransform())),n.intersect(i)}}function T(t){return"middle"===t||"center"===t}function M(t,e,n,i,r){for(var a=[],s=[],u=[],l=0;l<t.length;l++){var h=t[l].coord;s[0]=h,s[1]=0,u[0]=h,u[1]=n,e&&((0,d.Ne)(s,s,e),(0,d.Ne)(u,u,e));var c=new o.Z({subPixelOptimize:!0,shape:{x1:s[0],y1:s[1],x2:u[0],y2:u[1]},style:i,z2:2,autoBatch:!0,silent:!0});c.anid=r+"_"+t[l].tickValue,a.push(c)}return a}function k(t,e,n,r){var o=n.axis,a=n.getModel("axisTick"),s=a.get("show");if("auto"===s&&r.handleAutoShown&&(s=r.handleAutoShown("axisTick")),s&&!o.scale.isBlank()){for(var u=a.getModel("lineStyle"),l=r.tickDirection*a.get("length"),h=o.getTicksCoords(),c=M(h,e.transform,l,(0,i.ce)(u.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),f=0;f<c.length;f++)t.add(c[f]);return c}}function D(t,e,n,r){var o=n.axis,a=n.getModel("minorTick");if(a.get("show")&&!o.scale.isBlank()){var s=o.getMinorTicksCoords();if(s.length)for(var u=a.getModel("lineStyle"),l=r*a.get("length"),h=(0,i.ce)(u.getLineStyle(),(0,i.ce)(n.getModel("axisTick").getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])})),c=0;c<s.length;c++)for(var f=M(s[c],e.transform,l,h,"minorticks_"+c),p=0;p<f.length;p++)t.add(f[p])}}function C(t,e,n,r){var o=n.axis,s=(0,i.Jv)(r.axisLabelShow,n.get(["axisLabel","show"]));if(s&&!o.scale.isBlank()){var c=n.getModel("axisLabel"),f=c.get("margin"),p=o.getViewLabels(),d=((0,i.Jv)(r.labelRotate,c.get("rotate"))||0)*y/180,v=m.innerTextLayout(r.rotation,d,r.labelDirection),g=n.getCategories&&n.getCategories(!0),_=[],x=m.isLabelSilent(n),w=n.get("triggerEvent");return(0,i.S6)(p,(function(s,p){var d="ordinal"===o.scale.type?o.scale.getRawOrdinalNumber(s.tickValue):s.tickValue,y=s.formattedLabel,S=s.rawLabel,b=c;if(g&&g[d]){var T=g[d];(0,i.Kn)(T)&&T.textStyle&&(b=new h.Z(T.textStyle,c,n.ecModel))}var M=b.getTextColor()||n.get(["axisLine","lineStyle","color"]),k=o.dataToCoord(d),D=new a.ZP({x:k,y:r.labelOffset+r.labelDirection*f,rotation:v.rotation,silent:x,z2:10+(s.level||0),style:(0,l.Lr)(b,{text:y,align:b.getShallow("align",!0)||v.textAlign,verticalAlign:b.getShallow("verticalAlign",!0)||b.getShallow("baseline",!0)||v.textVerticalAlign,fill:(0,i.mf)(M)?M("category"===o.type?S:"value"===o.type?d+"":d,p):M})});if(D.anid="label_"+d,w){var C=m.makeAxisEventDataBase(n);C.targetType="axisLabel",C.value=S,C.tickIndex=p,"category"===o.type&&(C.dataIndex=d),(0,u.A)(D).eventData=C}e.add(D),D.updateTransform(),_.push(D),t.add(D),D.decomposeTransform()})),_}}e["Z"]=m},11726:function(t,e,n){var i=n(70655),r=n(18490),o=n(33166),a={},s=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return(0,i.ZT)(e,t),e.prototype.render=function(e,n,i,o){this.axisPointerClass&&r.iG(e),t.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,i,!0)},e.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},e.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},e.prototype.dispose=function(e,n){this._disposeAxisPointer(n),t.prototype.dispose.apply(this,arguments)},e.prototype._doUpdateAxisPointerClass=function(t,n,i){var o=e.getAxisPointerClass(this.axisPointerClass);if(o){var a=r.np(t);a?(this._axisPointer||(this._axisPointer=new o)).render(t,a,n,i):this._disposeAxisPointer(n)}},e.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},e.registerAxisPointerClass=function(t,e){a[t]=e},e.getAxisPointerClass=function(t){return t&&a[t]},e.type="axis",e}(o.Z);e["Z"]=s},82739:function(t,e,n){n.d(e,{N:function(){return ct}});var i=n(3380),r=n(68023),o=n(70655),a=n(98071),s=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return(0,o.ZT)(e,t),e.type="tooltip",e.dependencies=["axisPointer"],e.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},e}(a.Z),u=s,l=n(33051),h=n(66387),c=n(61158),f=n(84602),p=n(78988);function d(t){var e=t.get("confine");return null!=e?!!e:"richText"===t.get("renderMode")}function v(t){if(h.Z.domSupported)for(var e=document.documentElement.style,n=0,i=t.length;n<i;n++)if(t[n]in e)return t[n]}var g=v(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),y=v(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function m(t,e){if(!t)return e;e=(0,p.zW)(e,!0);var n=t.indexOf(e);return t=-1===n?e:"-"+t.slice(0,n)+"-"+e,t.toLowerCase()}function _(t,e){var n=t.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(t);return n?e?n[e]:n:null}var x=n(5685),w=m(y,"transition"),S=m(g,"transform"),b="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(h.Z.transform3dSupported?"will-change:transform;":"");function T(t){return t="left"===t?"right":"right"===t?"left":"top"===t?"bottom":"top",t}function M(t,e,n){if(!(0,l.HD)(n)||"inside"===n)return"";var i=t.get("backgroundColor"),r=t.get("borderWidth");e=(0,p.Lz)(e);var o,a=T(n),s=Math.max(1.5*Math.round(r),6),u="",h=S+":";(0,l.cq)(["left","right"],a)>-1?(u+="top:50%",h+="translateY(-50%) rotate("+(o="left"===a?-225:-45)+"deg)"):(u+="left:50%",h+="translateX(-50%) rotate("+(o="top"===a?225:45)+"deg)");var c=o*Math.PI/180,f=s+r,d=f*Math.abs(Math.cos(c))+f*Math.abs(Math.sin(c)),v=Math.round(100*((d-Math.SQRT2*r)/2+Math.SQRT2*r-(d-f)/2))/100;u+=";"+a+":-"+v+"px";var g=e+" solid "+r+"px;",y=["position:absolute;width:"+s+"px;height:"+s+"px;",u+";"+h+";","border-bottom:"+g,"border-right:"+g,"background-color:"+i+";"];return'<div style="'+y.join("")+'"></div>'}function k(t,e){var n="cubic-bezier(0.23,1,0.32,1)",i=" "+t/2+"s "+n,r="opacity"+i+",visibility"+i;return e||(i=" "+t+"s "+n,r+=h.Z.transformSupported?","+S+i:",left"+i+",top"+i),w+":"+r}function D(t,e,n){var i=t.toFixed(0)+"px",r=e.toFixed(0)+"px";if(!h.Z.transformSupported)return n?"top:"+r+";left:"+i+";":[["top",r],["left",i]];var o=h.Z.transform3dSupported,a="translate"+(o?"3d":"")+"("+i+","+r+(o?",0":"")+")";return n?"top:0;left:0;"+S+":"+a+";":[["top",0],["left",0],[g,a]]}function C(t){var e=[],n=t.get("fontSize"),i=t.getTextColor();i&&e.push("color:"+i),e.push("font:"+t.getFont()),n&&e.push("line-height:"+Math.round(3*n/2)+"px");var r=t.get("textShadowColor"),o=t.get("textShadowBlur")||0,a=t.get("textShadowOffsetX")||0,s=t.get("textShadowOffsetY")||0;return r&&o&&e.push("text-shadow:"+a+"px "+s+"px "+o+"px "+r),(0,l.S6)(["decoration","align"],(function(n){var i=t.get(n);i&&e.push("text-"+n+":"+i)})),e.join(";")}function I(t,e,n){var i=[],r=t.get("transitionDuration"),o=t.get("backgroundColor"),a=t.get("shadowBlur"),s=t.get("shadowColor"),u=t.get("shadowOffsetX"),h=t.get("shadowOffsetY"),c=t.getModel("textStyle"),f=(0,x.d_)(t,"html"),d=u+"px "+h+"px "+a+"px "+s;return i.push("box-shadow:"+d),e&&r&&i.push(k(r,n)),o&&i.push("background-color:"+o),(0,l.S6)(["width","color","radius"],(function(e){var n="border-"+e,r=(0,p.zW)(n),o=t.get(r);null!=o&&i.push(n+":"+o+("color"===e?"":"px"))})),i.push(C(c)),null!=f&&i.push("padding:"+(0,p.MY)(f).join("px ")+"px"),i.join(";")+";"}function P(t,e,n,i,r){var o=e&&e.painter;if(n){var a=o&&o.getViewportRoot();a&&(0,f.YB)(t,a,document.body,i,r)}else{t[0]=i,t[1]=r;var s=o&&o.getViewportRootOffset();s&&(t[0]+=s.offsetLeft,t[1]+=s.offsetTop)}t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}var A=function(){function t(t,e,n){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._firstShow=!0,this._longHide=!0,h.Z.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var r=this._zr=e.getZr(),o=this._appendToBody=n&&n.appendToBody;P(this._styleCoord,r,o,e.getWidth()/2,e.getHeight()/2),o?document.body.appendChild(i):t.appendChild(i),this._container=t;var a=this;i.onmouseenter=function(){a._enterable&&(clearTimeout(a._hideTimeout),a._show=!0),a._inContent=!0},i.onmousemove=function(t){if(t=t||window.event,!a._enterable){var e=r.handler,n=r.painter.getViewportRoot();(0,c.OD)(n,t,!0),e.dispatch("mousemove",t)}},i.onmouseleave=function(){a._inContent=!1,a._enterable&&a._show&&a.hideLater(a._hideDelay)}}return t.prototype.update=function(t){var e=this._container,n=_(e,"position"),i=e.style;"absolute"!==i.position&&"absolute"!==n&&(i.position="relative");var r=t.get("alwaysShowContent");r&&this._moveIfResized(),this.el.className=t.get("className")||""},t.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var n=this.el,i=n.style,r=this._styleCoord;n.innerHTML?i.cssText=b+I(t,!this._firstShow,this._longHide)+D(r[0],r[1],!0)+"border-color:"+(0,p.Lz)(e)+";"+(t.get("extraCssText")||"")+";pointer-events:"+(this._enterable?"auto":"none"):i.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},t.prototype.setContent=function(t,e,n,i,r){var o=this.el;if(null!=t){var a="";if((0,l.HD)(r)&&"item"===n.get("trigger")&&!d(n)&&(a=M(n,i,r)),(0,l.HD)(t))o.innerHTML=t+a;else if(t){o.innerHTML="",(0,l.kJ)(t)||(t=[t]);for(var s=0;s<t.length;s++)(0,l.Mh)(t[s])&&t[s].parentNode!==o&&o.appendChild(t[s]);if(a&&o.childNodes.length){var u=document.createElement("div");u.innerHTML=a,o.appendChild(u)}}}else o.innerHTML=""},t.prototype.setEnterable=function(t){this._enterable=t},t.prototype.getSize=function(){var t=this.el;return[t.offsetWidth,t.offsetHeight]},t.prototype.moveTo=function(t,e){var n=this._styleCoord;if(P(n,this._zr,this._appendToBody,t,e),null!=n[0]&&null!=n[1]){var i=this.el.style,r=D(n[0],n[1]);(0,l.S6)(r,(function(t){i[t[0]]=t[1]}))}},t.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},t.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",h.Z.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout((function(){return t._longHide=!0}),500)},t.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout((0,l.ak)(this.hide,this),t)):this.hide())},t.prototype.isShow=function(){return this._show},t.prototype.dispose=function(){this.el.parentNode.removeChild(this.el)},t}(),O=A,Z=n(96498),L=n(70175),R=function(){function t(t){this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._zr=t.getZr(),N(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return t.prototype.update=function(t){var e=t.get("alwaysShowContent");e&&this._moveIfResized()},t.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},t.prototype.setContent=function(t,e,n,i,r){var o=this;l.Kn(t)&&(0,L._y)(""),this.el&&this._zr.remove(this.el);var a=n.getModel("textStyle");this.el=new Z.ZP({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:i,textShadowColor:a.get("textShadowColor"),fill:n.get(["textStyle","color"]),padding:(0,x.d_)(n,"richText"),verticalAlign:"top",align:"left"},z:n.get("z")}),l.S6(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],(function(t){o.el.style[t]=n.get(t)})),l.S6(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],(function(t){o.el.style[t]=a.get(t)||0})),this._zr.add(this.el);var s=this;this.el.on("mouseover",(function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0})),this.el.on("mouseout",(function(){s._enterable&&s._show&&s.hideLater(s._hideDelay),s._inContent=!1}))},t.prototype.setEnterable=function(t){this._enterable=t},t.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),n=B(t.style);return[e.width+n.left+n.right,e.height+n.top+n.bottom]},t.prototype.moveTo=function(t,e){var n=this.el;if(n){var i=this._styleCoord;N(i,this._zr,t,e),t=i[0],e=i[1];var r=n.style,o=z(r.borderWidth||0),a=B(r);n.x=t+o+a.left,n.y=e+o+a.top,n.markRedraw()}},t.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},t.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},t.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(l.ak(this.hide,this),t)):this.hide())},t.prototype.isShow=function(){return this._show},t.prototype.dispose=function(){this._zr.remove(this.el)},t}();function z(t){return Math.max(0,t)}function B(t){var e=z(t.shadowBlur||0),n=z(t.shadowOffsetX||0),i=z(t.shadowOffsetY||0);return{left:z(e-n),right:z(e+n),top:z(e-i),bottom:z(e+i)}}function N(t,e,n,i){t[0]=n,t[1]=i,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}var E=R,F=n(85669),H=n(25293),W=n(92448),V=n(76172),U=n(12312),G=n(56996),X=n(34093),Y=n(75539),q=n(32234),K=n(33166),j=n(15015),J=n(30106),Q=n(61219),$=n(18310),tt=n(270),et=new H.Z({shape:{x:-1,y:-1,width:2,height:2}}),nt=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return(0,o.ZT)(e,t),e.prototype.init=function(t,e){if(!h.Z.node&&e.getDom()){var n=t.getComponent("tooltip"),i=this._renderMode=(0,q.U9)(n.get("renderMode"));this._tooltipContent="richText"===i?new E(e):new O(e.getDom(),e,{appendToBody:n.get("appendToBody",!0)})}},e.prototype.render=function(t,e,n){if(!h.Z.node&&n.getDom()){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,this._alwaysShowContent=t.get("alwaysShowContent");var i=this._tooltipContent;i.update(t),i.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow(),"richText"!==this._renderMode&&t.get("transitionDuration")?(0,tt.T9)(this,"_updatePosition",50,"fixRate"):(0,tt.ZH)(this,"_updatePosition")}},e.prototype._initGlobalListener=function(){var t=this._tooltipModel,e=t.get("triggerOn");G.z("itemTooltip",this._api,(0,l.ak)((function(t,n,i){"none"!==e&&(e.indexOf(t)>=0?this._tryShow(n,i):"leave"===t&&this._hide(i))}),this))},e.prototype._keepShow=function(){var t=this._tooltipModel,e=this._ecModel,n=this._api,i=t.get("triggerOn");if(null!=this._lastX&&null!=this._lastY&&"none"!==i&&"click"!==i){var r=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout((function(){!n.isDisposed()&&r.manuallyShowTip(t,e,n,{x:r._lastX,y:r._lastY,dataByCoordSys:r._lastDataByCoordSys})}))}},e.prototype.manuallyShowTip=function(t,e,n,i){if(i.from!==this.uid&&!h.Z.node&&n.getDom()){var r=rt(i,n);this._ticket="";var o=i.dataByCoordSys,a=lt(i,e,n);if(a){var s=a.el.getBoundingRect().clone();s.applyTransform(a.el.transform),this._tryShow({offsetX:s.x+s.width/2,offsetY:s.y+s.height/2,target:a.el,position:i.position,positionDefault:"bottom"},r)}else if(i.tooltip&&null!=i.x&&null!=i.y){var u=et;u.x=i.x,u.y=i.y,u.update(),(0,J.A)(u).tooltipConfig={name:null,option:i.tooltip},this._tryShow({offsetX:i.x,offsetY:i.y,target:u},r)}else if(o)this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:o,tooltipOption:i.tooltipOption},r);else if(null!=i.seriesIndex){if(this._manuallyAxisShowTip(t,e,n,i))return;var l=(0,W.Z)(i,e),c=l.point[0],f=l.point[1];null!=c&&null!=f&&this._tryShow({offsetX:c,offsetY:f,target:l.el,position:i.position,positionDefault:"bottom"},r)}else null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},r))}},e.prototype.manuallyHideTip=function(t,e,n,i){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,i.from!==this.uid&&this._hide(rt(i,n))},e.prototype._manuallyAxisShowTip=function(t,e,n,i){var r=i.seriesIndex,o=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){var s=e.getSeriesByIndex(r);if(s){var u=s.getData(),l=it([u.getItemModel(o),s,(s.coordinateSystem||{}).model],this._tooltipModel);if("axis"===l.get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:i.position}),!0}}},e.prototype._tryShow=function(t,e){var n=t.target,i=this._tooltipModel;if(i){this._lastX=t.offsetX,this._lastY=t.offsetY;var r=t.dataByCoordSys;if(r&&r.length)this._showAxisTooltip(r,t);else if(n){var o,a;this._lastDataByCoordSys=null,(0,$.o)(n,(function(t){return null!=(0,J.A)(t).dataIndex?(o=t,!0):null!=(0,J.A)(t).tooltipConfig?(a=t,!0):void 0}),!0),o?this._showSeriesItemTooltip(t,o,e):a?this._showComponentItemTooltip(t,a,e):this._hide(e)}else this._lastDataByCoordSys=null,this._hide(e)}},e.prototype._showOrMove=function(t,e){var n=t.get("showDelay");e=(0,l.ak)(e,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(e,n):e()},e.prototype._showAxisTooltip=function(t,e){var n=this._ecModel,i=this._tooltipModel,r=[e.offsetX,e.offsetY],o=it([e.tooltipOption],i),a=this._renderMode,s=[],u=(0,x.TX)("section",{blocks:[],noHeader:!0}),h=[],c=new x.iv;(0,l.S6)(t,(function(t){(0,l.S6)(t.dataByAxis,(function(t){var e=n.getComponent(t.axisDim+"Axis",t.axisIndex),r=t.value;if(e&&null!=r){var o=Y.gk(r,e.axis,n,t.seriesDataIndices,t.valueLabelOpt),f=(0,x.TX)("section",{header:o,noHeader:!(0,l.fy)(o),sortBlocks:!0,blocks:[]});u.blocks.push(f),(0,l.S6)(t.seriesDataIndices,(function(u){var d=n.getSeriesByIndex(u.seriesIndex),v=u.dataIndexInside,g=d.getDataParams(v);if(!(g.dataIndex<0)){g.axisDim=t.axisDim,g.axisIndex=t.axisIndex,g.axisType=t.axisType,g.axisId=t.axisId,g.axisValue=X.DX(e.axis,{value:r}),g.axisValueLabel=o,g.marker=c.makeTooltipMarker("item",(0,p.Lz)(g.color),a);var y=(0,Q.f)(d.formatTooltip(v,!0,null)),m=y.frag;if(m){var _=it([d],i).get("valueFormatter");f.blocks.push(_?(0,l.l7)({valueFormatter:_},m):m)}y.text&&h.push(y.text),s.push(g)}}))}}))})),u.blocks.reverse(),h.reverse();var f=e.position,d=o.get("order"),v=(0,x.BY)(u,c,a,d,n.get("useUTC"),o.get("textStyle"));v&&h.unshift(v);var g="richText"===a?"\n\n":"<br/>",y=h.join(g);this._showOrMove(o,(function(){this._updateContentNotChangedOnAxis(t,s)?this._updatePosition(o,f,r[0],r[1],this._tooltipContent,s):this._showTooltipContent(o,y,s,Math.random()+"",r[0],r[1],f,null,c)}))},e.prototype._showSeriesItemTooltip=function(t,e,n){var i=this._ecModel,r=(0,J.A)(e),o=r.seriesIndex,a=i.getSeriesByIndex(o),s=r.dataModel||a,u=r.dataIndex,h=r.dataType,c=s.getData(h),f=this._renderMode,d=t.positionDefault,v=it([c.getItemModel(u),s,a&&(a.coordinateSystem||{}).model],this._tooltipModel,d?{position:d}:null),g=v.get("trigger");if(null==g||"item"===g){var y=s.getDataParams(u,h),m=new x.iv;y.marker=m.makeTooltipMarker("item",(0,p.Lz)(y.color),f);var _=(0,Q.f)(s.formatTooltip(u,!1,h)),w=v.get("order"),S=v.get("valueFormatter"),b=_.frag,T=b?(0,x.BY)(S?(0,l.l7)({valueFormatter:S},b):b,m,f,w,i.get("useUTC"),v.get("textStyle")):_.text,M="item_"+s.name+"_"+u;this._showOrMove(v,(function(){this._showTooltipContent(v,T,y,M,t.offsetX,t.offsetY,t.position,t.target,m)})),n({type:"showTip",dataIndexInside:u,dataIndex:c.getRawIndex(u),seriesIndex:o,from:this.uid})}},e.prototype._showComponentItemTooltip=function(t,e,n){var i=(0,J.A)(e),r=i.tooltipConfig,o=r.option||{};if((0,l.HD)(o)){var a=o;o={content:a,formatter:a}}var s=[o],u=this._ecModel.getComponent(i.componentMainType,i.componentIndex);u&&s.push(u),s.push({formatter:o.content});var h=t.positionDefault,c=it(s,this._tooltipModel,h?{position:h}:null),f=c.get("content"),p=Math.random()+"",d=new x.iv;this._showOrMove(c,(function(){var n=(0,l.d9)(c.get("formatterParams")||{});this._showTooltipContent(c,f,n,p,t.offsetX,t.offsetY,t.position,e,d)})),n({type:"showTip",from:this.uid})},e.prototype._showTooltipContent=function(t,e,n,i,r,o,a,s,u){if(this._ticket="",t.get("showContent")&&t.get("show")){var h=this._tooltipContent;h.setEnterable(t.get("enterable"));var c=t.get("formatter");a=a||t.get("position");var f=e,d=this._getNearestPoint([r,o],n,t.get("trigger"),t.get("borderColor")),v=d.color;if(c)if((0,l.HD)(c)){var g=t.ecModel.get("useUTC"),y=(0,l.kJ)(n)?n[0]:n,m=y&&y.axisType&&y.axisType.indexOf("time")>=0;f=c,m&&(f=(0,j.WU)(y.axisValue,f,g)),f=(0,p.kF)(f,n,!0)}else if((0,l.mf)(c)){var _=(0,l.ak)((function(e,i){e===this._ticket&&(h.setContent(i,u,t,v,a),this._updatePosition(t,a,r,o,h,n,s))}),this);this._ticket=i,f=c(n,i,_)}else f=c;h.setContent(f,u,t,v,a),h.show(t,v),this._updatePosition(t,a,r,o,h,n,s)}},e.prototype._getNearestPoint=function(t,e,n,i){return"axis"===n||(0,l.kJ)(e)?{color:i||("html"===this._renderMode?"#fff":"none")}:(0,l.kJ)(e)?void 0:{color:i||e.color||e.borderColor}},e.prototype._updatePosition=function(t,e,n,i,r,o,a){var s=this._api.getWidth(),u=this._api.getHeight();e=e||t.get("position");var h=r.getSize(),c=t.get("align"),f=t.get("verticalAlign"),p=a&&a.getBoundingRect().clone();if(a&&p.applyTransform(a.transform),(0,l.mf)(e)&&(e=e([n,i],o,r.el,p,{viewSize:[s,u],contentSize:h.slice()})),(0,l.kJ)(e))n=(0,F.GM)(e[0],s),i=(0,F.GM)(e[1],u);else if((0,l.Kn)(e)){var v=e;v.width=h[0],v.height=h[1];var g=(0,V.ME)(v,{width:s,height:u});n=g.x,i=g.y,c=null,f=null}else if((0,l.HD)(e)&&a){var y=st(e,p,h,t.get("borderWidth"));n=y[0],i=y[1]}else{y=ot(n,i,r,s,u,c?null:20,f?null:20);n=y[0],i=y[1]}if(c&&(n-=ut(c)?h[0]/2:"right"===c?h[0]:0),f&&(i-=ut(f)?h[1]/2:"bottom"===f?h[1]:0),d(t)){y=at(n,i,r,s,u);n=y[0],i=y[1]}r.moveTo(n,i)},e.prototype._updateContentNotChangedOnAxis=function(t,e){var n=this._lastDataByCoordSys,i=this._cbParamsList,r=!!n&&n.length===t.length;return r&&(0,l.S6)(n,(function(n,o){var a=n.dataByAxis||[],s=t[o]||{},u=s.dataByAxis||[];r=r&&a.length===u.length,r&&(0,l.S6)(a,(function(t,n){var o=u[n]||{},a=t.seriesDataIndices||[],s=o.seriesDataIndices||[];r=r&&t.value===o.value&&t.axisType===o.axisType&&t.axisId===o.axisId&&a.length===s.length,r&&(0,l.S6)(a,(function(t,e){var n=s[e];r=r&&t.seriesIndex===n.seriesIndex&&t.dataIndex===n.dataIndex})),i&&(0,l.S6)(t.seriesDataIndices,(function(t){var n=t.seriesIndex,o=e[n],a=i[n];o&&a&&a.data!==o.data&&(r=!1)}))}))})),this._lastDataByCoordSys=t,this._cbParamsList=e,!!r},e.prototype._hide=function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},e.prototype.dispose=function(t,e){!h.Z.node&&e.getDom()&&((0,tt.ZH)(this,"_updatePosition"),this._tooltipContent.dispose(),G.E("itemTooltip",e))},e.type="tooltip",e}(K.Z);function it(t,e,n){var i,r=e.ecModel;n?(i=new U.Z(n,r,r),i=new U.Z(e.option,i,r)):i=e;for(var o=t.length-1;o>=0;o--){var a=t[o];a&&(a instanceof U.Z&&(a=a.get("tooltip",!0)),(0,l.HD)(a)&&(a={formatter:a}),a&&(i=new U.Z(a,i,r)))}return i}function rt(t,e){return t.dispatchAction||(0,l.ak)(e.dispatchAction,e)}function ot(t,e,n,i,r,o,a){var s=n.getSize(),u=s[0],l=s[1];return null!=o&&(t+u+o+2>i?t-=u+o:t+=o),null!=a&&(e+l+a>r?e-=l+a:e+=a),[t,e]}function at(t,e,n,i,r){var o=n.getSize(),a=o[0],s=o[1];return t=Math.min(t+a,i)-a,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function st(t,e,n,i){var r=n[0],o=n[1],a=Math.ceil(Math.SQRT2*i)+8,s=0,u=0,l=e.width,h=e.height;switch(t){case"inside":s=e.x+l/2-r/2,u=e.y+h/2-o/2;break;case"top":s=e.x+l/2-r/2,u=e.y-o-a;break;case"bottom":s=e.x+l/2-r/2,u=e.y+h+a;break;case"left":s=e.x-r-a,u=e.y+h/2-o/2;break;case"right":s=e.x+l+a,u=e.y+h/2-o/2}return[s,u]}function ut(t){return"center"===t||"middle"===t}function lt(t,e,n){var i=(0,q.zH)(t).queryOptionMap,r=i.keys()[0];if(r&&"series"!==r){var o=(0,q.HZ)(e,r,i.get(r),{useDefault:!1,enableAll:!1,enableNone:!1}),a=o.models[0];if(a){var s,u=n.getViewOfComponentModel(a);return u.group.traverse((function(e){var n=(0,J.A)(e).tooltipConfig;if(n&&n.name===t.name)return s=e,!0})),s?{componentMainType:r,componentIndex:a.componentIndex,el:s}:void 0}}}var ht=nt;function ct(t){(0,r.D)(i.N),t.registerComponentModel(u),t.registerComponentView(ht),t.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},l.ZT),t.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},l.ZT)}},53993:function(t,e,n){n.d(e,{w:function(){return s}});var i=n(33051),r=n(5685),o=n(68540),a=n(32234);function s(t){var e,n,s,l,h=t.series,c=t.dataIndex,f=t.multipleSeries,p=h.getData(),d=p.mapDimensionsAll("defaultedTooltip"),v=d.length,g=h.getRawValue(c),y=(0,i.kJ)(g),m=(0,r.jT)(h,c);if(v>1||y&&!v){var _=u(g,h,c,d,m);e=_.inlineValues,n=_.inlineValueTypes,s=_.blocks,l=_.inlineValues[0]}else if(v){var x=p.getDimensionInfo(d[0]);l=e=(0,o.hk)(p,c,d[0]),n=x.type}else l=e=y?g[0]:g;var w=(0,a.yu)(h),S=w&&h.name||"",b=p.getName(c),T=f?S:b;return(0,r.TX)("section",{header:S,noHeader:f||!w,sortParam:l,blocks:[(0,r.TX)("nameValue",{markerType:"item",markerColor:m,name:T,noName:!(0,i.fy)(T),value:e,valueType:n})].concat(s||[])})}function u(t,e,n,a,s){var u=e.getData(),l=(0,i.u4)(t,(function(t,e,n){var i=u.getDimensionInfo(n);return t||i&&!1!==i.tooltip&&null!=i.displayName}),!1),h=[],c=[],f=[];function p(t,e){var n=u.getDimensionInfo(e);n&&!1!==n.otherDims.tooltip&&(l?f.push((0,r.TX)("nameValue",{markerType:"subItem",markerColor:s,name:n.displayName,value:t,valueType:n.type})):(h.push(t),c.push(n.type)))}return a.length?(0,i.S6)(a,(function(t){p((0,o.hk)(u,n,t),t)})):(0,i.S6)(t,p),{inlineValues:h,inlineValueTypes:c,blocks:f}}},5685:function(t,e,n){n.d(e,{BY:function(){return y},TX:function(){return c},d_:function(){return M},iv:function(){return k},jT:function(){return T}});var i=n(78988),r=n(33051),o=n(98407),a=n(85669),s="line-height:1";function u(t,e){var n=t.color||"#6e7079",r=t.fontSize||12,o=t.fontWeight||"400",a=t.color||"#464646",s=t.fontSize||14,u=t.fontWeight||"900";return"html"===e?{nameStyle:"font-size:"+(0,i.F1)(r+"")+"px;color:"+(0,i.F1)(n)+";font-weight:"+(0,i.F1)(o+""),valueStyle:"font-size:"+(0,i.F1)(s+"")+"px;color:"+(0,i.F1)(a)+";font-weight:"+(0,i.F1)(u+"")}:{nameStyle:{fontSize:r,fill:n,fontWeight:o},valueStyle:{fontSize:s,fill:a,fontWeight:u}}}var l=[0,10,20,30],h=["","\n","\n\n","\n\n\n"];function c(t,e){return e.type=t,e}function f(t){return"section"===t.type}function p(t){return f(t)?v:g}function d(t){if(f(t)){var e=0,n=t.blocks.length,i=n>1||n>0&&!t.noHeader;return(0,r.S6)(t.blocks,(function(t){var n=d(t);n>=e&&(e=n+ +(i&&(!n||f(t)&&!t.noHeader)))})),e}return 0}function v(t,e,n,a){var l=e.noHeader,h=m(d(e)),c=[],f=e.blocks||[];(0,r.hu)(!f||(0,r.kJ)(f)),f=f||[];var v=t.orderMode;if(e.sortBlocks&&v){f=f.slice();var g={valueAsc:"asc",valueDesc:"desc"};if((0,r.RI)(g,v)){var y=new o.ID(g[v],null);f.sort((function(t,e){return y.evaluate(t.sortParam,e.sortParam)}))}else"seriesDesc"===v&&f.reverse()}(0,r.S6)(f,(function(n,i){var o=e.valueFormatter,s=p(n)(o?(0,r.l7)((0,r.l7)({},t),{valueFormatter:o}):t,n,i>0?h.html:0,a);null!=s&&c.push(s)}));var x="richText"===t.renderMode?c.join(h.richText):_(c.join(""),l?n:h.html);if(l)return x;var w=(0,i.uX)(e.header,"ordinal",t.useUTC),b=u(a,t.renderMode).nameStyle;return"richText"===t.renderMode?S(t,w,b)+h.richText+x:_('<div style="'+b+";"+s+';">'+(0,i.F1)(w)+"</div>"+x,n)}function g(t,e,n,o){var a=t.renderMode,s=e.noName,l=e.noValue,h=!e.markerType,c=e.name,f=t.useUTC,p=e.valueFormatter||t.valueFormatter||function(t){return t=(0,r.kJ)(t)?t:[t],(0,r.UI)(t,(function(t,e){return(0,i.uX)(t,(0,r.kJ)(g)?g[e]:g,f)}))};if(!s||!l){var d=h?"":t.markupStyleCreator.makeTooltipMarker(e.markerType,e.markerColor||"#333",a),v=s?"":(0,i.uX)(c,"ordinal",f),g=e.valueType,y=l?[]:p(e.value),m=!h||!s,T=!h&&s,M=u(o,a),k=M.nameStyle,D=M.valueStyle;return"richText"===a?(h?"":d)+(s?"":S(t,v,k))+(l?"":b(t,y,m,T,D)):_((h?"":d)+(s?"":x(v,!h,k))+(l?"":w(y,m,T,D)),n)}}function y(t,e,n,i,r,o){if(t){var a=p(t),s={useUTC:r,renderMode:n,orderMode:i,markupStyleCreator:e,valueFormatter:t.valueFormatter};return a(s,t,0,o)}}function m(t){return{html:l[t],richText:h[t]}}function _(t,e){var n='<div style="clear:both"></div>',i="margin: "+e+"px 0 0";return'<div style="'+i+";"+s+';">'+t+n+"</div>"}function x(t,e,n){var r=e?"margin-left:2px":"";return'<span style="'+n+";"+r+'">'+(0,i.F1)(t)+"</span>"}function w(t,e,n,o){var a=n?"10px":"20px",s=e?"float:right;margin-left:"+a:"";return t=(0,r.kJ)(t)?t:[t],'<span style="'+s+";"+o+'">'+(0,r.UI)(t,(function(t){return(0,i.F1)(t)})).join("&nbsp;&nbsp;")+"</span>"}function S(t,e,n){return t.markupStyleCreator.wrapRichTextStyle(e,n)}function b(t,e,n,i,o){var a=[o],s=i?10:20;return n&&a.push({padding:[0,0,0,s],align:"right"}),t.markupStyleCreator.wrapRichTextStyle((0,r.kJ)(e)?e.join("  "):e,a)}function T(t,e){var n=t.getData().getItemVisual(e,"style"),r=n[t.visualDrawType];return(0,i.Lz)(r)}function M(t,e){var n=t.get("padding");return null!=n?n:"richText"===e?[8,10]:10}var k=function(){function t(){this.richTextStyles={},this._nextStyleNameId=(0,a.jj)()}return t.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},t.prototype.makeTooltipMarker=function(t,e,n){var o="richText"===n?this._generateStyleName():null,a=(0,i.A0)({color:e,type:t,renderMode:n,markerId:o});return(0,r.HD)(a)?a:(this.richTextStyles[o]=a.style,a.content)},t.prototype.wrapRichTextStyle=function(t,e){var n={};(0,r.kJ)(e)?(0,r.S6)(e,(function(t){return(0,r.l7)(n,t)})):(0,r.l7)(n,e);var i=this._generateStyleName();return this.richTextStyles[i]=n,"{"+i+"|"+t+"}"},t}()},34093:function(t,e,n){n.d(e,{aG:function(){return I},Do:function(){return Z},DX:function(){return O},PY:function(){return B},rk:function(){return R},Xv:function(){return k},Yb:function(){return P},J9:function(){return A},Jk:function(){return C},WY:function(){return z},AH:function(){return N}});var i=n(33051),r=n(85043),o=n(70103),a=n(60379),s=n(79093),u=n(60479),l=n(61618),h=n(70655),c=n(85669),f=n(65021),p=a.Z.prototype,d=o.Z.prototype,v=c.NM,g=Math.floor,y=Math.ceil,m=Math.pow,_=Math.log,x=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new o.Z,e._interval=0,e}return(0,h.ZT)(e,t),e.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,r=e.getExtent(),o=d.getTicks.call(this,t);return i.UI(o,(function(t){var e=t.value,i=c.NM(m(this.base,e));return i=e===n[0]&&this._fixMin?S(i,r[0]):i,i=e===n[1]&&this._fixMax?S(i,r[1]):i,{value:i}}),this)},e.prototype.setExtent=function(t,e){var n=this.base;t=_(t)/_(n),e=_(e)/_(n),d.setExtent.call(this,t,e)},e.prototype.getExtent=function(){var t=this.base,e=p.getExtent.call(this);e[0]=m(t,e[0]),e[1]=m(t,e[1]);var n=this._originalScale,i=n.getExtent();return this._fixMin&&(e[0]=S(e[0],i[0])),this._fixMax&&(e[1]=S(e[1],i[1])),e},e.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=_(t[0])/_(e),t[1]=_(t[1])/_(e),p.unionExtent.call(this,t)},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n===1/0||n<=0)){var i=c.Xd(n),r=t/n*i;r<=.5&&(i*=10);while(!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0)i*=10;var o=[c.NM(y(e[0]/i)*i),c.NM(g(e[1]/i)*i)];this._interval=i,this._niceExtent=o}},e.prototype.calcNiceExtent=function(t){d.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return t=_(t)/_(this.base),f.XS(t,this._extent)},e.prototype.normalize=function(t){return t=_(t)/_(this.base),f.Fv(t,this._extent)},e.prototype.scale=function(t){return t=f.bA(t,this._extent),m(this.base,t)},e.type="log",e}(a.Z),w=x.prototype;function S(t,e){return v(t,c.p8(e))}w.getMinorTicks=d.getMinorTicks,w.getLabel=d.getLabel,a.Z.registerClass(x);var b=x,T=n(99936),M=n(38986);function k(t,e){var n=t.type,r=(0,M.Qw)(t,e,t.getExtent()).calculate();t.setBlank(r.isBlank);var o=r.min,a=r.max,u=e.ecModel;if(u&&"time"===n){var l=(0,s.Ge)("bar",u),h=!1;if(i.S6(l,(function(t){h=h||t.getBaseAxis()===e.axis})),h){var c=(0,s.My)(l),f=D(o,a,e,c);o=f.min,a=f.max}}return{extent:[o,a],fixMin:r.minFixed,fixMax:r.maxFixed}}function D(t,e,n,r){var o=n.axis.getExtent(),a=o[1]-o[0],u=(0,s.G_)(r,n.axis);if(void 0===u)return{min:t,max:e};var l=1/0;i.S6(u,(function(t){l=Math.min(t.offset,l)}));var h=-1/0;i.S6(u,(function(t){h=Math.max(t.offset+t.width,h)})),l=Math.abs(l),h=Math.abs(h);var c=l+h,f=e-t,p=1-(l+h)/a,d=f/p-f;return e+=d*(h/c),t-=d*(l/c),{min:t,max:e}}function C(t,e){var n=e,i=k(t,n),r=i.extent,o=n.get("splitNumber");t instanceof b&&(t.base=n.get("logBase"));var a=t.type,s=n.get("interval"),u="interval"===a||"time"===a;t.setExtent(r[0],r[1]),t.calcNiceExtent({splitNumber:o,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:u?n.get("minInterval"):null,maxInterval:u?n.get("maxInterval"):null}),null!=s&&t.setInterval&&t.setInterval(s)}function I(t,e){if(e=e||t.get("type"),e)switch(e){case"category":return new r.Z({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new l.Z({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(a.Z.getClass(e)||o.Z)}}function P(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)}function A(t){var e=t.getLabelModel().get("formatter"),n="category"===t.type?t.scale.getExtent()[0]:null;return"time"===t.scale.type?function(e){return function(n,i){return t.scale.getFormattedLabel(n,i,e)}}(e):i.HD(e)?function(e){return function(n){var i=t.scale.getLabel(n),r=e.replace("{value}",null!=i?i:"");return r}}(e):i.mf(e)?function(e){return function(i,r){return null!=n&&(r=i.value-n),e(O(t,i),r,null!=i.level?{level:i.level}:null)}}(e):function(e){return t.scale.getLabel(e)}}function O(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}function Z(t){var e=t.model,n=t.scale;if(e.get(["axisLabel","show"])&&!n.isBlank()){var i,o,a=n.getExtent();n instanceof r.Z?o=n.count():(i=n.getTicks(),o=i.length);var s,u=t.getLabelModel(),l=A(t),h=1;o>40&&(h=Math.ceil(o/40));for(var c=0;c<o;c+=h){var f=i?i[c]:{value:a[0]+c},p=l(f,c),d=u.getTextRect(p),v=L(d,u.get("rotate")||0);s?s.union(v):s=v}return s}}function L(t,e){var n=e*Math.PI/180,i=t.width,r=t.height,o=i*Math.abs(Math.cos(n))+Math.abs(r*Math.sin(n)),a=i*Math.abs(Math.sin(n))+Math.abs(r*Math.cos(n)),s=new u.Z(t.x,t.y,o,a);return s}function R(t){var e=t.get("interval");return null==e?"auto":e}function z(t){return"category"===t.type&&0===R(t.getLabelModel())}function B(t,e){var n={};return i.S6(t.mapDimensionsAll(e),(function(e){n[(0,T.IR)(t,e)]=!0})),i.XP(n)}function N(t,e,n){e&&i.S6(B(e,n),(function(n){var i=e.getApproximateExtent(n);i[0]<t[0]&&(t[0]=i[0]),i[1]>t[1]&&(t[1]=i[1])}))}},49069:function(t,e,n){n.d(e,{Mk:function(){return s},Yh:function(){return a},bK:function(){return o}});var i=n(33051),r=n(32234);function o(t,e,n){n=n||{};var r=t.coordinateSystem,o=e.axis,a={},s=o.getAxesOnZeroOf()[0],u=o.position,l=s?"onZero":u,h=o.dim,c=r.getRect(),f=[c.x,c.x+c.width,c.y,c.y+c.height],p={left:0,right:1,top:0,bottom:1,onZero:2},d=e.get("offset")||0,v="x"===h?[f[2]-d,f[3]+d]:[f[0]-d,f[1]+d];if(s){var g=s.toGlobalCoord(s.dataToCoord(0));v[p.onZero]=Math.max(Math.min(g,v[1]),v[0])}a.position=["y"===h?v[p[l]]:f[0],"x"===h?v[p[l]]:f[3]],a.rotation=Math.PI/2*("x"===h?0:1);var y={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=y[u],a.labelOffset=s?v[p[u]]-v[p.onZero]:0,e.get(["axisTick","inside"])&&(a.tickDirection=-a.tickDirection),i.Jv(n.labelInside,e.get(["axisLabel","inside"]))&&(a.labelDirection=-a.labelDirection);var m=e.get(["axisLabel","rotate"]);return a.labelRotate="top"===l?-m:m,a.z2=1,a}function a(t){return"cartesian2d"===t.get("coordinateSystem")}function s(t){var e={xAxisModel:null,yAxisModel:null};return i.S6(e,(function(n,i){var o=i.replace(/Model$/,""),a=t.getReferringComponents(o,r.C6).models[0];e[i]=a})),e}},38986:function(t,e,n){n.d(e,{Qw:function(){return u}});var i=n(33051),r=n(80423),o=function(){function t(t,e,n){this._prepareParams(t,e,n)}return t.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var o=this._isOrdinal="ordinal"===t.type;this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero();var a=this._modelMinRaw=e.get("min",!0);(0,i.mf)(a)?this._modelMinNum=l(t,a({min:n[0],max:n[1]})):"dataMin"!==a&&(this._modelMinNum=l(t,a));var s=this._modelMaxRaw=e.get("max",!0);if((0,i.mf)(s)?this._modelMaxNum=l(t,s({min:n[0],max:n[1]})):"dataMax"!==s&&(this._modelMaxNum=l(t,s)),o)this._axisDataLen=e.getCategories().length;else{var u=e.get("boundaryGap"),h=(0,i.kJ)(u)?u:[u||0,u||0];"boolean"===typeof h[0]||"boolean"===typeof h[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[(0,r.GM)(h[0],1),(0,r.GM)(h[1],1)]}},t.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,r=this._axisDataLen,o=this._boundaryGapInner,a=t?null:n-e||Math.abs(e),s="dataMin"===this._modelMinRaw?e:this._modelMinNum,u="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=s,h=null!=u;null==s&&(s=t?r?0:NaN:e-o[0]*a),null==u&&(u=t?r?r-1:NaN:n+o[1]*a),(null==s||!isFinite(s))&&(s=NaN),(null==u||!isFinite(u))&&(u=NaN);var c=(0,i.Bu)(s)||(0,i.Bu)(u)||t&&!r;this._needCrossZero&&(s>0&&u>0&&!l&&(s=0),s<0&&u<0&&!h&&(u=0));var f=this._determinedMin,p=this._determinedMax;return null!=f&&(s=f,l=!0),null!=p&&(u=p,h=!0),{min:s,max:u,minFixed:l,maxFixed:h,isBlank:c}},t.prototype.modifyDataMinMax=function(t,e){this[s[t]]=e},t.prototype.setDeterminedMinMax=function(t,e){var n=a[t];this[n]=e},t.prototype.freeze=function(){this.frozen=!0},t}(),a={min:"_determinedMin",max:"_determinedMax"},s={min:"_dataMin",max:"_dataMax"};function u(t,e,n){var i=t.rawExtentInfo;return i||(i=new o(t,e,n),t.rawExtentInfo=i,i)}function l(t,e){return null==e?null:(0,i.Bu)(e)?NaN:t.parse(e)}},54267:function(t,e,n){var i=n(33051),r={},o=function(){function t(){this._coordinateSystems=[]}return t.prototype.create=function(t,e){var n=[];i.S6(r,(function(i,r){var o=i.create(t,e);n=n.concat(o||[])})),this._coordinateSystems=n},t.prototype.update=function(t,e){i.S6(this._coordinateSystems,(function(n){n.update&&n.update(t,e)}))},t.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},t.register=function(t,e){r[t]=e},t.get=function(t){return r[t]},t}();e["Z"]=o},30454:function(t,e,n){n.d(e,{Hr:function(){return qe},S1:function(){return Wn},zl:function(){return jn},RS:function(){return Jn},qR:function(){return Qn},yn:function(){return ni},je:function(){return ii},sq:function(){return Yn},Br:function(){return qn},ds:function(){return Gn},Pu:function(){return Xn},OB:function(){return ri},YK:function(){return Kn},Og:function(){return $n}});var i=n(70655),r=n(56641),o=n(33051),a=n(66387),s=n(19455),u=n(23510),l=n(32234),h=n(12312),c=n(98071),f="";"undefined"!==typeof navigator&&(f=navigator.platform||"");var p,d,v,g="rgba(0, 0, 0, 0.2)",y={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:g,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:g,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:g,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:g,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:g,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:g,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:f.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},m=n(61772),_=n(82468),x=n(75494),w="\0_ec_inner",S=1;var b=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,i.ZT)(e,t),e.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new h.Z(i),this._locale=new h.Z(r),this._optionManager=o},e.prototype.setOption=function(t,e,n){var i=C(e);this._optionManager.setOption(t,n,i),this._resetOption(null,i)},e.prototype.resetOption=function(t,e){return this._resetOption(t,C(e))},e.prototype._resetOption=function(t,e){var n=!1,i=this._optionManager;if(!t||"recreate"===t){var r=i.mountOption("recreate"===t);0,this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(r,e)):v(this,r),n=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var a=i.getTimelineOption(this);a&&(n=!0,this._mergeOption(a,e))}if(!t||"recreate"===t||"media"===t){var s=i.getMediaOption(this);s.length&&(0,o.S6)(s,(function(t){n=!0,this._mergeOption(t,e)}),this)}return n},e.prototype.mergeOption=function(t){this._mergeOption(t,null)},e.prototype._mergeOption=function(t,e){var n=this.option,i=this._componentsMap,r=this._componentsCount,a=[],s=(0,o.kW)(),u=e&&e.replaceMergeMainTypeMap;function h(e){var a=(0,_.R)(this,e,l.kF(t[e])),s=i.get(e),h=s?u&&u.get(e)?"replaceMerge":"normalMerge":"replaceAll",f=l.ab(s,a,h);l.O0(f,e,c.Z),n[e]=null,i.set(e,null),r.set(e,0);var d,v=[],g=[],y=0;(0,o.S6)(f,(function(t,n){var i=t.existing,r=t.newOption;if(r){var a="series"===e,s=c.Z.getClass(e,t.keyInfo.subType,!a);if(!s)return;if("tooltip"===e){if(d)return void 0;d=!0}if(i&&i.constructor===s)i.name=t.keyInfo.name,i.mergeOption(r,this),i.optionUpdated(r,!1);else{var u=(0,o.l7)({componentIndex:n},t.keyInfo);i=new s(r,this,this,u),(0,o.l7)(i,u),t.brandNew&&(i.__requireNewView=!0),i.init(r,this,this),i.optionUpdated(null,!0)}}else i&&(i.mergeOption({},this),i.optionUpdated({},!1));i?(v.push(i.option),g.push(i),y++):(v.push(void 0),g.push(void 0))}),this),n[e]=v,i.set(e,g),r.set(e,y),"series"===e&&p(this)}(0,m.md)(this),(0,o.S6)(t,(function(t,e){null!=t&&(c.Z.hasClass(e)?e&&(a.push(e),s.set(e,!0)):n[e]=null==n[e]?(0,o.d9)(t):(0,o.TS)(n[e],t,!0))})),u&&u.each((function(t,e){c.Z.hasClass(e)&&!s.get(e)&&(a.push(e),s.set(e,!0))})),c.Z.topologicalTravel(a,c.Z.getAllClassMainTypes(),h,this),this._seriesIndices||p(this)},e.prototype.getOption=function(){var t=(0,o.d9)(this.option);return(0,o.S6)(t,(function(e,n){if(c.Z.hasClass(n)){for(var i=l.kF(e),r=i.length,o=!1,a=r-1;a>=0;a--)i[a]&&!l.lY(i[a])?o=!0:(i[a]=null,!o&&r--);i.length=r,t[n]=i}})),delete t[w],t},e.prototype.getTheme=function(){return this._theme},e.prototype.getLocaleModel=function(){return this._locale},e.prototype.setUpdatePayload=function(t){this._payload=t},e.prototype.getUpdatePayload=function(){return this._payload},e.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){var i=n[e||0];if(i)return i;if(null==e)for(var r=0;r<n.length;r++)if(n[r])return n[r]}},e.prototype.queryComponents=function(t){var e=t.mainType;if(!e)return[];var n,i=t.index,r=t.id,a=t.name,s=this._componentsMap.get(e);return s&&s.length?(null!=i?(n=[],(0,o.S6)(l.kF(i),(function(t){s[t]&&n.push(s[t])}))):n=null!=r?k("id",r,s):null!=a?k("name",a,s):(0,o.hX)(s,(function(t){return!!t})),D(n,t)):[]},e.prototype.findComponents=function(t){var e=t.query,n=t.mainType,i=a(e),r=i?this.queryComponents(i):(0,o.hX)(this._componentsMap.get(n),(function(t){return!!t}));return s(D(r,t));function a(t){var e=n+"Index",i=n+"Id",r=n+"Name";return!t||null==t[e]&&null==t[i]&&null==t[r]?null:{mainType:n,index:t[e],id:t[i],name:t[r]}}function s(e){return t.filter?(0,o.hX)(e,t.filter):e}},e.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if((0,o.mf)(t)){var r=e,a=t;i.each((function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&a.call(r,e,i,i.componentIndex)}}))}else for(var s=(0,o.HD)(t)?i.get(t):(0,o.Kn)(t)?this.findComponents(t):null,u=0;s&&u<s.length;u++){var l=s[u];l&&e.call(n,l,l.componentIndex)}},e.prototype.getSeriesByName=function(t){var e=l.U5(t,null);return(0,o.hX)(this._componentsMap.get("series"),(function(t){return!!t&&null!=e&&t.name===e}))},e.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},e.prototype.getSeriesByType=function(t){return(0,o.hX)(this._componentsMap.get("series"),(function(e){return!!e&&e.subType===t}))},e.prototype.getSeries=function(){return(0,o.hX)(this._componentsMap.get("series"),(function(t){return!!t}))},e.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},e.prototype.eachSeries=function(t,e){d(this),(0,o.S6)(this._seriesIndices,(function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)}),this)},e.prototype.eachRawSeries=function(t,e){(0,o.S6)(this._componentsMap.get("series"),(function(n){n&&t.call(e,n,n.componentIndex)}))},e.prototype.eachSeriesByType=function(t,e,n){d(this),(0,o.S6)(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)}),this)},e.prototype.eachRawSeriesByType=function(t,e,n){return(0,o.S6)(this.getSeriesByType(t),e,n)},e.prototype.isSeriesFiltered=function(t){return d(this),null==this._seriesIndicesMap.get(t.componentIndex)},e.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},e.prototype.filterSeries=function(t,e){d(this);var n=[];(0,o.S6)(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];t.call(e,r,i)&&n.push(i)}),this),this._seriesIndices=n,this._seriesIndicesMap=(0,o.kW)(n)},e.prototype.restoreData=function(t){p(this);var e=this._componentsMap,n=[];e.each((function(t,e){c.Z.hasClass(e)&&n.push(e)})),c.Z.topologicalTravel(n,c.Z.getAllClassMainTypes(),(function(n){(0,o.S6)(e.get(n),(function(e){!e||"series"===n&&T(e,t)||e.restoreData()}))}))},e.internalField=function(){p=function(t){var e=t._seriesIndices=[];(0,o.S6)(t._componentsMap.get("series"),(function(t){t&&e.push(t.componentIndex)})),t._seriesIndicesMap=(0,o.kW)(e)},d=function(t){0},v=function(t,e){t.option={},t.option[w]=S,t._componentsMap=(0,o.kW)({series:[]}),t._componentsCount=(0,o.kW)();var n=e.aria;(0,o.Kn)(n)&&null==n.enabled&&(n.enabled=!0),M(e,t._theme.option),(0,o.TS)(e,y,!1),t._mergeOption(e,null)}}(),e}(h.Z);function T(t,e){if(e){var n=e.seriesIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}function M(t,e){var n=t.color&&!t.colorLayer;(0,o.S6)(e,(function(e,i){"colorLayer"===i&&n||c.Z.hasClass(i)||("object"===typeof e?t[i]=t[i]?(0,o.TS)(t[i],e,!1):(0,o.d9)(e):null==t[i]&&(t[i]=e))}))}function k(t,e,n){if((0,o.kJ)(e)){var i=(0,o.kW)();return(0,o.S6)(e,(function(t){if(null!=t){var e=l.U5(t,null);null!=e&&i.set(t,!0)}})),(0,o.hX)(n,(function(e){return e&&i.get(e[t])}))}var r=l.U5(e,null);return(0,o.hX)(n,(function(e){return e&&null!=r&&e[t]===r}))}function D(t,e){return e.hasOwnProperty("subType")?(0,o.hX)(t,(function(t){return t&&t.subType===e.subType})):t}function C(t){var e=(0,o.kW)();return t&&(0,o.S6)(l.kF(t.replaceMerge),(function(t){e.set(t,!0)})),{replaceMergeMainTypeMap:e}}(0,o.jB)(b,x._);var I=b,P=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],A=function(){function t(t){o.S6(P,(function(e){this[e]=o.ak(t[e],t)}),this)}return t}(),O=A,Z=n(54267),L=/^(min|max)?(.+)$/,R=function(){function t(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return t.prototype.setOption=function(t,e,n){t&&((0,o.S6)((0,l.kF)(t.series),(function(t){t&&t.data&&(0,o.fU)(t.data)&&(0,o.s7)(t.data)})),(0,o.S6)((0,l.kF)(t.dataset),(function(t){t&&t.source&&(0,o.fU)(t.source)&&(0,o.s7)(t.source)}))),t=(0,o.d9)(t);var i=this._optionBackup,r=z(t,e,!i);this._newBaseOption=r.baseOption,i?(r.timelineOptions.length&&(i.timelineOptions=r.timelineOptions),r.mediaList.length&&(i.mediaList=r.mediaList),r.mediaDefault&&(i.mediaDefault=r.mediaDefault)):this._optionBackup=r},t.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],(0,o.d9)(t?e.baseOption:this._newBaseOption)},t.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=(0,o.d9)(n[i.getCurrentIndex()]))}return e},t.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,a=[],s=[];if(!i.length&&!r)return s;for(var u=0,l=i.length;u<l;u++)B(i[u].query,e,n)&&a.push(u);return!a.length&&r&&(a=[-1]),a.length&&!E(a,this._currentMediaIndices)&&(s=(0,o.UI)(a,(function(t){return(0,o.d9)(-1===t?r.option:i[t].option)}))),this._currentMediaIndices=a,s},t}();function z(t,e,n){var i,r,a=[],s=t.baseOption,u=t.timeline,l=t.options,h=t.media,c=!!t.media,f=!!(l||u||s&&s.timeline);function p(t){(0,o.S6)(e,(function(e){e(t,n)}))}return s?(r=s,r.timeline||(r.timeline=u)):((f||c)&&(t.options=t.media=null),r=t),c&&(0,o.kJ)(h)&&(0,o.S6)(h,(function(t){t&&t.option&&(t.query?a.push(t):i||(i=t))})),p(r),(0,o.S6)(l,(function(t){return p(t)})),(0,o.S6)(a,(function(t){return p(t.option)})),{baseOption:r,timelineOptions:l||[],mediaDefault:i,mediaList:a}}function B(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return(0,o.S6)(t,(function(t,e){var n=e.match(L);if(n&&n[1]&&n[2]){var o=n[1],a=n[2].toLowerCase();N(i[a],t,o)||(r=!1)}})),r}function N(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e}function E(t,e){return t.join(",")===e.join(",")}var F=R,H=o.S6,W=o.Kn,V=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function U(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=V.length;n<i;n++){var r=V[n],a=e.normal,s=e.emphasis;a&&a[r]&&(t[r]=t[r]||{},t[r].normal?o.TS(t[r].normal,a[r]):t[r].normal=a[r],a[r]=null),s&&s[r]&&(t[r]=t[r]||{},t[r].emphasis?o.TS(t[r].emphasis,s[r]):t[r].emphasis=s[r],s[r]=null)}}function G(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,r=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,o.ce(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r,r.focus&&(t.emphasis.focus=r.focus),r.blurScope&&(t.emphasis.blurScope=r.blurScope))}}function X(t){G(t,"itemStyle"),G(t,"lineStyle"),G(t,"areaStyle"),G(t,"label"),G(t,"labelLine"),G(t,"upperLabel"),G(t,"edgeLabel")}function Y(t,e){var n=W(t)&&t[e],i=W(n)&&n.textStyle;if(i){0;for(var r=0,o=l.Td.length;r<o;r++){var a=l.Td[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}}function q(t){t&&(X(t),Y(t,"label"),t.emphasis&&Y(t.emphasis,"label"))}function K(t){if(W(t)){U(t),X(t),Y(t,"label"),Y(t,"upperLabel"),Y(t,"edgeLabel"),t.emphasis&&(Y(t.emphasis,"label"),Y(t.emphasis,"upperLabel"),Y(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(U(e),q(e));var n=t.markLine;n&&(U(n),q(n));var i=t.markArea;i&&q(i);var r=t.data;if("graph"===t.type){r=r||t.nodes;var a=t.links||t.edges;if(a&&!o.fU(a))for(var s=0;s<a.length;s++)q(a[s]);o.S6(t.categories,(function(t){X(t)}))}if(r&&!o.fU(r))for(s=0;s<r.length;s++)q(r[s]);if(e=t.markPoint,e&&e.data){var u=e.data;for(s=0;s<u.length;s++)q(u[s])}if(n=t.markLine,n&&n.data){var l=n.data;for(s=0;s<l.length;s++)o.kJ(l[s])?(q(l[s][0]),q(l[s][1])):q(l[s])}"gauge"===t.type?(Y(t,"axisLabel"),Y(t,"title"),Y(t,"detail")):"treemap"===t.type?(G(t.breadcrumb,"itemStyle"),o.S6(t.levels,(function(t){X(t)}))):"tree"===t.type&&X(t.leaves)}}function j(t){return o.kJ(t)?t:t?[t]:[]}function J(t){return(o.kJ(t)?t[0]:t)||{}}function Q(t,e){H(j(t.series),(function(t){W(t)&&K(t)}));var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),H(n,(function(e){H(j(t[e]),(function(t){t&&(Y(t,"axisLabel"),Y(t.axisPointer,"label"))}))})),H(j(t.parallel),(function(t){var e=t&&t.parallelAxisDefault;Y(e,"axisLabel"),Y(e&&e.axisPointer,"label")})),H(j(t.calendar),(function(t){G(t,"itemStyle"),Y(t,"dayLabel"),Y(t,"monthLabel"),Y(t,"yearLabel")})),H(j(t.radar),(function(t){Y(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)})),H(j(t.geo),(function(t){W(t)&&(q(t),H(j(t.regions),(function(t){q(t)})))})),H(j(t.timeline),(function(t){q(t),G(t,"label"),G(t,"itemStyle"),G(t,"controlStyle",!0);var e=t.data;o.kJ(e)&&o.S6(e,(function(t){o.Kn(t)&&(G(t,"label"),G(t,"itemStyle"))}))})),H(j(t.toolbox),(function(t){G(t,"iconStyle"),H(t.feature,(function(t){G(t,"iconStyle")}))})),Y(J(t.axisPointer),"label"),Y(J(t.tooltip).axisPointer,"label")}function $(t,e){for(var n=e.split(","),i=t,r=0;r<n.length;r++)if(i=i&&i[n[r]],null==i)break;return i}function tt(t,e,n,i){for(var r,o=e.split(","),a=t,s=0;s<o.length-1;s++)r=o[s],null==a[r]&&(a[r]={}),a=a[r];(i||null==a[o[s]])&&(a[o[s]]=n)}function et(t){t&&(0,o.S6)(nt,(function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])}))}var nt=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],it=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],rt=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function ot(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<rt.length;n++){var i=rt[n][1],r=rt[n][0];null!=e[i]&&(e[r]=e[i])}}function at(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function st(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function ut(t){t&&null!=t.focusNodeAdjacency&&(t.emphasis=t.emphasis||{},null==t.emphasis.focus&&(t.emphasis.focus="adjacency"))}function lt(t,e){if(t)for(var n=0;n<t.length;n++)e(t[n]),t[n]&&lt(t[n].children,e)}function ht(t,e){Q(t,e),t.series=(0,l.kF)(t.series),(0,o.S6)(t.series,(function(t){if((0,o.Kn)(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e){null!=t.clockWise&&(t.clockwise=t.clockWise),at(t.label);var n=t.data;if(n&&!(0,o.fU)(n))for(var i=0;i<n.length;i++)at(n[i]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},(t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset))}else if("gauge"===e){var r=$(t,"pointer.color");null!=r&&tt(t,"itemStyle.color",r)}else if("bar"===e){ot(t),ot(t.backgroundStyle),ot(t.emphasis);n=t.data;if(n&&!(0,o.fU)(n))for(i=0;i<n.length;i++)"object"===typeof n[i]&&(ot(n[i]),ot(n[i]&&n[i].emphasis))}else if("sunburst"===e){var a=t.highlightPolicy;a&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=a)),st(t),lt(t.data,st)}else"graph"===e||"sankey"===e?ut(t):"map"===e&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation&&(0,o.ce)(t,t.mapLocation));null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation)),et(t)}})),t.dataRange&&(t.visualMap=t.dataRange),(0,o.S6)(it,(function(e){var n=t[e];n&&((0,o.kJ)(n)||(n=[n]),(0,o.S6)(n,(function(t){et(t)})))}))}var ct=n(85669);function ft(t){var e=(0,o.kW)();t.eachSeries((function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),o={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!o.stackedDimension||!o.isStackedByIndex&&!o.stackedByDimension)return;i.length&&r.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(o)}})),e.each(pt)}function pt(t){(0,o.S6)(t,(function(e,n){var i=[],r=[NaN,NaN],o=[e.stackResultDimension,e.stackedOverDimension],a=e.data,s=e.isStackedByIndex,u=e.seriesModel.get("stackStrategy")||"samesign";a.modify(o,(function(o,l,h){var c,f,p=a.get(e.stackedDimension,h);if(isNaN(p))return r;s?f=a.getRawIndex(h):c=a.get(e.stackedByDimension,h);for(var d=NaN,v=n-1;v>=0;v--){var g=t[v];if(s||(f=g.data.rawIndexOf(g.stackedByDimension,c)),f>=0){var y=g.data.getByRawIndex(g.stackResultDimension,f);if("all"===u||"positive"===u&&y>0||"negative"===u&&y<0||"samesign"===u&&p>=0&&y>0||"samesign"===u&&p<=0&&y<0){p=(0,ct.S$)(p,y),d=y;break}}}return i[0]=p,i[1]=d,i}))}))}var dt=n(93321),vt=n(33166),gt=n(75797),yt=n(25293),mt=n(44535),_t=n(29266),xt=n(8846),wt=n(30106),St=n(26357),bt=n(270),Tt=n(59066),Mt=n(89887),kt=n(77515),Dt=(0,l.Yf)(),Ct={itemStyle:(0,Tt.Z)(Mt.t,!0),lineStyle:(0,Tt.Z)(kt.v,!0)},It={lineStyle:"stroke",itemStyle:"fill"};function Pt(t,e){var n=t.visualStyleMapper||Ct[e];return n||(console.warn("Unkown style type '"+e+"'."),Ct.itemStyle)}function At(t,e){var n=t.visualDrawType||It[e];return n||(console.warn("Unkown style type '"+e+"'."),"fill")}var Ot={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=t.getModel(i),a=Pt(t,i),s=a(r),u=r.getShallow("decal");u&&(n.setVisual("decal",u),u.dirty=!0);var l=At(t,i),h=s[l],c=(0,o.mf)(h)?h:null,f="auto"===s.fill||"auto"===s.stroke;if(!s[l]||c||f){var p=t.getColorFromPalette(t.name,null,e.getSeriesCount());s[l]||(s[l]=p,n.setVisual("colorFromPalette",!0)),s.fill="auto"===s.fill||(0,o.mf)(s.fill)?p:s.fill,s.stroke="auto"===s.stroke||(0,o.mf)(s.stroke)?p:s.stroke}if(n.setVisual("style",s),n.setVisual("drawType",l),!e.isSeriesFiltered(t)&&c)return n.setVisual("colorFromPalette",!1),{dataEach:function(e,n){var i=t.getDataParams(n),r=(0,o.l7)({},s);r[l]=c(i),e.setItemVisual(n,"style",r)}}}},Zt=new h.Z,Lt={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t)){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=Pt(t,i),a=n.getVisual("drawType");return{dataEach:n.hasItemOption?function(t,e){var n=t.getRawDataItem(e);if(n&&n[i]){Zt.option=n[i];var s=r(Zt),u=t.ensureUniqueItemVisual(e,"style");(0,o.l7)(u,s),Zt.option.decal&&(t.setItemVisual(e,"decal",Zt.option.decal),Zt.option.decal.dirty=!0),a in s&&t.setItemVisual(e,"colorFromPalette",!1)}}:null}}}},Rt={performRawSeries:!0,overallReset:function(t){var e=(0,o.kW)();t.eachSeries((function(t){var n=t.getColorBy();if(!t.isColorBySeries()){var i=t.type+"-"+n,r=e.get(i);r||(r={},e.set(i,r)),Dt(t).scope=r}})),t.eachSeries((function(e){if(!e.isColorBySeries()&&!t.isSeriesFiltered(e)){var n=e.getRawData(),i={},r=e.getData(),o=Dt(e).scope,a=e.visualStyleAccessPath||"itemStyle",s=At(e,a);r.each((function(t){var e=r.getRawIndex(t);i[e]=t})),n.each((function(t){var a=i[t],u=r.getItemVisual(a,"colorFromPalette");if(u){var l=r.ensureUniqueItemVisual(a,"style"),h=n.getName(t)||t+"",c=n.count();l[s]=e.getColorFromPalette(h,o,c)}}))}}))}},zt=n(38154),Bt=n(96498),Nt=n(14826),Et=Math.PI;function Ft(t,e){e=e||{},o.ce(e,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var n=new zt.Z,i=new yt.Z({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});n.add(i);var r,a=new Bt.ZP({style:{text:e.text,fill:e.textColor,fontSize:e.fontSize,fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:e.fontFamily},zlevel:e.zlevel,z:10001}),s=new yt.Z({style:{fill:"none"},textContent:a,textConfig:{position:"right",distance:10},zlevel:e.zlevel,z:10001});return n.add(s),e.showSpinner&&(r=new Nt.Z({shape:{startAngle:-Et/2,endAngle:-Et/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001}),r.animateShape(!0).when(1e3,{endAngle:3*Et/2}).start("circularInOut"),r.animateShape(!0).when(1e3,{startAngle:3*Et/2}).delay(300).start("circularInOut"),n.add(r)),n.resize=function(){var n=a.getBoundingRect().width,o=e.showSpinner?e.spinnerRadius:0,u=(t.getWidth()-2*o-(e.showSpinner&&n?10:0)-n)/2-(e.showSpinner&&n?0:5+n/2)+(e.showSpinner?0:n/2)+(n?0:o),l=t.getHeight()/2;e.showSpinner&&r.setShape({cx:u,cy:l}),s.setShape({x:u-o,y:l-o,width:2*o,height:2*o}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},n.resize(),n}var Ht=n(8674),Wt=n(42151),Vt=function(){function t(t,e,n,i){this._stageTaskMap=(0,o.kW)(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}return t.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each((function(t){var e=t.overallTask;e&&e.dirty()}))},t.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex,o=r?n.step:null,a=i&&i.modDataCount,s=null!=a?Math.ceil(a/o):null;return{step:o,modBy:s,modDataCount:a}}},t.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},t.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData(),r=i.count(),o=n.progressiveEnabled&&e.incrementalPrepareRender&&r>=n.threshold,a=t.get("large")&&r>=t.get("largeThreshold"),s="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=n.context={progressiveRender:o,modDataCount:s,large:a}},t.prototype.restorePipelines=function(t){var e=this,n=e._pipelineMap=(0,o.kW)();t.eachSeries((function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),e._pipe(t,t.dataTask)}))},t.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),n=this.api;(0,o.S6)(this._allHandlers,(function(i){var r=t.get(i.uid)||t.set(i.uid,{}),a="";(0,o.hu)(!(i.reset&&i.overallReset),a),i.reset&&this._createSeriesStageTask(i,r,e,n),i.overallReset&&this._createOverallStageTask(i,r,e,n)}),this)},t.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},t.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},t.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},t.prototype._performStageTasks=function(t,e,n,i){i=i||{};var r=!1,a=this;function s(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}(0,o.S6)(t,(function(t,o){if(!i.visualType||i.visualType===t.visualType){var u=a._stageTaskMap.get(t.uid),l=u.seriesTaskMap,h=u.overallTask;if(h){var c,f=h.agentStubMap;f.each((function(t){s(i,t)&&(t.dirty(),c=!0)})),c&&h.dirty(),a.updatePayload(h,n);var p=a.getPerformArgs(h,i.block);f.each((function(t){t.perform(p)})),h.perform(p)&&(r=!0)}else l&&l.each((function(o,u){s(i,o)&&o.dirty();var l=a.getPerformArgs(o,i.block);l.skip=!t.performRawSeries&&e.isSeriesFiltered(o.context.model),a.updatePayload(o,n),o.perform(l)&&(r=!0)}))}})),this.unfinished=r||this.unfinished},t.prototype.performSeriesTasks=function(t){var e;t.eachSeries((function(t){e=t.dataTask.perform()||e})),this.unfinished=e||this.unfinished},t.prototype.plan=function(){this._pipelineMap.each((function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)}))},t.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},t.prototype._createSeriesStageTask=function(t,e,n,i){var r=this,a=e.seriesTaskMap,s=e.seriesTaskMap=(0,o.kW)(),u=t.seriesType,l=t.getTargetSeries;function h(e){var o=e.uid,u=s.set(o,a&&a.get(o)||(0,Ht.v)({plan:qt,reset:Kt,count:Qt}));u.context={model:e,ecModel:n,api:i,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:r},r._pipe(e,u)}t.createOnAllSeries?n.eachRawSeries(h):u?n.eachRawSeriesByType(u,h):l&&l(n,i).each(h)},t.prototype._createOverallStageTask=function(t,e,n,i){var r=this,a=e.overallTask=e.overallTask||(0,Ht.v)({reset:Ut});a.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r};var s=a.agentStubMap,u=a.agentStubMap=(0,o.kW)(),l=t.seriesType,h=t.getTargetSeries,c=!0,f=!1,p="";function d(t){var e=t.uid,n=u.set(e,s&&s.get(e)||(f=!0,(0,Ht.v)({reset:Gt,onDirty:Yt})));n.context={model:t,overallProgress:c},n.agent=a,n.__block=c,r._pipe(t,n)}(0,o.hu)(!t.createOnAllSeries,p),l?n.eachRawSeriesByType(l,d):h?h(n,i).each(d):(c=!1,(0,o.S6)(n.getSeries(),d)),f&&a.dirty()},t.prototype._pipe=function(t,e){var n=t.uid,i=this._pipelineMap.get(n);!i.head&&(i.head=e),i.tail&&i.tail.pipe(e),i.tail=e,e.__idxInPipeline=i.count++,e.__pipeline=i},t.wrapStageHandler=function(t,e){return(0,o.mf)(t)&&(t={overallReset:t,seriesType:$t(t)}),t.uid=(0,Wt.Kr)("stageHandler"),e&&(t.visualType=e),t},t}();function Ut(t){t.overallReset(t.ecModel,t.api,t.payload)}function Gt(t){return t.overallProgress&&Xt}function Xt(){this.agent.dirty(),this.getDownstream().dirty()}function Yt(){this.agent&&this.agent.dirty()}function qt(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function Kt(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=(0,l.kF)(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?(0,o.UI)(e,(function(t,e){return Jt(e)})):jt}var jt=Jt(0);function Jt(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var o=e.start;o<e.end;o++)r.dataEach(i,o);else r&&r.progress&&r.progress(e,i)}}function Qt(t){return t.data.count()}function $t(t){te=null;try{t(ee,ne)}catch(e){}return te}var te,ee={},ne={};function ie(t,e){for(var n in e.prototype)t[n]=o.ZT}ie(ee,I),ie(ne,O),ee.eachSeriesByType=ee.eachRawSeriesByType=function(t){te=t},ee.eachComponent=function(t){"series"===t.mainType&&t.subType&&(te=t.subType)};var re=Vt,oe=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],ae={color:oe,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],oe]},se="#B9B8CE",ue="#100C2A",le=function(){return{axisLine:{lineStyle:{color:se}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},he=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],ce={darkMode:!0,color:he,backgroundColor:ue,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:se}},textStyle:{color:se},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:se}},dataZoom:{borderColor:"#71708A",textStyle:{color:se},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:se}},timeline:{lineStyle:{color:se},label:{color:se},controlStyle:{color:se,borderColor:se}},calendar:{itemStyle:{color:ue},dayLabel:{color:se},monthLabel:{color:se},yearLabel:{color:se}},timeAxis:le(),logAxis:le(),valueAxis:le(),categoryAxis:le(),line:{symbol:"circle"},graph:{color:he},gauge:{title:{color:se},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:se},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};ce.categoryAxis.splitLine.show=!1;var fe=ce,pe=n(34251),de=function(){function t(){}return t.prototype.normalizeQuery=function(t){var e={},n={},i={};if(o.HD(t)){var r=(0,pe.u9)(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var a=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};o.S6(t,(function(t,r){for(var o=!1,u=0;u<a.length;u++){var l=a[u],h=r.lastIndexOf(l);if(h>0&&h===r.length-l.length){var c=r.slice(0,h);"data"!==c&&(e.mainType=c,e[l.toLowerCase()]=t,o=!0)}}s.hasOwnProperty(r)&&(n[r]=t,o=!0),o||(i[r]=t)}))}return{cptQuery:e,dataQuery:n,otherQuery:i}},t.prototype.filter=function(t,e){var n=this.eventInfo;if(!n)return!0;var i=n.targetEl,r=n.packedEvent,o=n.model,a=n.view;if(!o||!a)return!0;var s=e.cptQuery,u=e.dataQuery;return l(s,o,"mainType")&&l(s,o,"subType")&&l(s,o,"index","componentIndex")&&l(s,o,"name")&&l(s,o,"id")&&l(u,r,"name")&&l(u,r,"dataIndex")&&l(u,r,"dataType")&&(!a.filterForExposedEvent||a.filterForExposedEvent(t,e.otherQuery,i,r));function l(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},t.prototype.afterTrigger=function(){this.eventInfo=null},t}(),ve=["symbol","symbolSize","symbolRotate","symbolOffset"],ge=ve.concat(["symbolKeepAspect"]),ye={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData();if(t.legendIcon&&n.setVisual("legendIcon",t.legendIcon),t.hasSymbolVisual){for(var i={},r={},a=!1,s=0;s<ve.length;s++){var u=ve[s],l=t.get(u);(0,o.mf)(l)?(a=!0,r[u]=l):i[u]=l}if(i.symbol=i.symbol||t.defaultSymbol,n.setVisual((0,o.l7)({legendIcon:t.legendIcon||i.symbol,symbolKeepAspect:t.get("symbolKeepAspect")},i)),!e.isSeriesFiltered(t)){var h=(0,o.XP)(r);return{dataEach:a?c:null}}}function c(e,n){for(var i=t.getRawValue(n),o=t.getDataParams(n),a=0;a<h.length;a++){var s=h[a];e.setItemVisual(n,s,r[s](i,o))}}}},me={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t)){var n=t.getData();return{dataEach:n.hasItemOption?i:null}}function i(t,e){for(var n=t.getItemModel(e),i=0;i<ge.length;i++){var r=ge[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}}},_e=n(26211),xe=n(70175),we=n(31891),Se=n(10437),be=n(75212),Te=n(18310),Me=n(630);function ke(t,e){t.eachRawSeries((function(n){if(!t.isSeriesFiltered(n)){var i=n.getData();i.hasItemVisual()&&i.each((function(t){var n=i.getItemVisual(t,"decal");if(n){var r=i.ensureUniqueItemVisual(t,"style");r.decal=(0,Me.I)(n,e)}}));var r=i.getVisual("decal");if(r){var o=i.getVisual("style");o.decal=(0,Me.I)(r,e)}}}))}var De=new u.Z,Ce=De,Ie=n(23132),Pe=n(49428),Ae="undefined"!==typeof window,Oe=1,Ze=800,Le=900,Re=1e3,ze=2e3,Be=5e3,Ne=1e3,Ee=1100,Fe=2e3,He=3e3,We=4e3,Ve=4500,Ue=4600,Ge=5e3,Xe=6e3,Ye=7e3,qe={PROCESSOR:{FILTER:Re,SERIES_FILTER:Ze,STATISTIC:Be},VISUAL:{LAYOUT:Ne,PROGRESSIVE_LAYOUT:Ee,GLOBAL:Fe,CHART:He,POST_CHART_LAYOUT:Ue,COMPONENT:We,BRUSH:Ge,CHART_ITEM:Ve,ARIA:Xe,DECAL:Ye}},Ke="__flagInMainProcess",je="__pendingUpdate",Je="__needsUpdateStatus",Qe=/^[a-zA-Z0-9_]+$/,$e="__connectUpdateStatus",tn=0,en=1,nn=2;function rn(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(!this.isDisposed())return an(this,t,e);Pn(this.id)}}function on(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return an(this,t,e)}}function an(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),u.Z.prototype[e].apply(t,n)}var sn,un,ln,hn,cn,fn,pn,dn,vn,gn,yn,mn,_n,xn,wn,Sn,bn,Tn,Mn=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,i.ZT)(e,t),e}(u.Z),kn=Mn.prototype;kn.on=on("on"),kn.off=on("off");var Dn=function(t){function e(e,n,i){var a=t.call(this,new de)||this;a._chartsViews=[],a._chartsMap={},a._componentsViews=[],a._componentsMap={},a._pendingActions=[],i=i||{},(0,o.HD)(n)&&(n=zn[n]),a._dom=e;var u="canvas",l=!1,h=a._zr=r.S1(e,{renderer:i.renderer||u,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height,ssr:i.ssr,useDirtyRect:null==i.useDirtyRect?l:i.useDirtyRect});a._ssr=i.ssr,a._throttledZrFlush=(0,bt.P2)((0,o.ak)(h.flush,h),17),n=(0,o.d9)(n),n&&ht(n,!0),a._theme=n,a._locale=(0,be.D0)(i.locale||be.sO),a._coordSysMgr=new Z.Z;var c=a._api=wn(a);function f(t,e){return t.__prio-e.__prio}return(0,s.Z)(Rn,f),(0,s.Z)(Zn,f),a._scheduler=new re(a,c,Zn,Rn),a._messageCenter=new Mn,a._initEvents(),a.resize=(0,o.ak)(a.resize,a),h.animation.on("frame",a._onframe,a),gn(h,a),yn(h,a),(0,o.s7)(a),a}return(0,i.ZT)(e,t),e.prototype._onframe=function(){if(!this._disposed){Tn(this);var t=this._scheduler;if(this[je]){var e=this[je].silent;this[Ke]=!0;try{sn(this),hn.update.call(this,null,this[je].updateParams)}catch(a){throw this[Ke]=!1,this[je]=null,a}this._zr.flush(),this[Ke]=!1,this[je]=null,dn.call(this,e),vn.call(this,e)}else if(t.unfinished){var n=Oe,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),fn(this,i),t.performVisualTasks(i),xn(this,this._model,r,"remain",{}),n-=+new Date-o}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},e.prototype.getDom=function(){return this._dom},e.prototype.getId=function(){return this.id},e.prototype.getZr=function(){return this._zr},e.prototype.isSSR=function(){return this._ssr},e.prototype.setOption=function(t,e,n){if(!this[Ke])if(this._disposed)Pn(this.id);else{var i,r,a;if((0,o.Kn)(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,a=e.transition,e=e.notMerge),this[Ke]=!0,!this._model||e){var s=new F(this._api),u=this._theme,l=this._model=new I;l.scheduler=this._scheduler,l.ssr=this._ssr,l.init(null,null,null,u,this._locale,s)}this._model.setOption(t,{replaceMerge:r},Ln);var h={seriesTransition:a,optionChanged:!0};if(n)this[je]={silent:i,updateParams:h},this[Ke]=!1,this.getZr().wakeUp();else{try{sn(this),hn.update.call(this,null,h)}catch(c){throw this[je]=null,this[Ke]=!1,c}this._ssr||this._zr.flush(),this[je]=null,this[Ke]=!1,dn.call(this,i),vn.call(this,i)}}},e.prototype.setTheme=function(){(0,xe.Sh)("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},e.prototype.getModel=function(){return this._model},e.prototype.getOption=function(){return this._model&&this._model.getOption()},e.prototype.getWidth=function(){return this._zr.getWidth()},e.prototype.getHeight=function(){return this._zr.getHeight()},e.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||Ae&&window.devicePixelRatio||1},e.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},e.prototype.renderToCanvas=function(t){t=t||{};var e=this._zr.painter;return e.getRenderedCanvas({backgroundColor:t.backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},e.prototype.renderToSVGString=function(t){t=t||{};var e=this._zr.painter;return e.renderToString({useViewBox:t.useViewBox})},e.prototype.getSvgDataURL=function(){if(a.Z.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return(0,o.S6)(e,(function(t){t.stopAnimation(null,!0)})),t.painter.toDataURL()}},e.prototype.getDataURL=function(t){if(!this._disposed){t=t||{};var e=t.excludeComponents,n=this._model,i=[],r=this;(0,o.S6)(e,(function(t){n.eachComponent({mainType:t},(function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)}))}));var a="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return(0,o.S6)(i,(function(t){t.group.ignore=!1})),a}Pn(this.id)},e.prototype.getConnectedDataURL=function(t){if(!this._disposed){var e="svg"===t.type,n=this.group,i=Math.min,a=Math.max,s=1/0;if(En[n]){var u=s,l=s,h=-s,c=-s,f=[],p=t&&t.pixelRatio||this.getDevicePixelRatio();(0,o.S6)(Nn,(function(r,s){if(r.group===n){var p=e?r.getZr().painter.getSvgDom().innerHTML:r.renderToCanvas((0,o.d9)(t)),d=r.getDom().getBoundingClientRect();u=i(d.left,u),l=i(d.top,l),h=a(d.right,h),c=a(d.bottom,c),f.push({dom:p,left:d.left,top:d.top})}})),u*=p,l*=p,h*=p,c*=p;var d=h-u,v=c-l,g=Ie.qW.createCanvas(),y=r.S1(g,{renderer:e?"svg":"canvas"});if(y.resize({width:d,height:v}),e){var m="";return(0,o.S6)(f,(function(t){var e=t.left-u,n=t.top-l;m+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"})),y.painter.getSvgRoot().innerHTML=m,t.connectedBackgroundColor&&y.painter.setBackgroundColor(t.connectedBackgroundColor),y.refreshImmediately(),y.painter.toDataURL()}return t.connectedBackgroundColor&&y.add(new yt.Z({shape:{x:0,y:0,width:d,height:v},style:{fill:t.connectedBackgroundColor}})),(0,o.S6)(f,(function(t){var e=new mt.ZP({style:{x:t.left*p-u,y:t.top*p-l,image:t.dom}});y.add(e)})),y.refreshImmediately(),g.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}Pn(this.id)},e.prototype.convertToPixel=function(t,e){return cn(this,"convertToPixel",t,e)},e.prototype.convertFromPixel=function(t,e){return cn(this,"convertFromPixel",t,e)},e.prototype.containPixel=function(t,e){if(!this._disposed){var n,i=this._model,r=l.pm(i,t);return(0,o.S6)(r,(function(t,i){i.indexOf("Models")>=0&&(0,o.S6)(t,(function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n=n||!!r.containPoint(e);else if("seriesModels"===i){var o=this._chartsMap[t.__viewId];o&&o.containPoint&&(n=n||o.containPoint(e,t))}else 0}),this)}),this),!!n}Pn(this.id)},e.prototype.getVisual=function(t,e){var n=this._model,i=l.pm(n,t,{defaultMainType:"series"}),r=i.seriesModel;var o=r.getData(),a=i.hasOwnProperty("dataIndexInside")?i.dataIndexInside:i.hasOwnProperty("dataIndex")?o.indexOfRawIndex(i.dataIndex):null;return null!=a?(0,_e.Or)(o,a,e):(0,_e.UL)(o,e)},e.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},e.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},e.prototype._initEvents=function(){var t=this;(0,o.S6)(In,(function(e){var n=function(n){var i,r=t.getModel(),a=n.target,s="globalout"===e;if(s?i={}:a&&(0,Te.o)(a,(function(t){var e=(0,wt.A)(t);if(e&&null!=e.dataIndex){var n=e.dataModel||r.getSeriesByIndex(e.seriesIndex);return i=n&&n.getDataParams(e.dataIndex,e.dataType)||{},!0}if(e.eventData)return i=(0,o.l7)({},e.eventData),!0}),!0),i){var u=i.componentType,l=i.componentIndex;"markLine"!==u&&"markPoint"!==u&&"markArea"!==u||(u="series",l=i.seriesIndex);var h=u&&null!=l&&r.getComponent(u,l),c=h&&t["series"===h.mainType?"_chartsMap":"_componentsMap"][h.__viewId];0,i.event=n,i.type=e,t._$eventProcessor.eventInfo={targetEl:a,packedEvent:i,model:h,view:c},t.trigger(e,i)}};n.zrEventfulCallAtLast=!0,t._zr.on(e,n,t)})),(0,o.S6)(On,(function(e,n){t._messageCenter.on(n,(function(t){this.trigger(n,t)}),t)})),(0,o.S6)(["selectchanged"],(function(e){t._messageCenter.on(e,(function(t){this.trigger(e,t)}),t)})),(0,we.s)(this._messageCenter,this,this._api)},e.prototype.isDisposed=function(){return this._disposed},e.prototype.clear=function(){this._disposed?Pn(this.id):this.setOption({series:[]},!0)},e.prototype.dispose=function(){if(this._disposed)Pn(this.id);else{this._disposed=!0;var t=this.getDom();t&&l.P$(this.getDom(),Hn,"");var e=this,n=e._api,i=e._model;(0,o.S6)(e._componentsViews,(function(t){t.dispose(i,n)})),(0,o.S6)(e._chartsViews,(function(t){t.dispose(i,n)})),e._zr.dispose(),e._dom=e._model=e._chartsMap=e._componentsMap=e._chartsViews=e._componentsViews=e._scheduler=e._api=e._zr=e._throttledZrFlush=e._theme=e._coordSysMgr=e._messageCenter=null,delete Nn[e.id]}},e.prototype.resize=function(t){if(!this[Ke])if(this._disposed)Pn(this.id);else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[je]&&(null==i&&(i=this[je].silent),n=!0,this[je]=null),this[Ke]=!0;try{n&&sn(this),hn.update.call(this,{type:"resize",animation:(0,o.l7)({duration:0},t&&t.animation)})}catch(r){throw this[Ke]=!1,r}this[Ke]=!1,dn.call(this,i),vn.call(this,i)}}},e.prototype.showLoading=function(t,e){if(this._disposed)Pn(this.id);else if((0,o.Kn)(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Bn[t]){var n=Bn[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},e.prototype.hideLoading=function(){this._disposed?Pn(this.id):(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},e.prototype.makeActionFromEvent=function(t){var e=(0,o.l7)({},t);return e.type=On[t.type],e},e.prototype.dispatchAction=function(t,e){if(this._disposed)Pn(this.id);else if((0,o.Kn)(e)||(e={silent:!!e}),An[t.type]&&this._model)if(this[Ke])this._pendingActions.push(t);else{var n=e.silent;pn.call(this,t,n);var i=e.flush;i?this._zr.flush():!1!==i&&a.Z.browser.weChat&&this._throttledZrFlush(),dn.call(this,n),vn.call(this,n)}},e.prototype.updateLabelLayout=function(){Ce.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},e.prototype.appendData=function(t){if(this._disposed)Pn(this.id);else{var e=t.seriesIndex,n=this.getModel(),i=n.getSeriesByIndex(e);0,i.appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp()}},e.internalField=function(){function t(t){t.clearColorPalette(),t.eachSeries((function(t){t.clearColorPalette()}))}function e(t){var e=[],n=[],i=!1;if(t.eachComponent((function(t,r){var o=r.get("zlevel")||0,a=r.get("z")||0,s=r.getZLevelKey();i=i||!!s,("series"===t?n:e).push({zlevel:o,z:a,idx:r.componentIndex,type:t,key:s})})),i){var r,a,u=e.concat(n);(0,s.Z)(u,(function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel})),(0,o.S6)(u,(function(e){var n=t.getComponent(e.type,e.idx),i=e.zlevel,o=e.key;null!=r&&(i=Math.max(r,i)),o?(i===r&&o!==a&&i++,a=o):a&&(i===r&&i++,a=""),r=i,n.setZLevel(i)}))}}function n(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),t.hoverState===St.wU&&t.states.emphasis?e.push("emphasis"):t.hoverState===St.CX&&t.states.blur&&e.push("blur"),t.useStates(e)}function r(t,e){var n=t._zr,i=n.storage,r=0;i.traverse((function(t){t.isGroup||r++})),r>e.get("hoverLayerThreshold")&&!a.Z.node&&!a.Z.worker&&e.eachSeries((function(e){if(!e.preventUsingHoverLayer){var n=t._chartsMap[e.__viewId];n.__alive&&n.eachRendered((function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)}))}}))}function u(t,e){var n=t.get("blendMode")||null;e.eachRendered((function(t){t.isGroup||(t.style.blend=n)}))}function h(t,e){if(!t.preventAutoZ){var n=t.get("z")||0,i=t.get("zlevel")||0;e.eachRendered((function(t){return c(t,n,i,-1/0),!0}))}}function c(t,e,n,i){var r=t.getTextContent(),o=t.getTextGuideLine(),a=t.isGroup;if(a)for(var s=t.childrenRef(),u=0;u<s.length;u++)i=Math.max(c(s[u],e,n,i),i);else t.z=e,t.zlevel=n,i=Math.max(t.z2,i);if(r&&(r.z=e,r.zlevel=n,isFinite(i)&&(r.z2=i+2)),o){var l=t.textGuideLineConfig;o.z=e,o.zlevel=n,isFinite(i)&&(o.z2=i+(l&&l.showAbove?1:-1))}return i}function f(t,e){e.eachRendered((function(t){if(!_t.eq(t)){var e=t.getTextContent(),n=t.getTextGuideLine();t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null)}}))}function p(t,e){var i=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),o=i.get("duration"),a=o>0?{duration:o,delay:i.get("delay"),easing:i.get("easing")}:null;e.eachRendered((function(t){if(t.states&&t.states.emphasis){if(_t.eq(t))return;if(t instanceof xt.ZP&&(0,St.e9)(t),t.__dirty){var e=t.prevStates;e&&t.useStates(e)}if(r){t.stateTransition=a;var i=t.getTextContent(),o=t.getTextGuideLine();i&&(i.stateTransition=a),o&&(o.stateTransition=a)}t.__dirty&&n(t)}}))}sn=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),un(t,!0),un(t,!1),e.plan()},un=function(t,e){for(var n=t._model,i=t._scheduler,r=e?t._componentsViews:t._chartsViews,o=e?t._componentsMap:t._chartsMap,a=t._zr,s=t._api,u=0;u<r.length;u++)r[u].__alive=!1;function l(t){var u=t.__requireNewView;t.__requireNewView=!1;var l="_ec_"+t.id+"_"+t.type,h=!u&&o[l];if(!h){var c=(0,pe.u9)(t.type),f=e?vt.Z.getClass(c.main,c.sub):gt.Z.getClass(c.sub);0,h=new f,h.init(n,s),o[l]=h,r.push(h),a.add(h.group)}t.__viewId=h.__id=l,h.__alive=!0,h.__model=t,h.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!e&&i.prepareView(h,t,n,s)}e?n.eachComponent((function(t,e){"series"!==t&&l(e)})):n.eachSeries(l);for(u=0;u<r.length;){var h=r[u];h.__alive?u++:(!e&&h.renderTask.dispose(),a.remove(h.group),h.dispose(n,s),r.splice(u,1),o[h.__id]===h&&delete o[h.__id],h.__id=h.group.__ecComponentInfo=null)}},ln=function(t,e,n,i,r){var a=t._model;if(a.setUpdatePayload(n),i){var s={};s[i+"Id"]=n[i+"Id"],s[i+"Index"]=n[i+"Index"],s[i+"Name"]=n[i+"Name"];var u={mainType:i,query:s};r&&(u.subType=r);var h,c=n.excludeSeriesId;null!=c&&(h=(0,o.kW)(),(0,o.S6)(l.kF(c),(function(t){var e=l.U5(t,null);null!=e&&h.set(e,!0)}))),a&&a.eachComponent(u,(function(e){var i=h&&null!==h.get(e.id);if(!i)if((0,St.xp)(n))if(e instanceof dt.Z)n.type!==St.Ki||n.notBlur||e.get(["emphasis","disabled"])||(0,St.UL)(e,n,t._api);else{var r=(0,St.oJ)(e.mainType,e.componentIndex,n.name,t._api),a=r.focusSelf,s=r.dispatchers;n.type===St.Ki&&a&&!n.notBlur&&(0,St.zI)(e.mainType,e.componentIndex,t._api),s&&(0,o.S6)(s,(function(t){n.type===St.Ki?(0,St.fD)(t):(0,St.Mh)(t)}))}else(0,St.aG)(n)&&e instanceof dt.Z&&((0,St.og)(e,n,t._api),(0,St.ci)(e),bn(t))}),t),a&&a.eachComponent(u,(function(e){var n=h&&null!==h.get(e.id);n||f(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])}),t)}else(0,o.S6)([].concat(t._componentsViews).concat(t._chartsViews),f);function f(i){i&&i.__alive&&i[e]&&i[e](i.__model,a,t._api,n)}},hn={prepareAndUpdate:function(t){sn(this),hn.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(e,n){var i=this._model,r=this._api,o=this._zr,a=this._coordSysMgr,s=this._scheduler;if(i){i.setUpdatePayload(e),s.restoreData(i,e),s.performSeriesTasks(i),a.create(i,r),s.performDataProcessorTasks(i,e),fn(this,i),a.update(i,r),t(i),s.performVisualTasks(i,e),mn(this,i,r,e,n);var u=i.get("backgroundColor")||"transparent",l=i.get("darkMode");o.setBackgroundColor(u),null!=l&&"auto"!==l&&o.setDarkMode(l),Ce.trigger("afterupdate",i,r)}},updateTransform:function(e){var n=this,i=this._model,r=this._api;if(i){i.setUpdatePayload(e);var a=[];i.eachComponent((function(t,o){if("series"!==t){var s=n.getViewOfComponentModel(o);if(s&&s.__alive)if(s.updateTransform){var u=s.updateTransform(o,i,r,e);u&&u.update&&a.push(s)}else a.push(s)}}));var s=(0,o.kW)();i.eachSeries((function(t){var o=n._chartsMap[t.__viewId];if(o.updateTransform){var a=o.updateTransform(t,i,r,e);a&&a.update&&s.set(t.uid,1)}else s.set(t.uid,1)})),t(i),this._scheduler.performVisualTasks(i,e,{setDirty:!0,dirtyMap:s}),xn(this,i,r,e,{},s),Ce.trigger("afterupdate",i,r)}},updateView:function(e){var n=this._model;n&&(n.setUpdatePayload(e),gt.Z.markUpdateMethod(e,"updateView"),t(n),this._scheduler.performVisualTasks(n,e,{setDirty:!0}),mn(this,n,this._api,e,{}),Ce.trigger("afterupdate",n,this._api))},updateVisual:function(e){var n=this,i=this._model;i&&(i.setUpdatePayload(e),i.eachSeries((function(t){t.getData().clearAllVisual()})),gt.Z.markUpdateMethod(e,"updateVisual"),t(i),this._scheduler.performVisualTasks(i,e,{visualType:"visual",setDirty:!0}),i.eachComponent((function(t,r){if("series"!==t){var o=n.getViewOfComponentModel(r);o&&o.__alive&&o.updateVisual(r,i,n._api,e)}})),i.eachSeries((function(t){var r=n._chartsMap[t.__viewId];r.updateVisual(t,i,n._api,e)})),Ce.trigger("afterupdate",i,this._api))},updateLayout:function(t){hn.update.call(this,t)}},cn=function(t,e,n,i){if(t._disposed)Pn(t.id);else{for(var r,o=t._model,a=t._coordSysMgr.getCoordinateSystems(),s=l.pm(o,n),u=0;u<a.length;u++){var h=a[u];if(h[e]&&null!=(r=h[e](o,s,i)))return r}0}},fn=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries((function(t){i.updateStreamModes(t,n[t.__viewId])}))},pn=function(t,e){var n=this,i=this.getModel(),r=t.type,a=t.escapeConnect,s=An[r],u=s.actionInfo,h=(u.update||"update").split(":"),c=h.pop(),f=null!=h[0]&&(0,pe.u9)(h[0]);this[Ke]=!0;var p=[t],d=!1;t.batch&&(d=!0,p=(0,o.UI)(t.batch,(function(e){return e=(0,o.ce)((0,o.l7)({},e),t),e.batch=null,e})));var v,g=[],y=(0,St.aG)(t),m=(0,St.xp)(t);if(m&&(0,St.T5)(this._api),(0,o.S6)(p,(function(e){if(v=s.action(e,n._model,n._api),v=v||(0,o.l7)({},e),v.type=u.event||v.type,g.push(v),m){var i=l.zH(t),r=i.queryOptionMap,a=i.mainTypeSpecified,h=a?r.keys()[0]:"series";ln(n,c,e,h),bn(n)}else y?(ln(n,c,e,"series"),bn(n)):f&&ln(n,c,e,f.main,f.sub)})),"none"!==c&&!m&&!y&&!f)try{this[je]?(sn(this),hn.update.call(this,t),this[je]=null):hn[c].call(this,t)}catch(w){throw this[Ke]=!1,w}if(v=d?{type:u.event||r,escapeConnect:a,batch:g}:g[0],this[Ke]=!1,!e){var _=this._messageCenter;if(_.trigger(v.type,v),y){var x={type:"selectchanged",escapeConnect:a,selected:(0,St.C5)(i),isFromClick:t.isFromClick||!1,fromAction:t.type,fromActionPayload:t};_.trigger(x.type,x)}}},dn=function(t){var e=this._pendingActions;while(e.length){var n=e.shift();pn.call(this,n,t)}},vn=function(t){!t&&this.trigger("updated")},gn=function(t,e){t.on("rendered",(function(n){e.trigger("rendered",n),!t.animation.isFinished()||e[je]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")}))},yn=function(t,e){t.on("mouseover",(function(t){var n=t.target,i=(0,Te.o)(n,St.Av);i&&((0,St.$l)(i,t,e._api),bn(e))})).on("mouseout",(function(t){var n=t.target,i=(0,Te.o)(n,St.Av);i&&((0,St.xr)(i,t,e._api),bn(e))})).on("click",(function(t){var n=t.target,i=(0,Te.o)(n,(function(t){return null!=(0,wt.A)(t).dataIndex}),!0);if(i){var r=i.selected?"unselect":"select",o=(0,wt.A)(i);e._api.dispatchAction({type:r,dataType:o.dataType,dataIndexInside:o.dataIndex,seriesIndex:o.seriesIndex,isFromClick:!0})}}))},mn=function(t,n,i,r,a){e(n),_n(t,n,i,r,a),(0,o.S6)(t._chartsViews,(function(t){t.__alive=!1})),xn(t,n,i,r,a),(0,o.S6)(t._chartsViews,(function(t){t.__alive||t.remove(n,i)}))},_n=function(t,e,n,i,r,a){(0,o.S6)(a||t._componentsViews,(function(t){var r=t.__model;f(r,t),t.render(r,e,n,i),h(r,t),p(r,t)}))},xn=function(t,e,n,i,a,s){var l=t._scheduler;a=(0,o.l7)(a||{},{updatedSeries:e.getSeries()}),Ce.trigger("series:beforeupdate",e,n,a);var c=!1;e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var r=n.renderTask;l.updatePayload(r,i),f(e,n),s&&s.get(e.uid)&&r.dirty(),r.perform(l.getPerformArgs(r))&&(c=!0),n.group.silent=!!e.get("silent"),u(e,n),(0,St.ci)(e)})),l.unfinished=c||l.unfinished,Ce.trigger("series:layoutlabels",e,n,a),Ce.trigger("series:transition",e,n,a),e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];h(e,n),p(e,n)})),r(t,e),Ce.trigger("series:afterupdate",e,n,a)},bn=function(t){t[Je]=!0,t.getZr().wakeUp()},Tn=function(t){t[Je]&&(t.getZr().storage.traverse((function(t){_t.eq(t)||n(t)})),t[Je]=!1)},wn=function(t){return new(function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return(0,i.ZT)(n,e),n.prototype.getCoordinateSystems=function(){return t._coordSysMgr.getCoordinateSystems()},n.prototype.getComponentByElement=function(e){while(e){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}},n.prototype.enterEmphasis=function(e,n){(0,St.fD)(e,n),bn(t)},n.prototype.leaveEmphasis=function(e,n){(0,St.Mh)(e,n),bn(t)},n.prototype.enterBlur=function(e){(0,St.SX)(e),bn(t)},n.prototype.leaveBlur=function(e){(0,St.VP)(e),bn(t)},n.prototype.enterSelect=function(e){(0,St.XX)(e),bn(t)},n.prototype.leaveSelect=function(e){(0,St.SJ)(e),bn(t)},n.prototype.getModel=function(){return t.getModel()},n.prototype.getViewOfComponentModel=function(e){return t.getViewOfComponentModel(e)},n.prototype.getViewOfSeriesModel=function(e){return t.getViewOfSeriesModel(e)},n}(O))(t)},Sn=function(t){function e(t,e){for(var n=0;n<t.length;n++){var i=t[n];i[$e]=e}}(0,o.S6)(On,(function(n,i){t._messageCenter.on(i,(function(n){if(En[t.group]&&t[$e]!==tn){if(n&&n.escapeConnect)return;var i=t.makeActionFromEvent(n),r=[];(0,o.S6)(Nn,(function(e){e!==t&&e.group===t.group&&r.push(e)})),e(r,tn),(0,o.S6)(r,(function(t){t[$e]!==en&&t.dispatchAction(i)})),e(r,nn)}}))}))}}(),e}(u.Z),Cn=Dn.prototype;Cn.on=rn("on"),Cn.off=rn("off"),Cn.one=function(t,e,n){var i=this;function r(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];e&&e.apply&&e.apply(this,n),i.off(t,r)}(0,xe.Sh)("ECharts#one is deprecated."),this.on.call(this,t,r,n)};var In=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function Pn(t){0}var An={},On={},Zn=[],Ln=[],Rn=[],zn={},Bn={},Nn={},En={},Fn=+new Date-0,Hn=(new Date,"_echarts_instance_");function Wn(t,e,n){var i=!(n&&n.ssr);if(i){0;var r=Vn(t);if(r)return r;0}var o=new Dn(t,e,n);return o.id="ec_"+Fn++,Nn[o.id]=o,i&&l.P$(t,Hn,o.id),Sn(o),Ce.trigger("afterinit",o),o}function Vn(t){return Nn[l.IL(t,Hn)]}function Un(t,e){zn[t]=e}function Gn(t){(0,o.cq)(Ln,t)<0&&Ln.push(t)}function Xn(t,e){ei(Zn,t,e,ze)}function Yn(t){Kn("afterinit",t)}function qn(t){Kn("afterupdate",t)}function Kn(t,e){Ce.on(t,e)}function jn(t,e,n){(0,o.mf)(e)&&(n=e,e="");var i=(0,o.Kn)(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,On[e]||((0,o.hu)(Qe.test(i)&&Qe.test(e)),An[i]||(An[i]={action:n,actionInfo:t}),On[e]=i)}function Jn(t,e){Z.Z.register(t,e)}function Qn(t,e){ei(Rn,t,e,Ne,"layout")}function $n(t,e){ei(Rn,t,e,He,"visual")}var ti=[];function ei(t,e,n,i,r){if(((0,o.mf)(e)||(0,o.Kn)(e))&&(n=e,e=i),!((0,o.cq)(ti,n)>=0)){ti.push(n);var a=re.wrapStageHandler(n,r);a.__prio=e,a.__raw=n,t.push(a)}}function ni(t,e){Bn[t]=e}function ii(t,e,n){var i=(0,Pe.C)("registerMap");i&&i(t,e,n)}var ri=Se.DA;$n(Fe,Ot),$n(Ve,Lt),$n(Ve,Rt),$n(Fe,ye),$n(Ve,me),$n(Ye,ke),Gn(ht),Xn(Le,ft),ni("default",Ft),jn({type:St.Ki,event:St.Ki,update:St.Ki},o.ZT),jn({type:St.yx,event:St.yx,update:St.yx},o.ZT),jn({type:St.Hg,event:St.Hg,update:St.Hg},o.ZT),jn({type:St.JQ,event:St.JQ,update:St.JQ},o.ZT),jn({type:St.iK,event:St.iK,update:St.iK},o.ZT),Un("light",ae),Un("dark",fe)},49428:function(t,e,n){n.d(e,{C:function(){return o},M:function(){return r}});var i={};function r(t,e){i[t]=e}function o(t){return i[t]}},75212:function(t,e,n){n.d(e,{sO:function(){return p},D0:function(){return v},Li:function(){return y},G8:function(){return g}});var i=n(12312),r=n(66387),o={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},a={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}},s=n(33051),u="ZH",l="EN",h=l,c={},f={},p=r.Z.domSupported?function(){var t=(document.documentElement.lang||navigator.language||navigator.browserLanguage).toUpperCase();return t.indexOf(u)>-1?u:h}():h;function d(t,e){t=t.toUpperCase(),f[t]=new i.Z(e),c[t]=e}function v(t){if((0,s.HD)(t)){var e=c[t.toUpperCase()]||{};return t===u||t===l?(0,s.d9)(e):(0,s.TS)((0,s.d9)(e),(0,s.d9)(c[h]),!1)}return(0,s.TS)((0,s.d9)(t),(0,s.d9)(c[h]),!1)}function g(t){return f[t]}function y(){return f[h]}d(l,o),d(u,a)},8674:function(t,e,n){n.d(e,{v:function(){return r}});var i=n(33051);function r(t){return new o(t)}var o=function(){function t(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return t.prototype.perform=function(t){var e,n=this._upstream,r=t&&t.skip;if(this._dirty&&n){var o=this.context;o.data=o.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!r&&(e=this._plan(this.context));var a,s=c(this._modBy),u=this._modDataCount||0,l=c(t&&t.modBy),h=t&&t.modDataCount||0;function c(t){return!(t>=1)&&(t=1),t}s===l&&u===h||(e="reset"),(this._dirty||"reset"===e)&&(this._dirty=!1,a=this._doReset(r)),this._modBy=l,this._modDataCount=h;var f=t&&t.step;if(this._dueEnd=n?n._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var p=this._dueIndex,d=Math.min(null!=f?this._dueIndex+f:1/0,this._dueEnd);if(!r&&(a||p<d)){var v=this._progress;if((0,i.kJ)(v))for(var g=0;g<v.length;g++)this._doProgress(v[g],p,d,l,h);else this._doProgress(v,p,d,l,h)}this._dueIndex=d;var y=null!=this._settedOutputEnd?this._settedOutputEnd:d;0,this._outputDueEnd=y}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},t.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},t.prototype._doProgress=function(t,e,n,i,r){a.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:a.next},this.context)},t.prototype._doReset=function(t){var e,n;this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&(e=this._reset(this.context),e&&e.progress&&(n=e.forceFirstProgress,e=e.progress),(0,i.kJ)(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var r=this._downstream;return r&&r.dirty(),n},t.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},t.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},t.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},t.prototype.getUpstream=function(){return this._upstream},t.prototype.getDownstream=function(){return this._downstream},t.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},t}(),a=function(){var t,e,n,i,r,o={reset:function(u,l,h,c){e=u,t=l,n=h,i=c,r=Math.ceil(i/n),o.next=n>1&&i>0?s:a}};return o;function a(){return e<t?e++:null}function s(){var o=e%r*n+Math.ceil(e/r),a=e>=t?null:o<i?o:e;return e++,a}}()},4130:function(t,e){function n(t){return null==t?0:t.length||1}function i(t){return t}var r=function(){function t(t,e,n,r,o,a){this._old=t,this._new=e,this._oldKeyGetter=n||i,this._newKeyGetter=r||i,this.context=o,this._diffModeMultiple="multiple"===a}return t.prototype.add=function(t){return this._add=t,this},t.prototype.update=function(t){return this._update=t,this},t.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},t.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},t.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},t.prototype.remove=function(t){return this._remove=t,this},t.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},t.prototype._executeOneToOne=function(){var t=this._old,e=this._new,i={},r=new Array(t.length),o=new Array(e.length);this._initIndexMap(t,null,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<t.length;a++){var s=r[a],u=i[s],l=n(u);if(l>1){var h=u.shift();1===u.length&&(i[s]=u[0]),this._update&&this._update(h,a)}else 1===l?(i[s]=null,this._update&&this._update(u,a)):this._remove&&this._remove(a)}this._performRestAdd(o,i)},t.prototype._executeMultiple=function(){var t=this._old,e=this._new,i={},r={},o=[],a=[];this._initIndexMap(t,i,o,"_oldKeyGetter"),this._initIndexMap(e,r,a,"_newKeyGetter");for(var s=0;s<o.length;s++){var u=o[s],l=i[u],h=r[u],c=n(l),f=n(h);if(c>1&&1===f)this._updateManyToOne&&this._updateManyToOne(h,l),r[u]=null;else if(1===c&&f>1)this._updateOneToMany&&this._updateOneToMany(h,l),r[u]=null;else if(1===c&&1===f)this._update&&this._update(h,l),r[u]=null;else if(c>1&&f>1)this._updateManyToMany&&this._updateManyToMany(h,l),r[u]=null;else if(c>1)for(var p=0;p<c;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(a,r)},t.prototype._performRestAdd=function(t,e){for(var i=0;i<t.length;i++){var r=t[i],o=e[r],a=n(o);if(a>1)for(var s=0;s<a;s++)this._add&&this._add(o[s]);else 1===a&&this._add&&this._add(o);e[r]=null}},t.prototype._initIndexMap=function(t,e,i,r){for(var o=this._diffModeMultiple,a=0;a<t.length;a++){var s="_ec_"+this[r](t[a],a);if(o||(i[a]=s),e){var u=e[s],l=n(u);0===l?(e[s]=a,o&&i.push(s)):1===l?e[s]=[u,a]:u.push(a)}}},t}();e["Z"]=r},43834:function(t,e,n){n.d(e,{hG:function(){return h}});var i,r=n(33051),o=n(98407),a=n(99574),s="undefined",u=typeof Uint32Array===s?Array:Uint32Array,l=typeof Uint16Array===s?Array:Uint16Array,h=typeof Int32Array===s?Array:Int32Array,c=typeof Float64Array===s?Array:Float64Array,f={float:c,int:h,ordinal:Array,number:Array,time:c};function p(t){return t>65535?u:l}function d(){return[1/0,-1/0]}function v(t){var e=t.constructor;return e===Array?t.slice():new e(t)}function g(t,e,n,i,r){var o=f[n||"float"];if(r){var a=t[e],s=a&&a.length;if(s!==i){for(var u=new o(i),l=0;l<s;l++)u[l]=a[l];t[e]=u}}else t[e]=new o(i)}var y=function(){function t(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=(0,r.kW)()}return t.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var o=t.getSource(),s=this.defaultDimValueGetter=i[o.sourceFormat];this._dimValueGetter=n||s,this._rawExtent=[];(0,a.QY)(o);this._dimensions=(0,r.UI)(e,(function(t){return{type:t.type,property:t.property}})),this._initDataFromProvider(0,t.count())},t.prototype.getProvider=function(){return this._provider},t.prototype.getSource=function(){return this._provider.getSource()},t.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new f[e||"float"](this._rawCount),this._rawExtent[r]=d(),r},t.prototype.collectOrdinalMeta=function(t,e){var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length;0===o&&(r[t]=d());for(var s=r[t],u=o;u<a;u++){var l=n[u]=e.parseAndCollect(n[u]);isNaN(l)||(s[0]=Math.min(l,s[0]),s[1]=Math.max(l,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},t.prototype.getOrdinalMeta=function(t){var e=this._dimensions[t],n=e.ordinalMeta;return n},t.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},t.prototype.appendData=function(t){var e=this._provider,n=this.count();e.appendData(t);var i=e.count();return e.persistent||(i+=n),n<i&&this._initDataFromProvider(n,i,!0),[n,i]},t.prototype.appendValues=function(t,e){for(var n=this._chunks,r=this._dimensions,o=r.length,a=this._rawExtent,s=this.count(),u=s+Math.max(t.length,e||0),l=0;l<o;l++){var h=r[l];g(n,l,h.type,u,!0)}for(var c=[],f=s;f<u;f++)for(var p=f-s,d=0;d<o;d++){h=r[d];var v=i.arrayRows.call(this,t[p]||c,h.property,p,d);n[d][f]=v;var y=a[d];v<y[0]&&(y[0]=v),v>y[1]&&(y[1]=v)}return this._rawCount=this._count=u,{start:s,end:u}},t.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,o=this._chunks,a=this._dimensions,s=a.length,u=this._rawExtent,l=(0,r.UI)(a,(function(t){return t.property})),h=0;h<s;h++){var c=a[h];u[h]||(u[h]=d()),g(o,h,c.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,o,u);else for(var f=[],p=t;p<e;p++){f=i.getItem(p,f);for(var v=0;v<s;v++){var y=o[v],m=this._dimValueGetter(f,l[v],p,v);y[p]=m;var _=u[v];m<_[0]&&(_[0]=m),m>_[1]&&(_[1]=m)}}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},t.prototype.count=function(){return this._count},t.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._chunks[t];return n?n[this.getRawIndex(e)]:NaN},t.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;r=0;for(var o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},t.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._chunks[t];return n?n[e]:NaN},t.prototype.getSum=function(t){var e=this._chunks[t],n=0;if(e)for(var i=0,r=this.count();i<r;i++){var o=this.get(t,i);isNaN(o)||(n+=o)}return n},t.prototype.getMedian=function(t){var e=[];this.each([t],(function(t){isNaN(t)||e.push(t)}));var n=e.sort((function(t,e){return t-e})),i=this.count();return 0===i?0:i%2===1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},t.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;var i=0,r=this._count-1;while(i<=r){var o=(i+r)/2|0;if(e[o]<t)i=o+1;else{if(!(e[o]>t))return o;r=o-1}}return-1},t.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks,r=i[t],o=[];if(!r)return o;null==n&&(n=1/0);for(var a=1/0,s=-1,u=0,l=0,h=this.count();l<h;l++){var c=this.getRawIndex(l),f=e-r[c],p=Math.abs(f);p<=n&&((p<a||p===a&&f>=0&&s<0)&&(a=p,s=f,u=0),f===s&&(o[u++]=l))}return o.length=u,o},t.prototype.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,i=this._count;if(n===Array){t=new n(i);for(var r=0;r<i;r++)t[r]=e[r]}else t=new n(e.buffer,0,i)}else{n=p(this._rawCount);t=new n(this.count());for(r=0;r<t.length;r++)t[r]=r}return t},t.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=p(n._rawCount),o=new r(i),a=[],s=t.length,u=0,l=t[0],h=n._chunks,c=0;c<i;c++){var f=void 0,d=n.getRawIndex(c);if(0===s)f=e(c);else if(1===s){var v=h[l][d];f=e(v,c)}else{for(var g=0;g<s;g++)a[g]=h[t[g]][d];a[g]=c,f=e.apply(null,a)}f&&(o[u++]=d)}return u<i&&(n._indices=o),n._count=u,n._extent=[],n._updateGetRawIdx(),n},t.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=(0,r.XP)(t),o=i.length;if(!o)return this;var a=e.count(),s=p(e._rawCount),u=new s(a),l=0,h=i[0],c=t[h][0],f=t[h][1],d=e._chunks,v=!1;if(!e._indices){var g=0;if(1===o){for(var y=d[i[0]],m=0;m<n;m++){var _=y[m];(_>=c&&_<=f||isNaN(_))&&(u[l++]=g),g++}v=!0}else if(2===o){y=d[i[0]];var x=d[i[1]],w=t[i[1]][0],S=t[i[1]][1];for(m=0;m<n;m++){_=y[m];var b=x[m];(_>=c&&_<=f||isNaN(_))&&(b>=w&&b<=S||isNaN(b))&&(u[l++]=g),g++}v=!0}}if(!v)if(1===o)for(m=0;m<a;m++){var T=e.getRawIndex(m);_=d[i[0]][T];(_>=c&&_<=f||isNaN(_))&&(u[l++]=T)}else for(m=0;m<a;m++){for(var M=!0,k=(T=e.getRawIndex(m),0);k<o;k++){var D=i[k];_=d[D][T];(_<t[D][0]||_>t[D][1])&&(M=!1)}M&&(u[l++]=e.getRawIndex(m))}return l<a&&(e._indices=u),e._count=l,e._extent=[],e._updateGetRawIdx(),e},t.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},t.prototype.modify=function(t,e){this._updateDims(this,t,e)},t.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],u=t._rawExtent,l=0;l<e.length;l++)u[e[l]]=d();for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),f=0;f<o;f++)s[f]=i[e[f]][c];s[o]=h;var p=n&&n.apply(null,s);if(null!=p){"object"!==typeof p&&(r[0]=p,p=r);for(l=0;l<p.length;l++){var v=e[l],g=p[l],y=u[v],m=i[v];m&&(m[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},t.prototype.lttbDownSample=function(t,e){var n,i,r,o=this.clone([t],!0),a=o._chunks,s=a[t],u=this.count(),l=0,h=Math.floor(1/e),c=this.getRawIndex(0),f=new(p(this._rawCount))(Math.min(2*(Math.ceil(u/h)+2),u));f[l++]=c;for(var d=1;d<u-1;d+=h){for(var v=Math.min(d+h,u-1),g=Math.min(d+2*h,u),y=(g+v)/2,m=0,_=v;_<g;_++){var x=this.getRawIndex(_),w=s[x];isNaN(w)||(m+=w)}m/=g-v;var S=d,b=Math.min(d+h,u),T=d-1,M=s[c];n=-1,r=S;var k=-1,D=0;for(_=S;_<b;_++){x=this.getRawIndex(_),w=s[x];isNaN(w)?(D++,k<0&&(k=x)):(i=Math.abs((T-y)*(w-M)-(T-_)*(m-M)),i>n&&(n=i,r=x))}D>0&&D<b-S&&(f[l++]=Math.min(k,r),r=Math.max(k,r)),f[l++]=r,c=r}return f[l++]=this.getRawIndex(u-1),o._count=l,o._indices=f,o.getRawIndex=this._getRawIdx,o},t.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),u=o[t],l=this.count(),h=r._rawExtent[t]=d(),c=new(p(this._rawCount))(Math.ceil(l/s)),f=0,v=0;v<l;v+=s){s>l-v&&(s=l-v,a.length=s);for(var g=0;g<s;g++){var y=this.getRawIndex(v+g);a[g]=u[y]}var m=n(a),_=this.getRawIndex(Math.min(v+i(a,m)||0,l-1));u[_]=m,m<h[0]&&(h[0]=m),m>h[1]&&(h[1]=m),c[f++]=_}return r._count=f,r._indices=c,r._updateGetRawIdx(),r},t.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,u=[];s<n;s++)u[s]=i[t[s]][a];u[s]=r,e.apply(null,u)}}},t.prototype.getDataExtent=function(t){var e=this._chunks[t],n=d();if(!e)return n;var i,r=this.count(),o=!this._indices;if(o)return this._rawExtent[t].slice();if(i=this._extent[t],i)return i.slice();i=n;for(var a=i[0],s=i[1],u=0;u<r;u++){var l=this.getRawIndex(u),h=e[l];h<a&&(a=h),h>s&&(s=h)}return i=[a,s],this._extent[t]=i,i},t.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},t.prototype.clone=function(e,n){var i=new t,o=this._chunks,a=e&&(0,r.u4)(e,(function(t,e){return t[e]=!0,t}),{});if(a)for(var s=0;s<o.length;s++)i._chunks[s]=a[s]?v(o[s]):o[s];else i._chunks=o;return this._copyCommonProps(i),n||(i._indices=this._cloneIndices()),i._updateGetRawIdx(),i},t.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=(0,r.d9)(this._extent),t._rawExtent=(0,r.d9)(this._rawExtent)},t.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var n=this._indices.length;e=new t(n);for(var i=0;i<n;i++)e[i]=this._indices[i]}else e=new t(this._indices);return e}return null},t.prototype._getRawIdxIdentity=function(t){return t},t.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},t.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},t.internalField=function(){function t(t,e,n,i){return(0,o.yQ)(t[i],this._dimensions[i])}i={arrayRows:t,objectRows:function(t,e,n,i){return(0,o.yQ)(t[e],this._dimensions[i])},keyedColumns:t,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return(0,o.yQ)(r instanceof Array?r[i]:r,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}}}(),t}();e["ZP"]=y},51401:function(t,e,n){var i=n(33051),r=0,o=function(){function t(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++r}return t.createByAxisModel=function(e){var n=e.option,r=n.data,o=r&&(0,i.UI)(r,a);return new t({categories:o,needCollect:!o,deduplication:!1!==n.dedplication})},t.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},t.prototype.parseAndCollect=function(t){var e,n=this._needCollect;if(!(0,i.HD)(t)&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var r=this._getOrCreateMap();return e=r.get(t),null==e&&(n?(e=this.categories.length,this.categories[e]=t,r.set(t,e)):e=NaN),e},t.prototype._getOrCreateMap=function(){return this._map||(this._map=(0,i.kW)(this.categories))},t}();function a(t){return(0,i.Kn)(t)&&null!=t.value?t.value:t+""}e["Z"]=o},5101:function(t,e,n){var i,r,o,a,s,u,l,h=n(33051),c=n(12312),f=n(4130),p=n(68540),d=n(10381),v=n(35440),g=n(94279),y=n(32234),m=n(30106),_=n(99574),x=n(43834),w=n(31029),S=h.Kn,b=h.UI,T="undefined"===typeof Int32Array?Array:Int32Array,M="e\0\0",k=-1,D=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],C=["_approximateExtent"],I=function(){function t(t,e){var n;this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"];var i=!1;(0,w.bB)(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(i=!0,n=t),n=n||["x","y"];for(var r={},o=[],a={},s=!1,u={},l=0;l<n.length;l++){var c=n[l],f=h.HD(c)?new v.Z({name:c}):c instanceof v.Z?c:new v.Z(c),p=f.name;f.type=f.type||"float",f.coordDim||(f.coordDim=p,f.coordDimIndex=0);var d=f.otherDims=f.otherDims||{};o.push(p),r[p]=f,null!=u[p]&&(s=!0),f.createInvertedIndices&&(a[p]=[]),0===d.itemName&&(this._nameDimIdx=l),0===d.itemId&&(this._idDimIdx=l),i&&(f.storeDimIndex=l)}if(this.dimensions=o,this._dimInfos=r,this._initGetDimensionInfo(s),this.hostModel=e,this._invertedIndicesMap=a,this._dimOmitted){var g=this._dimIdxToName=h.kW();h.S6(o,(function(t){g.set(r[t].storeDimIndex,t)}))}}return t.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(null==e)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var n=this._dimIdxToName.get(e);if(null!=n)return n;var i=this._schema.getSourceDimension(e);return i?i.name:void 0},t.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(null!=e)return e;if(null==t)return-1;var n=this._getDimInfo(t);return n?n.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},t.prototype._recognizeDimIndex=function(t){if(h.hj(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},t.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},t.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},t.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},t.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},t.prototype.mapDimension=function(t,e){var n=this._dimSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return i?i[e]:null},t.prototype.mapDimensionsAll=function(t){var e=this._dimSummary,n=e.encode[t];return(n||[]).slice()},t.prototype.getStore=function(){return this._store},t.prototype.initData=function(t,e,n){var i,r=this;if(t instanceof x.ZP&&(i=t),!i){var o=this.dimensions,a=(0,_.Ld)(t)||h.zG(t)?new p.Pl(t,o.length):t;i=new x.ZP;var s=b(o,(function(t){return{type:r._dimInfos[t].type,property:t}}));i.initData(a,s,n)}this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=(0,d.y)(this,this._schema),this.userOutput=this._dimSummary.userOutput},t.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},t.prototype.appendValues=function(t,e){var n=this._store.appendValues(t,e.length),i=n.start,r=n.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var a=i;a<r;a++){var s=a-i;this._nameList[a]=e[s],o&&l(this,a)}},t.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},t.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==g.J5&&!t.fillStorage},t.prototype._doInit=function(t,e){if(!(t>=e)){var n=this._store,r=n.getProvider();this._updateOrdinalMeta();var o=this._nameList,a=this._idList,s=r.getSource().sourceFormat,u=s===g.cy;if(u&&!r.pure)for(var h=[],c=t;c<e;c++){var f=r.getItem(c,h);if(!this.hasItemOption&&(0,y.Co)(f)&&(this.hasItemOption=!0),f){var p=f.name;null==o[c]&&null!=p&&(o[c]=(0,y.U5)(p,null));var d=f.id;null==a[c]&&null!=d&&(a[c]=(0,y.U5)(d,null))}}if(this._shouldMakeIdFromName())for(c=t;c<e;c++)l(this,c);i(this)}},t.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},t.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},t.prototype.setCalculationInfo=function(t,e){S(t)?h.l7(this._calculationInfo,t):this._calculationInfo[t]=e},t.prototype.getName=function(t){var e=this.getRawIndex(t),n=this._nameList[e];return null==n&&null!=this._nameDimIdx&&(n=o(this,this._nameDimIdx,e)),null==n&&(n=""),n},t.prototype._getCategory=function(t,e){var n=this._store.get(t,e),i=this._store.getOrdinalMeta(t);return i?i.categories[n]:n},t.prototype.getId=function(t){return r(this,this.getRawIndex(t))},t.prototype.count=function(){return this._store.count()},t.prototype.get=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.get(i.storeDimIndex,e)},t.prototype.getByRawIndex=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.getByRawIndex(i.storeDimIndex,e)},t.prototype.getIndices=function(){return this._store.getIndices()},t.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},t.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},t.prototype.getValues=function(t,e){var n=this,i=this._store;return h.kJ(t)?i.getValues(b(t,(function(t){return n._getStoreDimIndex(t)})),e):i.getValues(t)},t.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},t.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},t.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},t.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},t.prototype.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t];var i=n[e];return null==i||isNaN(i)?k:i},t.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},t.prototype.each=function(t,e,n){h.mf(t)&&(n=e,e=t,t=[]);var i=n||this,r=b(a(t),this._getStoreDimIndex,this);this._store.each(r,i?h.ak(e,i):e)},t.prototype.filterSelf=function(t,e,n){h.mf(t)&&(n=e,e=t,t=[]);var i=n||this,r=b(a(t),this._getStoreDimIndex,this);return this._store=this._store.filter(r,i?h.ak(e,i):e),this},t.prototype.selectRange=function(t){var e=this,n={},i=h.XP(t),r=[];return h.S6(i,(function(i){var o=e._getStoreDimIndex(i);n[o]=t[i],r.push(o)})),this._store=this._store.selectRange(n),this},t.prototype.mapArray=function(t,e,n){h.mf(t)&&(n=e,e=t,t=[]),n=n||this;var i=[];return this.each(t,(function(){i.push(e&&e.apply(this,arguments))}),n),i},t.prototype.map=function(t,e,n,i){var r=n||i||this,o=b(a(t),this._getStoreDimIndex,this),s=u(this);return s._store=this._store.map(o,r?h.ak(e,r):e),s},t.prototype.modify=function(t,e,n,i){var r=n||i||this;var o=b(a(t),this._getStoreDimIndex,this);this._store.modify(o,r?h.ak(e,r):e)},t.prototype.downSample=function(t,e,n,i){var r=u(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},t.prototype.lttbDownSample=function(t,e){var n=u(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},t.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},t.prototype.getItemModel=function(t){var e=this.hostModel,n=this.getRawDataItem(t);return new c.Z(n,e,e&&e.ecModel)},t.prototype.diff=function(t){var e=this;return new f.Z(t?t.getStore().getIndices():[],this.getStore().getIndices(),(function(e){return r(t,e)}),(function(t){return r(e,t)}))},t.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},t.prototype.setVisual=function(t,e){this._visual=this._visual||{},S(t)?h.l7(this._visual,t):this._visual[t]=e},t.prototype.getItemVisual=function(t,e){var n=this._itemVisuals[t],i=n&&n[e];return null==i?this.getVisual(e):i},t.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},t.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t];i||(i=n[t]={});var r=i[e];return null==r&&(r=this.getVisual(e),h.kJ(r)?r=r.slice():S(r)&&(r=h.l7({},r)),i[e]=r),r},t.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,S(e)?h.l7(i,e):i[e]=n},t.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},t.prototype.setLayout=function(t,e){S(t)?h.l7(this._layout,t):this._layout[t]=e},t.prototype.getLayout=function(t){return this._layout[t]},t.prototype.getItemLayout=function(t){return this._itemLayouts[t]},t.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?h.l7(this._itemLayouts[t]||{},e):e},t.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},t.prototype.setItemGraphicEl=function(t,e){var n=this.hostModel&&this.hostModel.seriesIndex;(0,m.Q)(n,this.dataType,t,e),this._graphicEls[t]=e},t.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},t.prototype.eachItemGraphicEl=function(t,e){h.S6(this._graphicEls,(function(n,i){n&&t&&t.call(e,n,i)}))},t.prototype.cloneShallow=function(e){return e||(e=new t(this._schema?this._schema:b(this.dimensions,this._getDimInfo,this),this.hostModel)),s(e,this),e._store=this._store,e},t.prototype.wrapMethod=function(t,e){var n=this[t];h.mf(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(h.tP(arguments)))})},t.internalField=function(){i=function(t){var e=t._invertedIndicesMap;h.S6(e,(function(n,i){var r=t._dimInfos[i],o=r.ordinalMeta,a=t._store;if(o){n=e[i]=new T(o.categories.length);for(var s=0;s<n.length;s++)n[s]=k;for(s=0;s<a.count();s++)n[a.get(r.storeDimIndex,s)]=s}}))},o=function(t,e,n){return(0,y.U5)(t._getCategory(e,n),null)},r=function(t,e){var n=t._idList[e];return null==n&&null!=t._idDimIdx&&(n=o(t,t._idDimIdx,e)),null==n&&(n=M+e),n},a=function(t){return h.kJ(t)||(t=null!=t?[t]:[]),t},u=function(e){var n=new t(e._schema?e._schema:b(e.dimensions,e._getDimInfo,e),e.hostModel);return s(n,e),n},s=function(t,e){h.S6(D.concat(e.__wrappedMethods||[]),(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t.__wrappedMethods=e.__wrappedMethods,h.S6(C,(function(n){t[n]=h.d9(e[n])})),t._calculationInfo=h.l7({},e._calculationInfo)},l=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,a=t._idDimIdx,s=n[e],u=i[e];if(null==s&&null!=r&&(n[e]=s=o(t,r,e)),null==u&&null!=a&&(i[e]=u=o(t,a,e)),null==u&&null!=s){var l=t._nameRepeatCount,h=l[s]=(l[s]||0)+1;u=s,h>1&&(u+="__ec__"+h),i[e]=u}}}(),t}();e["Z"]=I},35440:function(t,e,n){var i=n(33051),r=function(){function t(t){this.otherDims={},null!=t&&i.l7(this,t)}return t}();e["Z"]=r},99574:function(t,e,n){n.d(e,{Kp:function(){return f},Ld:function(){return u},ML:function(){return c},QY:function(){return y},_P:function(){return l},nx:function(){return h}});var i=n(33051),r=n(94279),o=n(32234),a=n(61772),s=function(){function t(t){this.data=t.data||(t.sourceFormat===r.hL?{}:[]),this.sourceFormat=t.sourceFormat||r.RA,this.seriesLayoutBy=t.seriesLayoutBy||r.fY,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&(0,a.u7)(this,n)===a.Dq.Must&&(i.type="ordinal")}}return t}();function u(t){return t instanceof s}function l(t,e,n){n=n||f(t);var r=e.seriesLayoutBy,o=p(t,n,r,e.sourceHeader,e.dimensions),a=new s({data:t,sourceFormat:n,seriesLayoutBy:r,dimensionsDefine:o.dimensionsDefine,startIndex:o.startIndex,dimensionsDetectedCount:o.dimensionsDetectedCount,metaRawOption:(0,i.d9)(e)});return a}function h(t){return new s({data:t,sourceFormat:(0,i.fU)(t)?r.J5:r.cy})}function c(t){return new s({data:t.data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:(0,i.d9)(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})}function f(t){var e=r.RA;if((0,i.fU)(t))e=r.J5;else if((0,i.kJ)(t)){0===t.length&&(e=r.XD);for(var n=0,o=t.length;n<o;n++){var a=t[n];if(null!=a){if((0,i.kJ)(a)){e=r.XD;break}if((0,i.Kn)(a)){e=r.qb;break}}}}else if((0,i.Kn)(t))for(var s in t)if((0,i.RI)(t,s)&&(0,i.zG)(t[s])){e=r.hL;break}return e}function p(t,e,n,a,s){var u,l;if(!t)return{dimensionsDefine:v(s),startIndex:l,dimensionsDetectedCount:u};if(e===r.XD){var h=t;"auto"===a||null==a?g((function(t){null!=t&&"-"!==t&&((0,i.HD)(t)?null==l&&(l=1):l=0)}),n,h,10):l=(0,i.hj)(a)?a:a?1:0,s||1!==l||(s=[],g((function(t,e){s[e]=null!=t?t+"":""}),n,h,1/0)),u=s?s.length:n===r.Wc?h.length:h[0]?h[0].length:null}else if(e===r.qb)s||(s=d(t));else if(e===r.hL)s||(s=[],(0,i.S6)(t,(function(t,e){s.push(e)})));else if(e===r.cy){var c=(0,o.C4)(t[0]);u=(0,i.kJ)(c)&&c.length||1}else r.J5;return{startIndex:l,dimensionsDefine:v(s),dimensionsDetectedCount:u}}function d(t){var e,n=0;while(n<t.length&&!(e=t[n++]));if(e){var r=[];return(0,i.S6)(e,(function(t,e){r.push(e)})),r}}function v(t){if(t){var e=(0,i.kW)();return(0,i.UI)(t,(function(t,n){t=(0,i.Kn)(t)?t:{name:t};var r={name:t.name,displayName:t.displayName,type:t.type};if(null==r.name)return r;r.name+="",null==r.displayName&&(r.displayName=r.name);var o=e.get(r.name);return o?r.name+="-"+o.count++:e.set(r.name,{count:1}),r}))}}function g(t,e,n,i){if(e===r.Wc)for(var o=0;o<n.length&&o<i;o++)t(n[o]?n[o][0]:null,o);else{var a=n[0]||[];for(o=0;o<a.length&&o<i;o++)t(a[o],o)}}function y(t){var e=t.sourceFormat;return e===r.qb||e===r.hL}},31029:function(t,e,n){n.d(e,{Eo:function(){return u},Jj:function(){return c},Jl:function(){return f},bB:function(){return l},v5:function(){return h}});var i=n(33051),r=n(32234),o=n(99574),a=(0,r.Yf)(),s={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},u=function(){function t(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return t.prototype.isDimensionOmitted=function(){return this._dimOmitted},t.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=c(this.source)))},t.prototype.getSourceDimensionIndex=function(t){return(0,i.pD)(this._dimNameMap.get(t),-1)},t.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},t.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=(0,o.QY)(this.source),n=!f(t),i="",r=[],a=0,u=0;a<t;a++){var l=void 0,h=void 0,c=void 0,p=this.dimensions[u];if(p&&p.storeDimIndex===a)l=e?p.name:null,h=p.type,c=p.ordinalMeta,u++;else{var d=this.getSourceDimension(a);d&&(l=e?d.name:null,h=d.type)}r.push({property:l,type:h,ordinalMeta:c}),!e||null==l||p&&p.isCalculationCoord||(i+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),i+="$",i+=s[h]||"f",c&&(i+=c.uid),i+="$"}var v=this.source,g=[v.seriesLayoutBy,v.startIndex,i].join("$$");return{dimensions:r,hash:g}},t.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];if(r&&r.storeDimIndex===e)r.isCalculationCoord||(i=r.name),n++;else{var o=this.getSourceDimension(e);o&&(i=o.name)}t.push(i)}return t},t.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},t}();function l(t){return t instanceof u}function h(t){for(var e=(0,i.kW)(),n=0;n<(t||[]).length;n++){var r=t[n],o=(0,i.Kn)(r)?r.name:r;null!=o&&null==e.get(o)&&e.set(o,n)}return e}function c(t){var e=a(t);return e.dimNameMap||(e.dimNameMap=h(t.dimensionsDefine))}function f(t){return t>30}},55623:function(t,e,n){n.d(e,{Z:function(){return f},q:function(){return c}});var i=n(94279),r=n(35440),o=n(33051),a=n(99574),s=n(43834),u=n(32234),l=n(61772),h=n(31029);function c(t,e){return f(t,e).dimensions}function f(t,e){(0,a.Ld)(t)||(t=(0,a.nx)(t)),e=e||{};var n=e.coordDimensions||[],c=e.dimensionsDefine||t.dimensionsDefine||[],f=(0,o.kW)(),g=[],y=d(t,n,c,e.dimensionsCount),m=e.canOmitUnusedDimensions&&(0,h.Jl)(y),_=c===t.dimensionsDefine,x=_?(0,h.Jj)(t):(0,h.v5)(c),w=e.encodeDefine;!w&&e.encodeDefaulter&&(w=e.encodeDefaulter(t,y));for(var S=(0,o.kW)(w),b=new s.hG(y),T=0;T<b.length;T++)b[T]=-1;function M(t){var e=b[t];if(e<0){var n=c[t],i=(0,o.Kn)(n)?n:{name:n},a=new r.Z,s=i.name;null!=s&&null!=x.get(s)&&(a.name=a.displayName=s),null!=i.type&&(a.type=i.type),null!=i.displayName&&(a.displayName=i.displayName);var u=g.length;return b[t]=u,a.storeDimIndex=t,g.push(a),a}return g[e]}if(!m)for(T=0;T<y;T++)M(T);S.each((function(t,e){var n=(0,u.kF)(t).slice();if(1===n.length&&!(0,o.HD)(n[0])&&n[0]<0)S.set(e,!1);else{var i=S.set(e,[]);(0,o.S6)(n,(function(t,n){var r=(0,o.HD)(t)?x.get(t):t;null!=r&&r<y&&(i[n]=r,D(M(r),e,n))}))}}));var k=0;function D(t,e,n){null!=i.f7.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,f.set(e,!0))}(0,o.S6)(n,(function(t){var e,n,i,r;if((0,o.HD)(t))e=t,r={};else{r=t,e=r.name;var a=r.ordinalMeta;r.ordinalMeta=null,r=(0,o.l7)({},r),r.ordinalMeta=a,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null}var s=S.get(e);if(!1!==s){if(s=(0,u.kF)(s),!s.length)for(var l=0;l<(n&&n.length||1);l++){while(k<y&&null!=M(k).coordDim)k++;k<y&&s.push(k++)}(0,o.S6)(s,(function(t,a){var s=M(t);if(_&&null!=r.type&&(s.type=r.type),D((0,o.ce)(s,r),e,a),null==s.name&&n){var u=n[a];!(0,o.Kn)(u)&&(u={name:u}),s.name=s.displayName=u.name,s.defaultTooltip=u.defaultTooltip}i&&(0,o.ce)(s.otherDims,i)}))}}));var C=e.generateCoord,I=e.generateCoordCount,P=null!=I;I=C?I||1:0;var A=C||"value";function O(t){null==t.name&&(t.name=t.coordDim)}if(m)(0,o.S6)(g,(function(t){O(t)})),g.sort((function(t,e){return t.storeDimIndex-e.storeDimIndex}));else for(var Z=0;Z<y;Z++){var L=M(Z),R=L.coordDim;null==R&&(L.coordDim=v(A,f,P),L.coordDimIndex=0,(!C||I<=0)&&(L.isExtraCoord=!0),I--),O(L),null!=L.type||(0,l.u7)(t,Z)!==l.Dq.Must&&(!L.isExtraCoord||null==L.otherDims.itemName&&null==L.otherDims.seriesName)||(L.type="ordinal")}return p(g),new h.Eo({source:t,dimensions:g,fullDimensionCount:y,dimensionOmitted:m})}function p(t){for(var e=(0,o.kW)(),n=0;n<t.length;n++){var i=t[n],r=i.name,a=e.get(r)||0;a>0&&(i.name=r+(a-1)),a++,e.set(r,a)}}function d(t,e,n,i){var r=Math.max(t.dimensionsDetectedCount||1,e.length,n.length,i||0);return(0,o.S6)(e,(function(t){var e;(0,o.Kn)(t)&&(e=t.dimsDef)&&(r=Math.max(r,e.length))})),r}function v(t,e,n){var i=e.data;if(n||i.hasOwnProperty(t)){var r=0;while(i.hasOwnProperty(t+r))r++;t+=r}return e.set(t,!0),t}},68540:function(t,e,n){n.d(e,{Pl:function(){return f},_j:function(){return v},a:function(){return m},hk:function(){return b},tB:function(){return w}});var i,r,o,a,s,u=n(33051),l=n(32234),h=n(99574),c=n(94279),f=function(){function t(t,e){var n=(0,h.Ld)(t)?t:(0,h.nx)(t);this._source=n;var i=this._data=n.data;n.sourceFormat===c.J5&&(this._offset=0,this._dimSize=e,this._data=i),s(this,i,n)}return t.prototype.getSource=function(){return this._source},t.prototype.count=function(){return 0},t.prototype.getItem=function(t,e){},t.prototype.appendData=function(t){},t.prototype.clean=function(){},t.protoInitialize=function(){var e=t.prototype;e.pure=!1,e.persistent=!0}(),t.internalField=function(){var t;s=function(t,r,o){var s=o.sourceFormat,l=o.seriesLayoutBy,h=o.startIndex,f=o.dimensionsDefine,p=a[S(s,l)];if((0,u.l7)(t,p),s===c.J5)t.getItem=e,t.count=i,t.fillStorage=n;else{var d=v(s,l);t.getItem=(0,u.ak)(d,null,r,h,f);var g=m(s,l);t.count=(0,u.ak)(g,null,r,h,f)}};var e=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},n=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],u=null==s[0]?1/0:s[0],l=null==s[1]?-1/0:s[1],h=e-t,c=n[a],f=0;f<h;f++){var p=r[f*o+a];c[t+f]=p,p<u&&(u=p),p>l&&(l=p)}s[0]=u,s[1]=l}},i=function(){return this._data?this._data.length/this._dimSize:0};function r(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}t={},t[c.XD+"_"+c.fY]={pure:!0,appendData:r},t[c.XD+"_"+c.Wc]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[c.qb]={pure:!0,appendData:r},t[c.hL]={pure:!0,appendData:function(t){var e=this._data;(0,u.S6)(t,(function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])}))}},t[c.cy]={appendData:r},t[c.J5]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},a=t}(),t}(),p=function(t,e,n,i){return t[i]},d=(i={},i[c.XD+"_"+c.fY]=function(t,e,n,i){return t[i+e]},i[c.XD+"_"+c.Wc]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var u=a[s];o[s]=u?u[i]:null}return o},i[c.qb]=p,i[c.hL]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=n[a].name;0;var u=t[s];o[a]=u?u[i]:null}return o},i[c.cy]=p,i);function v(t,e){var n=d[S(t,e)];return n}var g=function(t,e,n){return t.length},y=(r={},r[c.XD+"_"+c.fY]=function(t,e,n){return Math.max(0,t.length-e)},r[c.XD+"_"+c.Wc]=function(t,e,n){var i=t[0];return i?Math.max(0,i.length-e):0},r[c.qb]=g,r[c.hL]=function(t,e,n){var i=n[0].name;var r=t[i];return r?r.length:0},r[c.cy]=g,r);function m(t,e){var n=y[S(t,e)];return n}var _=function(t,e,n){return t[e]},x=(o={},o[c.XD]=_,o[c.qb]=function(t,e,n){return t[n]},o[c.hL]=_,o[c.cy]=function(t,e,n){var i=(0,l.C4)(t);return i instanceof Array?i[e]:i},o[c.J5]=_,o);function w(t){var e=x[t];return e}function S(t,e){return t===c.XD?t+"_"+e:t}function b(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r=t.getStore(),o=r.getSource().sourceFormat;if(null!=n){var a=t.getDimensionIndex(n),s=r.getDimensionProperty(a);return w(o)(i,a,s)}var u=i;return o===c.cy&&(u=(0,l.C4)(i)),u}}}},99936:function(t,e,n){n.d(e,{BM:function(){return o},IR:function(){return u},M:function(){return s}});var i=n(33051),r=n(31029);function o(t,e,n){n=n||{};var r,o,s,u=n.byIndex,l=n.stackedCoordDimension;a(e)?r=e:(o=e.schema,r=o.dimensions,s=e.store);var h,c,f,p,d=!(!t||!t.get("stack"));if((0,i.S6)(r,(function(t,e){(0,i.HD)(t)&&(r[e]=t={name:t}),d&&!t.isExtraCoord&&(u||h||!t.ordinalMeta||(h=t),c||"ordinal"===t.type||"time"===t.type||l&&l!==t.coordDim||(c=t))})),!c||u||h||(u=!0),c){f="__\0ecstackresult_"+t.id,p="__\0ecstackedover_"+t.id,h&&(h.createInvertedIndices=!0);var v=c.coordDim,g=c.type,y=0;(0,i.S6)(r,(function(t){t.coordDim===v&&y++}));var m={name:f,coordDim:v,coordDimIndex:y,type:g,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:r.length},_={name:p,coordDim:p,coordDimIndex:y+1,type:g,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:r.length+1};o?(s&&(m.storeDimIndex=s.ensureCalculationDimension(p,g),_.storeDimIndex=s.ensureCalculationDimension(f,g)),o.appendCalculationDimension(m),o.appendCalculationDimension(_)):(r.push(m),r.push(_))}return{stackedDimension:c&&c.name,stackedByDimension:h&&h.name,isStackedByIndex:u,stackedOverDimension:p,stackResultDimension:f}}function a(t){return!(0,r.bB)(t.schema)}function s(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function u(t,e){return s(t,e)?t.getCalculationInfo("stackResultDimension"):e}},98407:function(t,e,n){n.d(e,{ID:function(){return c},o2:function(){return u},tR:function(){return p},yQ:function(){return a}});var i=n(85669),r=n(33051),o=n(70175);function a(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"!==n||(0,r.hj)(t)||null==t||"-"===t||(t=+(0,i.sG)(t)),null==t||""===t?NaN:+t)}var s=(0,r.kW)({number:function(t){return parseFloat(t)},time:function(t){return+(0,i.sG)(t)},trim:function(t){return(0,r.HD)(t)?(0,r.fy)(t):t}});function u(t){return s.get(t)}var l={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return t>e},gte:function(t,e){return t>=e}},h=function(){function t(t,e){if(!(0,r.hj)(e)){var n="";0,(0,o._y)(n)}this._opFn=l[t],this._rvalFloat=(0,i.FK)(e)}return t.prototype.evaluate=function(t){return(0,r.hj)(t)?this._opFn(t,this._rvalFloat):this._opFn((0,i.FK)(t),this._rvalFloat)},t}(),c=function(){function t(t,e){var n="desc"===t;this._resultLT=n?1:-1,null==e&&(e=n?"min":"max"),this._incomparable="min"===e?-1/0:1/0}return t.prototype.evaluate=function(t,e){var n=(0,r.hj)(t)?t:(0,i.FK)(t),o=(0,r.hj)(e)?e:(0,i.FK)(e),a=isNaN(n),s=isNaN(o);if(a&&(n=this._incomparable),s&&(o=this._incomparable),a&&s){var u=(0,r.HD)(t),l=(0,r.HD)(e);u&&(n=l?t:0),l&&(o=u?e:0)}return n<o?this._resultLT:n>o?-this._resultLT:0},t}(),f=function(){function t(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=(0,i.FK)(e)}return t.prototype.evaluate=function(t){var e=t===this._rval;if(!e){var n=typeof t;n===this._rvalTypeof||"number"!==n&&"number"!==this._rvalTypeof||(e=(0,i.FK)(t)===this._rvalFloat)}return this._isEQ?e:!e},t}();function p(t,e){return"eq"===t||"ne"===t?new f("eq"===t,e):(0,r.RI)(l,t)?new h(t,e):null}},10381:function(t,e,n){n.d(e,{T:function(){return u},y:function(){return a}});var i=n(33051),r=n(94279),o=function(){function t(t,e){this._encode=t,this._schema=e}return t.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},t.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},t}();function a(t,e){var n={},a=n.encode={},u=(0,i.kW)(),h=[],c=[],f={};(0,i.S6)(t.dimensions,(function(e){var n=t.getDimensionInfo(e),i=n.coordDim;if(i){0;var o=n.coordDimIndex;s(a,i)[o]=e,n.isExtraCoord||(u.set(i,1),l(n.type)&&(h[0]=e),s(f,i)[o]=t.getDimensionIndex(n.name)),n.defaultTooltip&&c.push(e)}r.f7.each((function(t,e){var i=s(a,e),r=n.otherDims[e];null!=r&&!1!==r&&(i[r]=n.name)}))}));var p=[],d={};u.each((function(t,e){var n=a[e];d[e]=n[0],p=p.concat(n)})),n.dataDimsOnCoord=p,n.dataDimIndicesOnCoord=(0,i.UI)(p,(function(e){return t.getDimensionInfo(e).storeDimIndex})),n.encodeFirstDimNotExtra=d;var v=a.label;v&&v.length&&(h=v.slice());var g=a.tooltip;return g&&g.length?c=g.slice():c.length||(c=h.slice()),a.defaultedLabel=h,a.defaultedTooltip=c,n.userOutput=new o(f,e),n}function s(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function u(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function l(t){return!("ordinal"===t||"time"===t)}},61772:function(t,e,n){n.d(e,{Dq:function(){return a},JT:function(){return f},Ss:function(){return h},Wd:function(){return c},md:function(){return u},pY:function(){return l},u7:function(){return p}});var i=n(32234),r=n(33051),o=n(94279),a={Must:1,Might:2,Not:3},s=(0,i.Yf)();function u(t){s(t).datasetMap=(0,r.kW)()}function l(t,e,n){var i={},o=c(e);if(!o||!t)return i;var a,u,l=[],h=[],f=e.ecModel,p=s(f).datasetMap,d=o.uid+"_"+n.seriesLayoutBy;t=t.slice(),(0,r.S6)(t,(function(e,n){var o=(0,r.Kn)(e)?e:t[n]={name:e};"ordinal"===o.type&&null==a&&(a=n,u=y(o)),i[o.name]=[]}));var v=p.get(d)||p.set(d,{categoryWayDim:u,valueWayDim:0});function g(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function y(t){var e=t.dimsDef;return e?e.length:1}return(0,r.S6)(t,(function(t,e){var n=t.name,r=y(t);if(null==a){var o=v.valueWayDim;g(i[n],o,r),g(h,o,r),v.valueWayDim+=r}else if(a===e)g(i[n],0,r),g(l,0,r);else{o=v.categoryWayDim;g(i[n],o,r),g(h,o,r),v.categoryWayDim+=r}})),l.length&&(i.itemName=l),h.length&&(i.seriesName=h),i}function h(t,e,n){var i={},s=c(t);if(!s)return i;var u,l=e.sourceFormat,h=e.dimensionsDefine;l!==o.qb&&l!==o.hL||(0,r.S6)(h,(function(t,e){"name"===((0,r.Kn)(t)?t.name:t)&&(u=e)}));var f=function(){for(var t={},i={},r=[],o=0,s=Math.min(5,n);o<s;o++){var c=d(e.data,l,e.seriesLayoutBy,h,e.startIndex,o);r.push(c);var f=c===a.Not;if(f&&null==t.v&&o!==u&&(t.v=o),(null==t.n||t.n===t.v||!f&&r[t.n]===a.Not)&&(t.n=o),p(t)&&r[t.n]!==a.Not)return t;f||(c===a.Might&&null==i.v&&o!==u&&(i.v=o),null!=i.n&&i.n!==i.v||(i.n=o))}function p(t){return null!=t.v&&null!=t.n}return p(t)?t:p(i)?i:null}();if(f){i.value=[f.v];var p=null!=u?u:f.n;i.itemName=[p],i.seriesName=[p]}return i}function c(t){var e=t.get("data",!0);if(!e)return(0,i.HZ)(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},i.C6).models[0]}function f(t){return t.get("transform",!0)||t.get("fromTransformResult",!0)?(0,i.HZ)(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},i.C6).models:[]}function p(t,e){return d(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function d(t,e,n,s,u,l){var h,c,f,p=5;if((0,r.fU)(t))return a.Not;if(s){var d=s[l];(0,r.Kn)(d)?(c=d.name,f=d.type):(0,r.HD)(d)&&(c=d)}if(null!=f)return"ordinal"===f?a.Must:a.Not;if(e===o.XD){var v=t;if(n===o.Wc){for(var g=v[l],y=0;y<(g||[]).length&&y<p;y++)if(null!=(h=T(g[u+y])))return h}else for(y=0;y<v.length&&y<p;y++){var m=v[u+y];if(m&&null!=(h=T(m[l])))return h}}else if(e===o.qb){var _=t;if(!c)return a.Not;for(y=0;y<_.length&&y<p;y++){var x=_[y];if(x&&null!=(h=T(x[c])))return h}}else if(e===o.hL){var w=t;if(!c)return a.Not;g=w[c];if(!g||(0,r.fU)(g))return a.Not;for(y=0;y<g.length&&y<p;y++)if(null!=(h=T(g[y])))return h}else if(e===o.cy){var S=t;for(y=0;y<S.length&&y<p;y++){x=S[y];var b=(0,i.C4)(x);if(!(0,r.kJ)(b))return a.Not;if(null!=(h=T(b[l])))return h}}function T(t){var e=(0,r.HD)(t);return null!=t&&isFinite(t)&&""!==t?e?a.Might:a.Not:e&&"-"!==t?a.Must:void 0}return a.Not}},36437:function(t,e,n){n.d(e,{U:function(){return h},t:function(){return c}});var i=n(33051),r=n(99574),o=n(94279),a=n(61772),s=n(10437),u=n(43834),l=n(68540),h=function(){function t(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return t.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},t.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},t.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},t.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},t.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n=this._sourceHost,a=this._getUpstreamSourceManagers(),s=!!a.length;if(f(n)){var u=n,l=void 0,h=void 0,c=void 0;if(s){var p=a[0];p.prepareSource(),c=p.getSource(),l=c.data,h=c.sourceFormat,e=[p._getVersionSign()]}else l=u.get("data",!0),h=(0,i.fU)(l)?o.J5:o.cy,e=[];var d=this._getSourceMetaRawOption()||{},v=c&&c.metaRawOption||{},g=(0,i.pD)(d.seriesLayoutBy,v.seriesLayoutBy)||null,y=(0,i.pD)(d.sourceHeader,v.sourceHeader),m=(0,i.pD)(d.dimensions,v.dimensions),_=g!==v.seriesLayoutBy||!!y!==!!v.sourceHeader||m;t=_?[(0,r._P)(l,{seriesLayoutBy:g,sourceHeader:y,dimensions:m},h)]:[]}else{var x=n;if(s){var w=this._applyTransform(a);t=w.sourceList,e=w.upstreamSignList}else{var S=x.get("source",!0);t=[(0,r._P)(S,this._getSourceMetaRawOption(),null)],e=[]}}this._setLocalSource(t,e)},t.prototype._applyTransform=function(t){var e,n=this._sourceHost,o=n.get("transform",!0),a=n.get("fromTransformResult",!0);if(null!=a){var u="";1!==t.length&&p(u)}var l=[],h=[];return(0,i.S6)(t,(function(t){t.prepareSource();var e=t.getSource(a||0),n="";null==a||e||p(n),l.push(e),h.push(t._getVersionSign())})),o?e=(0,s.vK)(o,l,{datasetIndex:n.componentIndex}):null!=a&&(e=[(0,r.ML)(l[0])]),{sourceList:e,upstreamSignList:h}},t.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},t.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return e},t.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},t.prototype._innerGetDataStore=function(t,e,n){var i=0,r=this._storeList,o=r[i];o||(o=r[i]={});var a=o[n];if(!a){var s=this._getUpstreamSourceManagers()[0];f(this._sourceHost)&&s?a=s._innerGetDataStore(t,e,n):(a=new u.ZP,a.initData(new l.Pl(e,t.length),t)),o[n]=a}return a},t.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(f(t)){var e=(0,a.Wd)(t);return e?[e.getSourceManager()]:[]}return(0,i.UI)((0,a.JT)(t),(function(t){return t.getSourceManager()}))},t.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;if(f(i))t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var r=i;t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0)}return{seriesLayoutBy:t,sourceHeader:e,dimensions:n}},t}();function c(t){var e=t.option.transform;e&&(0,i.s7)(t.option.transform)}function f(t){return"series"===t.mainType}function p(t){throw new Error(t)}},10437:function(t,e,n){n.d(e,{DA:function(){return y},vK:function(){return m}});var i=n(94279),r=n(32234),o=n(33051),a=n(68540),s=n(98407),u=n(70175),l=n(99574),h=function(){function t(){}return t.prototype.getRawData=function(){throw new Error("not supported")},t.prototype.getRawDataItem=function(t){throw new Error("not supported")},t.prototype.cloneRawData=function(){},t.prototype.getDimensionInfo=function(t){},t.prototype.cloneAllDimensionInfo=function(){},t.prototype.count=function(){},t.prototype.retrieveValue=function(t,e){},t.prototype.retrieveValueFromItem=function(t,e){},t.prototype.convertValue=function(t,e){return(0,s.yQ)(t,e)},t}();function c(t,e){var n=new h,r=t.data,s=n.sourceFormat=t.sourceFormat,l=t.startIndex,c="";t.seriesLayoutBy!==i.fY&&(0,u._y)(c);var g=[],y={},m=t.dimensionsDefine;if(m)(0,o.S6)(m,(function(t,e){var n=t.name,i={index:e,name:n,displayName:t.displayName};if(g.push(i),null!=n){var r="";(0,o.RI)(y,n)&&(0,u._y)(r),y[n]=i}}));else for(var _=0;_<t.dimensionsDetectedCount;_++)g.push({index:_});var x=(0,a._j)(s,i.fY);e.__isBuiltIn&&(n.getRawDataItem=function(t){return x(r,l,g,t)},n.getRawData=(0,o.ak)(f,null,t)),n.cloneRawData=(0,o.ak)(p,null,t);var w=(0,a.a)(s,i.fY);n.count=(0,o.ak)(w,null,r,l,g);var S=(0,a.tB)(s);n.retrieveValue=function(t,e){var n=x(r,l,g,t);return b(n,e)};var b=n.retrieveValueFromItem=function(t,e){if(null!=t){var n=g[e];return n?S(t,e,n.name):void 0}};return n.getDimensionInfo=(0,o.ak)(d,null,g,y),n.cloneAllDimensionInfo=(0,o.ak)(v,null,g),n}function f(t){var e=t.sourceFormat;if(!x(e)){var n="";0,(0,u._y)(n)}return t.data}function p(t){var e=t.sourceFormat,n=t.data;if(!x(e)){var r="";0,(0,u._y)(r)}if(e===i.XD){for(var a=[],s=0,l=n.length;s<l;s++)a.push(n[s].slice());return a}if(e===i.qb){for(a=[],s=0,l=n.length;s<l;s++)a.push((0,o.l7)({},n[s]));return a}}function d(t,e,n){if(null!=n)return(0,o.hj)(n)||!isNaN(n)&&!(0,o.RI)(e,n)?t[n]:(0,o.RI)(e,n)?e[n]:void 0}function v(t){return(0,o.d9)(t)}var g=(0,o.kW)();function y(t){t=(0,o.d9)(t);var e=t.type,n="";e||(0,u._y)(n);var i=e.split(":");2!==i.length&&(0,u._y)(n);var r=!1;"echarts"===i[0]&&(e=i[1],r=!0),t.__isBuiltIn=r,g.set(e,t)}function m(t,e,n){var i=(0,r.kF)(t),o=i.length,a="";o||(0,u._y)(a);for(var s=0,l=o;s<l;s++){var h=i[s];e=_(h,e,n,1===o?null:s),s!==l-1&&(e.length=Math.max(e.length,1))}return e}function _(t,e,n,a){var s="";e.length||(0,u._y)(s),(0,o.Kn)(t)||(0,u._y)(s);var h=t.type,f=g.get(h);f||(0,u._y)(s);var p=(0,o.UI)(e,(function(t){return c(t,f)})),d=(0,r.kF)(f.transform({upstream:p[0],upstreamList:p,config:(0,o.d9)(t.config)}));return(0,o.UI)(d,(function(t,n){var r="";(0,o.Kn)(t)||(0,u._y)(r),t.data||(0,u._y)(r);var a,s=(0,l.Kp)(t.data);x(s)||(0,u._y)(r);var h=e[0];if(h&&0===n&&!t.dimensions){var c=h.startIndex;c&&(t.data=h.data.slice(0,c).concat(t.data)),a={seriesLayoutBy:i.fY,sourceHeader:c,dimensions:h.metaRawOption.dimensions}}else a={seriesLayoutBy:i.fY,sourceHeader:0,dimensions:t.dimensions};return(0,l._P)(t.data,a,null)}))}function x(t){return t===i.XD||t===i.qb}},68023:function(t,e,n){n.d(e,{D:function(){return p}});var i=n(30454),r=n(33166),o=n(75797),a=n(98071),s=n(93321),u=n(33051),l=n(49428),h=n(56641),c=[],f={registerPreprocessor:i.ds,registerProcessor:i.Pu,registerPostInit:i.sq,registerPostUpdate:i.Br,registerUpdateLifecycle:i.YK,registerAction:i.zl,registerCoordinateSystem:i.RS,registerLayout:i.qR,registerVisual:i.Og,registerTransform:i.OB,registerLoading:i.yn,registerMap:i.je,registerImpl:l.M,PRIORITY:i.Hr,ComponentModel:a.Z,ComponentView:r.Z,SeriesModel:s.Z,ChartView:o.Z,registerComponentModel:function(t){a.Z.registerClass(t)},registerComponentView:function(t){r.Z.registerClass(t)},registerSeriesModel:function(t){s.Z.registerClass(t)},registerChartView:function(t){o.Z.registerClass(t)},registerSubTypeDefaulter:function(t,e){a.Z.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){(0,h.wm)(t,e)}};function p(t){(0,u.kJ)(t)?(0,u.S6)(t,(function(t){p(t)})):(0,u.cq)(c,t)>=0||(c.push(t),(0,u.mf)(t)&&(t={install:t}),t.install(f))}},54162:function(t,e,n){n.d(e,{GI:function(){return u},VT:function(){return o},WE:function(){return s},yl:function(){return l}});var i=n(41587),r=n(60479);function o(t){for(var e=[],n=0;n<t.length;n++){var r=t[n];if(!r.defaultAttr.ignore){var o=r.label,a=o.getComputedTransform(),s=o.getBoundingRect(),u=!a||a[1]<1e-5&&a[2]<1e-5,l=o.style.margin||0,h=s.clone();h.applyTransform(a),h.x-=l/2,h.y-=l/2,h.width+=l,h.height+=l;var c=u?new i.Z(s,a):null;e.push({label:o,labelLine:r.labelLine,rect:h,localRect:s,obb:c,priority:r.priority,defaultAttr:r.defaultAttr,layoutOption:r.computedLayoutOption,axisAligned:u,transform:a})}}return e}function a(t,e,n,i,r,o){var a=t.length;if(!(a<2)){t.sort((function(t,n){return t.rect[e]-n.rect[e]}));for(var s,u=0,l=!1,h=[],c=0,f=0;f<a;f++){var p=t[f],d=p.rect;s=d[e]-u,s<0&&(d[e]-=s,p.label[e]-=s,l=!0);var v=Math.max(-s,0);h.push(v),c+=v,u=d[e]+d[n]}c>0&&o&&S(-c/a,0,a);var g,y,m=t[0],_=t[a-1];return x(),g<0&&b(-g,.8),y<0&&b(y,.8),x(),w(g,y,1),w(y,g,-1),x(),g<0&&T(-g),y<0&&T(y),l}function x(){g=m.rect[e]-i,y=r-_.rect[e]-_.rect[n]}function w(t,e,n){if(t<0){var i=Math.min(e,-t);if(i>0){S(i*n,0,a);var r=i+t;r<0&&b(-r*n,1)}else b(-t*n,1)}}function S(n,i,r){0!==n&&(l=!0);for(var o=i;o<r;o++){var a=t[o],s=a.rect;s[e]+=n,a.label[e]+=n}}function b(i,r){for(var o=[],s=0,u=1;u<a;u++){var l=t[u-1].rect,h=Math.max(t[u].rect[e]-l[e]-l[n],0);o.push(h),s+=h}if(s){var c=Math.min(Math.abs(i)/s,r);if(i>0)for(u=0;u<a-1;u++){var f=o[u]*c;S(f,0,u+1)}else for(u=a-1;u>0;u--){f=o[u-1]*c;S(-f,u,a)}}}function T(t){var e=t<0?-1:1;t=Math.abs(t);for(var n=Math.ceil(t/(a-1)),i=0;i<a-1;i++)if(e>0?S(n,0,i+1):S(-n,a-i-1,a),t-=n,t<=0)return}}function s(t,e,n,i){return a(t,"x","width",e,n,i)}function u(t,e,n,i){return a(t,"y","height",e,n,i)}function l(t){var e=[];t.sort((function(t,e){return e.priority-t.priority}));var n=new r.Z(0,0,0,0);function o(t){if(!t.ignore){var e=t.ensureState("emphasis");null==e.ignore&&(e.ignore=!1)}t.ignore=!0}for(var a=0;a<t.length;a++){var s=t[a],u=s.axisAligned,l=s.localRect,h=s.transform,c=s.label,f=s.labelLine;n.copy(s.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05;for(var p=s.obb,d=!1,v=0;v<e.length;v++){var g=e[v];if(n.intersect(g.rect)){if(u&&g.axisAligned){d=!0;break}if(g.obb||(g.obb=new i.Z(g.localRect,g.transform)),p||(p=new i.Z(l,h)),p.intersect(g.obb)){d=!0;break}}}d?(o(c),f&&o(f)):(c.attr("ignore",s.defaultAttr.ignore),f&&f.attr("ignore",s.defaultAttr.labelGuideIgnore),e.push(s))}}},36006:function(t,e,n){n.d(e,{Lr:function(){return p},k3:function(){return f},nC:function(){return d},ni:function(){return c},pe:function(){return b},qA:function(){return S},qT:function(){return w},tD:function(){return T}});var i=n(96498),r=n(33051),o=n(26357),a=n(32234),s=n(29266),u={};function l(t,e){for(var n=0;n<o.L1.length;n++){var i=o.L1[n],r=e[i],a=t.ensureState(i);a.style=a.style||{},a.style.text=r}var s=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(s,!0)}function h(t,e,n){var i,a=t.labelFetcher,s=t.labelDataIndex,u=t.labelDimIndex,l=e.normal;a&&(i=a.getFormattedLabel(s,"normal",null,u,l&&l.get("formatter"),null!=n?{interpolatedValue:n}:null)),null==i&&(i=(0,r.mf)(t.defaultText)?t.defaultText(s,t,n):t.defaultText);for(var h={normal:i},c=0;c<o.L1.length;c++){var f=o.L1[c],p=e[f];h[f]=(0,r.pD)(a?a.getFormattedLabel(s,f,null,u,p&&p.get("formatter")):null,i)}return h}function c(t,e,n,a){n=n||u;for(var s=t instanceof i.ZP,c=!1,f=0;f<o.qc.length;f++){var v=e[o.qc[f]];if(v&&v.getShallow("show")){c=!0;break}}var g=s?t:t.getTextContent();if(c){s||(g||(g=new i.ZP,t.setTextContent(g)),t.stateProxy&&(g.stateProxy=t.stateProxy));var y=h(n,e),m=e.normal,_=!!m.getShallow("show"),x=p(m,a&&a.normal,n,!1,!s);x.text=y.normal,s||t.setTextConfig(d(m,n,!1));for(f=0;f<o.L1.length;f++){var w=o.L1[f];v=e[w];if(v){var b=g.ensureState(w),T=!!(0,r.pD)(v.getShallow("show"),_);if(T!==_&&(b.ignore=!T),b.style=p(v,a&&a[w],n,!0,!s),b.style.text=y[w],!s){var M=t.ensureState(w);M.textConfig=d(v,n,!0)}}}g.silent=!!m.getShallow("silent"),null!=g.style.x&&(x.x=g.style.x),null!=g.style.y&&(x.y=g.style.y),g.ignore=!_,g.useStyle(x),g.dirty(),n.enableTextSetter&&(S(g).setLabelText=function(t){var i=h(n,e,t);l(g,i)})}else g&&(g.ignore=!0);t.dirty()}function f(t,e){e=e||"label";for(var n={normal:t.getModel(e)},i=0;i<o.L1.length;i++){var r=o.L1[i];n[r]=t.getModel([r,e])}return n}function p(t,e,n,i,o){var a={};return v(a,t,n,i,o),e&&(0,r.l7)(a,e),a}function d(t,e,n){e=e||{};var i,o={},a=t.getShallow("rotate"),s=(0,r.pD)(t.getShallow("distance"),n?null:5),u=t.getShallow("offset");return i=t.getShallow("position")||(n?null:"inside"),"outside"===i&&(i=e.defaultOutsidePosition||"top"),null!=i&&(o.position=i),null!=u&&(o.offset=u),null!=a&&(a*=Math.PI/180,o.rotation=a),null!=s&&(o.distance=s),o.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",o}function v(t,e,n,i,r){n=n||u;var o,a=e.ecModel,s=a&&a.option.textStyle,l=g(e);if(l)for(var h in o={},l)if(l.hasOwnProperty(h)){var c=e.getModel(["rich",h]);x(o[h]={},c,s,n,i,r,!1,!0)}o&&(t.rich=o);var f=e.get("overflow");f&&(t.overflow=f);var p=e.get("minMargin");null!=p&&(t.margin=p),x(t,e,s,n,i,r,!0,!1)}function g(t){var e;while(t&&t!==t.ecModel){var n=(t.option||u).rich;if(n){e=e||{};for(var i=(0,r.XP)(n),o=0;o<i.length;o++){var a=i[o];e[a]=1}}t=t.parentModel}return e}var y=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],m=["align","lineHeight","width","height","tag","verticalAlign"],_=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function x(t,e,n,i,o,a,s,l){n=!o&&n||u;var h=i&&i.inheritColor,c=e.getShallow("color"),f=e.getShallow("textBorderColor"),p=(0,r.pD)(e.getShallow("opacity"),n.opacity);"inherit"!==c&&"auto"!==c||(c=h||null),"inherit"!==f&&"auto"!==f||(f=h||null),a||(c=c||n.color,f=f||n.textBorderColor),null!=c&&(t.fill=c),null!=f&&(t.stroke=f);var d=(0,r.pD)(e.getShallow("textBorderWidth"),n.textBorderWidth);null!=d&&(t.lineWidth=d);var v=(0,r.pD)(e.getShallow("textBorderType"),n.textBorderType);null!=v&&(t.lineDash=v);var g=(0,r.pD)(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset);null!=g&&(t.lineDashOffset=g),o||null!=p||l||(p=i&&i.defaultOpacity),null!=p&&(t.opacity=p),o||a||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var x=0;x<y.length;x++){var w=y[x],S=(0,r.pD)(e.getShallow(w),n[w]);null!=S&&(t[w]=S)}for(x=0;x<m.length;x++){w=m[x],S=e.getShallow(w);null!=S&&(t[w]=S)}if(null==t.verticalAlign){var b=e.getShallow("baseline");null!=b&&(t.verticalAlign=b)}if(!s||!i.disableBox){for(x=0;x<_.length;x++){w=_[x],S=e.getShallow(w);null!=S&&(t[w]=S)}var T=e.getShallow("borderType");null!=T&&(t.borderDash=T),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!h||(t.backgroundColor=h),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!h||(t.borderColor=h)}}function w(t,e){var n=e&&e.getModel("textStyle");return(0,r.fy)([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}var S=(0,a.Yf)();function b(t,e,n,i){if(t){var r=S(t);r.prevValue=r.value,r.value=n;var o=e.normal;r.valueAnimation=o.get("valueAnimation"),r.valueAnimation&&(r.precision=o.get("precision"),r.defaultInterpolatedText=i,r.statesModels=e)}}function T(t,e,n,i,o){var u=S(t);if(u.valueAnimation&&u.prevValue!==u.value){var c=u.defaultInterpolatedText,f=(0,r.pD)(u.interpolatedValue,u.prevValue),p=u.value;t.percent=0,(null==u.prevValue?s.KZ:s.D)(t,{percent:1},i,e,null,d)}function d(i){var r=(0,a.pk)(n,u.precision,f,p,i);u.interpolatedValue=1===i?null:r;var s=h({labelDataIndex:e,labelFetcher:o,defaultText:c?c(r):r+""},u.statesModels,r);l(t,s)}}},79093:function(t,e,n){n.d(e,{Bk:function(){return m},G_:function(){return g},Ge:function(){return f},Gk:function(){return c},My:function(){return d},bK:function(){return y}});var i=n(33051),r=n(85669),o=n(99936),a=n(95682),s=n(80887),u="__ec_stack_";function l(t){return t.get("stack")||u+t.seriesIndex}function h(t){return t.dim+t.index}function c(t){var e=[],n=t.axis,r="axis0";if("category"===n.type){for(var o=n.getBandWidth(),a=0;a<t.count;a++)e.push((0,i.ce)({bandWidth:o,axisKey:r,stackId:u+a},t));var s=v(e),l=[];for(a=0;a<t.count;a++){var h=s[r][u+a];h.offsetCenter=h.offset+h.width/2,l.push(h)}return l}}function f(t,e){var n=[];return e.eachSeriesByType(t,(function(t){_(t)&&n.push(t)})),n}function p(t){var e={};(0,i.S6)(t,(function(t){var n=t.coordinateSystem,i=n.getBaseAxis();if("time"===i.type||"value"===i.type)for(var r=t.getData(),o=i.dim+"_"+i.index,a=r.getDimensionIndex(r.mapDimension(i.dim)),s=r.getStore(),u=0,l=s.count();u<l;++u){var h=s.get(a,u);e[o]?e[o].push(h):e[o]=[h]}}));var n={};for(var r in e)if(e.hasOwnProperty(r)){var o=e[r];if(o){o.sort((function(t,e){return t-e}));for(var a=null,s=1;s<o.length;++s){var u=o[s]-o[s-1];u>0&&(a=null===a?u:Math.min(a,u))}n[r]=a}}return n}function d(t){var e=p(t),n=[];return(0,i.S6)(t,(function(t){var i,o=t.coordinateSystem,a=o.getBaseAxis(),s=a.getExtent();if("category"===a.type)i=a.getBandWidth();else if("value"===a.type||"time"===a.type){var u=a.dim+"_"+a.index,c=e[u],f=Math.abs(s[1]-s[0]),p=a.scale.getExtent(),d=Math.abs(p[1]-p[0]);i=c?f/d*c:f}else{var v=t.getData();i=Math.abs(s[1]-s[0])/v.count()}var g=(0,r.GM)(t.get("barWidth"),i),y=(0,r.GM)(t.get("barMaxWidth"),i),m=(0,r.GM)(t.get("barMinWidth")||(x(t)?.5:1),i),_=t.get("barGap"),w=t.get("barCategoryGap");n.push({bandWidth:i,barWidth:g,barMaxWidth:y,barMinWidth:m,barGap:_,barCategoryGap:w,axisKey:h(a),stackId:l(t)})})),v(n)}function v(t){var e={};(0,i.S6)(t,(function(t,n){var i=t.axisKey,r=t.bandWidth,o=e[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},a=o.stacks;e[i]=o;var s=t.stackId;a[s]||o.autoWidthCount++,a[s]=a[s]||{width:0,maxWidth:0};var u=t.barWidth;u&&!a[s].width&&(a[s].width=u,u=Math.min(o.remainedWidth,u),o.remainedWidth-=u);var l=t.barMaxWidth;l&&(a[s].maxWidth=l);var h=t.barMinWidth;h&&(a[s].minWidth=h);var c=t.barGap;null!=c&&(o.gap=c);var f=t.barCategoryGap;null!=f&&(o.categoryGap=f)}));var n={};return(0,i.S6)(e,(function(t,e){n[e]={};var o=t.stacks,a=t.bandWidth,s=t.categoryGap;if(null==s){var u=(0,i.XP)(o).length;s=Math.max(35-4*u,15)+"%"}var l=(0,r.GM)(s,a),h=(0,r.GM)(t.gap,1),c=t.remainedWidth,f=t.autoWidthCount,p=(c-l)/(f+(f-1)*h);p=Math.max(p,0),(0,i.S6)(o,(function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){i=t.width;e&&(i=Math.min(i,e)),n&&(i=Math.max(i,n)),t.width=i,c-=i+h*i,f--}else{var i=p;e&&e<i&&(i=Math.min(e,c)),n&&n>i&&(i=n),i!==p&&(t.width=i,c-=i+h*i,f--)}})),p=(c-l)/(f+(f-1)*h),p=Math.max(p,0);var d,v=0;(0,i.S6)(o,(function(t,e){t.width||(t.width=p),d=t,v+=t.width*(1+h)})),d&&(v-=d.width*h);var g=-v/2;(0,i.S6)(o,(function(t,i){n[e][i]=n[e][i]||{bandWidth:a,offset:g,width:t.width},g+=t.width*(1+h)}))})),n}function g(t,e,n){if(t&&e){var i=t[h(e)];return null!=i&&null!=n?i[l(n)]:i}}function y(t,e){var n=f(t,e),r=d(n);(0,i.S6)(n,(function(t){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),o=l(t),a=r[h(i)][o],s=a.offset,u=a.width;e.setLayout({bandWidth:a.bandWidth,offset:s,size:u})}))}function m(t){return{seriesType:t,plan:(0,a.Z)(),reset:function(t){if(_(t)){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),r=n.getOtherAxis(i),a=e.getDimensionIndex(e.mapDimension(r.dim)),u=e.getDimensionIndex(e.mapDimension(i.dim)),l=t.get("showBackground",!0),h=e.mapDimension(r.dim),c=e.getCalculationInfo("stackResultDimension"),f=(0,o.M)(e,h)&&!!e.getCalculationInfo("stackedOnSeries"),p=r.isHorizontal(),d=w(i,r),v=x(t),g=t.get("barMinHeight")||0,y=c&&e.getDimensionIndex(c),m=e.getLayout("size"),S=e.getLayout("offset");return{progress:function(t,e){var i,r=t.count,o=v&&(0,s.o)(3*r),h=v&&l&&(0,s.o)(3*r),c=v&&(0,s.o)(r),_=n.master.getRect(),x=p?_.width:_.height,w=e.getStore(),b=0;while(null!=(i=t.next())){var T=w.get(f?y:a,i),M=w.get(u,i),k=d,D=void 0;f&&(D=+T-w.get(a,i));var C=void 0,I=void 0,P=void 0,A=void 0;if(p){var O=n.dataToPoint([T,M]);if(f){var Z=n.dataToPoint([D,M]);k=Z[0]}C=k,I=O[1]+S,P=O[0]-k,A=m,Math.abs(P)<g&&(P=(P<0?-1:1)*g)}else{O=n.dataToPoint([M,T]);if(f){Z=n.dataToPoint([M,D]);k=Z[1]}C=O[0]+S,I=k,P=m,A=O[1]-k,Math.abs(A)<g&&(A=(A<=0?-1:1)*g)}v?(o[b]=C,o[b+1]=I,o[b+2]=p?P:A,h&&(h[b]=p?_.x:C,h[b+1]=p?I:_.y,h[b+2]=x),c[i]=i):e.setItemLayout(i,{x:C,y:I,width:P,height:A}),b+=3}v&&e.setLayout({largePoints:o,largeDataIndices:c,largeBackgroundPoints:h,valueAxisHorizontal:p})}}}}}}function _(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function x(t){return t.pipelineContext&&t.pipelineContext.large}function w(t,e){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}},31891:function(t,e,n){n.d(e,{s:function(){return s},y:function(){return o}});var i=n(33051),r=n(32234);function o(t,e){function n(e,n){var i=[];return e.eachComponent({mainType:"series",subType:t,query:n},(function(t){i.push(t.seriesIndex)})),i}(0,i.S6)([[t+"ToggleSelect","toggleSelect"],[t+"Select","select"],[t+"UnSelect","unselect"]],(function(t){e(t[0],(function(e,r,o){e=(0,i.l7)({},e),o.dispatchAction((0,i.l7)(e,{type:t[1],seriesIndex:n(r,e)}))}))}))}function a(t,e,n,o,a){var s=t+e;n.isSilent(s)||o.eachComponent({mainType:"series",subType:"pie"},(function(t){for(var e=t.seriesIndex,o=t.option.selectedMap,u=a.selected,l=0;l<u.length;l++)if(u[l].seriesIndex===e){var h=t.getData(),c=(0,r.gO)(h,a.fromActionPayload);n.trigger(s,{type:s,seriesId:t.id,name:(0,i.kJ)(c)?h.getName(c[0]):h.getName(c),selected:(0,i.HD)(o)?o:(0,i.l7)({},o)})}}))}function s(t,e,n){t.on("selectchanged",(function(t){var i=n.getModel();t.isFromClick?(a("map","selectchanged",e,i,t),a("pie","selectchanged",e,i,t)):"select"===t.fromAction?(a("map","selected",e,i,t),a("pie","selected",e,i,t)):"unselect"===t.fromAction&&(a("map","unselected",e,i,t),a("pie","unselected",e,i,t))}))}},98071:function(t,e,n){var i=n(70655),r=n(33051),o=n(12312),a=n(42151),s=n(34251),u=n(32234),l=n(76172),h=(0,u.Yf)(),c=function(t){function e(e,n,i){var r=t.call(this,e,n,i)||this;return r.uid=a.Kr("ec_cpt_model"),r}return(0,i.ZT)(e,t),e.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},e.prototype.mergeDefaultAndTheme=function(t,e){var n=l.YD(this),i=n?l.tE(t):{},o=e.getTheme();r.TS(t,o.get(this.mainType)),r.TS(t,this.getDefaultOption()),n&&l.dt(t,i,n)},e.prototype.mergeOption=function(t,e){r.TS(this.option,t,!0);var n=l.YD(this);n&&l.dt(this.option,t,n)},e.prototype.optionUpdated=function(t,e){},e.prototype.getDefaultOption=function(){var t=this.constructor;if(!(0,s.PT)(t))return t.defaultOption;var e=h(this);if(!e.defaultOption){var n=[],i=t;while(i){var o=i.prototype.defaultOption;o&&n.push(o),i=i.superClass}for(var a={},u=n.length-1;u>=0;u--)a=r.TS(a,n[u],!0);e.defaultOption=a}return e.defaultOption},e.prototype.getReferringComponents=function(t,e){var n=t+"Index",i=t+"Id";return(0,u.HZ)(this.ecModel,t,{index:this.get(n,!0),id:this.get(i,!0)},e)},e.prototype.getBoxLayoutParams=function(){var t=this;return{left:t.get("left"),top:t.get("top"),right:t.get("right"),bottom:t.get("bottom"),width:t.get("width"),height:t.get("height")}},e.prototype.getZLevelKey=function(){return""},e.prototype.setZLevel=function(t){this.option.zlevel=t},e.protoInitialize=function(){var t=e.prototype;t.type="component",t.id="",t.name="",t.mainType="",t.subType="",t.componentIndex=0}(),e}(o.Z);function f(t){var e=[];return r.S6(c.getClassesByMainType(t),(function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])})),e=r.UI(e,(function(t){return(0,s.u9)(t).main})),"dataset"!==t&&r.cq(e,"dataset")<=0&&e.unshift("dataset"),e}(0,s.pw)(c,o.Z),(0,s.au)(c),a.cj(c),a.jS(c,f),e["Z"]=c},12312:function(t,e,n){n.d(e,{Z:function(){return x}});var i=n(66387),r=n(34251),o=n(59066),a=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],s=(0,o.Z)(a),u=function(){function t(){}return t.prototype.getAreaStyle=function(t,e){return s(this,t,e)},t}(),l=n(36006),h=n(96498),c=["textStyle","color"],f=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],p=new h.ZP,d=function(){function t(){}return t.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(c):null)},t.prototype.getFont=function(){return(0,l.qT)({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},t.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<f.length;n++)e[f[n]]=this.getShallow(f[n]);return p.useStyle(e),p.update(),p.getBoundingRect()},t}(),v=d,g=n(77515),y=n(89887),m=n(33051),_=function(){function t(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}return t.prototype.init=function(t,e,n){for(var i=[],r=3;r<arguments.length;r++)i[r-3]=arguments[r]},t.prototype.mergeOption=function(t,e){(0,m.TS)(this.option,t,!0)},t.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},t.prototype.getShallow=function(t,e){var n=this.option,i=null==n?n:n[t];if(null==i&&!e){var r=this.parentModel;r&&(i=r.getShallow(t))}return i},t.prototype.getModel=function(e,n){var i=null!=e,r=i?this.parsePath(e):null,o=i?this._doGet(r):this.option;return n=n||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(r)),new t(o,n,this.ecModel)},t.prototype.isEmpty=function(){return null==this.option},t.prototype.restoreData=function(){},t.prototype.clone=function(){var t=this.constructor;return new t((0,m.d9)(this.option))},t.prototype.parsePath=function(t){return"string"===typeof t?t.split("."):t},t.prototype.resolveParentPath=function(t){return t},t.prototype.isAnimationEnabled=function(){if(!i.Z.node&&this.option){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},t.prototype._doGet=function(t,e){var n=this.option;if(!t)return n;for(var i=0;i<t.length;i++)if(t[i]&&(n=n&&"object"===typeof n?n[t[i]]:null,null==n))break;return null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel)),n},t}();(0,r.dm)(_),(0,r.Qj)(_),(0,m.jB)(_,g.K),(0,m.jB)(_,y.D),(0,m.jB)(_,u),(0,m.jB)(_,v);var x=_},93321:function(t,e,n){n.d(e,{V:function(){return y}});var i=n(70655),r=n(33051),o=n(66387),a=n(32234),s=n(98071),u=n(75494),l=n(61219),h=n(76172),c=n(8674),f=n(34251),p=n(36437),d=n(53993),v=a.Yf();function g(t,e){return t.getName(e)||t.getId(e)}var y="__universalTransitionEnabled",m=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return(0,i.ZT)(e,t),e.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=(0,c.v)({count:w,reset:S}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n);var i=v(this).sourceManager=new p.U(this);i.prepareSource();var r=this.getInitialData(t,n);T(r,this),this.dataTask.context.data=r,v(this).dataBeforeProcessed=r,_(this),this._initSelectedMapFromData(r)},e.prototype.mergeDefaultAndTheme=function(t,e){var n=(0,h.YD)(this),i=n?(0,h.tE)(t):{},o=this.subType;s.Z.hasClass(o)&&(o+="Series"),r.TS(t,e.getTheme().get(this.subType)),r.TS(t,this.getDefaultOption()),a.Cc(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&(0,h.dt)(t,i,n)},e.prototype.mergeOption=function(t,e){t=r.TS(this.option,t,!0),this.fillDataTextStyle(t.data);var n=(0,h.YD)(this);n&&(0,h.dt)(this.option,t,n);var i=v(this).sourceManager;i.dirty(),i.prepareSource();var o=this.getInitialData(t,e);T(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,v(this).dataBeforeProcessed=o,_(this),this._initSelectedMapFromData(o)},e.prototype.fillDataTextStyle=function(t){if(t&&!r.fU(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&a.Cc(t[n],"label",e)},e.prototype.getInitialData=function(t,e){},e.prototype.appendData=function(t){var e=this.getRawData();e.appendData(t.data)},e.prototype.getData=function(t){var e=k(this);if(e){var n=e.context.data;return null==t?n:n.getLinkedData(t)}return v(this).data},e.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},e.prototype.setData=function(t){var e=k(this);if(e){var n=e.context;n.outputData=t,e!==this.dataTask&&(n.data=t)}v(this).data=t},e.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return r.kW(t)},e.prototype.getSourceManager=function(){return v(this).sourceManager},e.prototype.getSource=function(){return this.getSourceManager().getSource()},e.prototype.getRawData=function(){return v(this).dataBeforeProcessed},e.prototype.getColorBy=function(){var t=this.get("colorBy");return t||"series"},e.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},e.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},e.prototype.formatTooltip=function(t,e,n){return(0,d.w)({series:this,dataIndex:t,multipleSeries:e})},e.prototype.isAnimationEnabled=function(){var t=this.ecModel;if(o.Z.node&&(!t||!t.ssr))return!1;var e=this.getShallow("animation");return e&&this.getData().count()>this.getShallow("animationThreshold")&&(e=!1),!!e},e.prototype.restoreData=function(){this.dataTask.dirty()},e.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel,r=u._.prototype.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},e.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},e.prototype.getProgressive=function(){return this.get("progressive")},e.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},e.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},e.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)return this.option.selectedMap={},void(this._selectedDataIndicesMap={});for(var o=0;o<t.length;o++){var a=t[o],s=g(r,a);n[s]=!1,this._selectedDataIndicesMap[s]=-1}}},e.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},e.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=r.XP(t),n=[],i=0;i<e.length;i++){var o=t[e[i]];o>=0&&n.push(o)}return n},e.prototype.isSelected=function(t,e){var n=this.option.selectedMap;if(!n)return!1;var i=this.getData(e);return("all"===n||n[g(i,t)])&&!i.getItemModel(t).get(["select","disabled"])},e.prototype.isUniversalTransitionEnabled=function(){if(this[y])return!0;var t=this.option.universalTransition;return!!t&&(!0===t||t&&t.enabled)},e.prototype._innerSelect=function(t,e){var n,i,o=this.option,a=o.selectedMode,s=e.length;if(a&&s)if("series"===a)o.selectedMap="all";else if("multiple"===a){r.Kn(o.selectedMap)||(o.selectedMap={});for(var u=o.selectedMap,l=0;l<s;l++){var h=e[l],c=g(t,h);u[c]=!0,this._selectedDataIndicesMap[c]=t.getRawIndex(h)}}else if("single"===a||!0===a){var f=e[s-1];c=g(t,f);o.selectedMap=(n={},n[c]=!0,n),this._selectedDataIndicesMap=(i={},i[c]=t.getRawIndex(f),i)}},e.prototype._initSelectedMapFromData=function(t){if(!this.option.selectedMap){var e=[];t.hasItemOption&&t.each((function(n){var i=t.getRawDataItem(n);i&&i.selected&&e.push(n)})),e.length>0&&this._innerSelect(t,e)}},e.registerClass=function(t){return s.Z.registerClass(t)},e.protoInitialize=function(){var t=e.prototype;t.type="series.__base__",t.seriesIndex=0,t.ignoreStyleOnData=!1,t.hasSymbolVisual=!1,t.defaultSymbol="circle",t.visualStyleAccessPath="itemStyle",t.visualDrawType="fill"}(),e}(s.Z);function _(t){var e=t.name;a.yu(t)||(t.name=x(t)||e)}function x(t){var e=t.getRawData(),n=e.mapDimensionsAll("seriesName"),i=[];return r.S6(n,(function(t){var n=e.getDimensionInfo(t);n.displayName&&i.push(n.displayName)})),i.join(" ")}function w(t){return t.model.getRawData().count()}function S(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),b}function b(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function T(t,e){r.S6(r.WW(t.CHANGABLE_METHODS,t.DOWNSAMPLE_METHODS),(function(n){t.wrapMethod(n,r.WA(M,e))}))}function M(t,e){var n=k(t);return n&&n.setOutputEnd((e||this).count()),e}function k(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}r.jB(m,l.X),r.jB(m,u._),(0,f.pw)(m,s.Z),e["Z"]=m},82468:function(t,e,n){n.d(e,{R:function(){return a},f:function(){return o}});var i=n(33051),r=(0,i.kW)();function o(t,e){(0,i.hu)(null==r.get(t)&&e),r.set(t,e)}function a(t,e,n){var i=r.get(e);if(!i)return n;var o=i(t);return o?n.concat(o):n}},61219:function(t,e,n){n.d(e,{X:function(){return s},f:function(){return u}});var i=n(33051),r=n(68540),o=n(78988),a=/\{@(.+?)\}/g,s=function(){function t(){}return t.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),u=s&&s[n.getItemVisual(t,"drawType")||"fill"],l=s&&s.stroke,h=this.mainType,c="series"===h,f=n.userOutput&&n.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:u,borderColor:l,dimensionNames:f?f.fullDimensions:null,encode:f?f.encode:null,$vars:["seriesName","name","value"]}},t.prototype.getFormattedLabel=function(t,e,n,s,u,l){e=e||"normal";var h=this.getData(n),c=this.getDataParams(t,n);if(l&&(c.value=l.interpolatedValue),null!=s&&i.kJ(c.value)&&(c.value=c.value[s]),!u){var f=h.getItemModel(t);u=f.get("normal"===e?["label","formatter"]:[e,"label","formatter"])}if(i.mf(u))return c.status=e,c.dimensionIndex=s,u(c);if(i.HD(u)){var p=(0,o.kF)(u,c);return p.replace(a,(function(e,n){var o=n.length,a=n;"["===a.charAt(0)&&"]"===a.charAt(o-1)&&(a=+a.slice(1,o-1));var s=(0,r.hk)(h,t,a);if(l&&i.kJ(l.interpolatedValue)){var u=h.getDimensionIndex(a);u>=0&&(s=l.interpolatedValue[u])}return null!=s?s+"":""}))}},t.prototype.getRawValue=function(t,e){return(0,r.hk)(this.getData(e),t)},t.prototype.formatTooltip=function(t,e,n){},t}();function u(t){var e,n;return i.Kn(t)?t.type&&(n=t):e=t,{text:e,frag:n}}},89887:function(t,e,n){n.d(e,{D:function(){return a},t:function(){return r}});var i=n(59066),r=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],o=(0,i.Z)(r),a=function(){function t(){}return t.prototype.getItemStyle=function(t,e){return o(this,t,e)},t}()},77515:function(t,e,n){n.d(e,{K:function(){return a},v:function(){return r}});var i=n(59066),r=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],o=(0,i.Z)(r),a=function(){function t(){}return t.prototype.getLineStyle=function(t){return o(this,t)},t}()},59066:function(t,e,n){n.d(e,{Z:function(){return r}});var i=n(33051);function r(t,e){for(var n=0;n<t.length;n++)t[n][1]||(t[n][1]=t[n][0]);return e=e||!1,function(n,r,o){for(var a={},s=0;s<t.length;s++){var u=t[s][1];if(!(r&&i.cq(r,u)>=0||o&&i.cq(o,u)<0)){var l=n.getShallow(u,e);null!=l&&(a[t[s][0]]=l)}}return a}}},75494:function(t,e,n){n.d(e,{_:function(){return a},i:function(){return s}});var i=n(32234),r=(0,i.Yf)(),o=(0,i.Yf)(),a=function(){function t(){}return t.prototype.getColorFromPalette=function(t,e,n){var o=(0,i.kF)(this.get("color",!0)),a=this.get("colorLayer",!0);return l(this,r,o,a,t,e,n)},t.prototype.clearColorPalette=function(){h(this,r)},t}();function s(t,e,n,r){var a=(0,i.kF)(t.get(["aria","decal","decals"]));return l(t,o,a,null,e,n,r)}function u(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}function l(t,e,n,i,r,o,a){o=o||t;var s=e(o),l=s.paletteIdx||0,h=s.paletteNameMap=s.paletteNameMap||{};if(h.hasOwnProperty(r))return h[r];var c=null!=a&&i?u(i,a):n;if(c=c||n,c&&c.length){var f=c[l];return r&&(h[r]=f),s.paletteIdx=(l+1)%c.length,f}}function h(t,e){e(t).paletteIdx=0,e(t).paletteNameMap={}}},91416:function(t,e,n){n.d(e,{N:function(){return M}});var i=n(4990),r=n(33051),o=n(70655),a=n(23510),s=n(5787),u=n(97772),l=n(60479),h=n(14414),c=n(23132);function f(t,e,n){var i=c.qW.createCanvas(),r=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=r+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=o*n,i}var p=function(t){function e(e,n,o){var a,s=t.call(this)||this;s.motionBlur=!1,s.lastFrameAlpha=.7,s.dpr=1,s.virtual=!1,s.config={},s.incremental=!1,s.zlevel=0,s.maxRepaintRectCount=5,s.__dirty=!0,s.__firstTimePaint=!0,s.__used=!1,s.__drawIndex=0,s.__startIndex=0,s.__endIndex=0,s.__prevStartIndex=null,s.__prevEndIndex=null,o=o||i.KL,"string"===typeof e?a=f(e,n,o):r.Kn(e)&&(a=e,e=a.id),s.id=e,s.dom=a;var u=a.style;return u&&(r.$j(a),a.onselectstart=function(){return!1},u.padding="0",u.margin="0",u.borderWidth="0"),s.painter=n,s.dpr=o,s}return(0,o.ZT)(e,t),e.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},e.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},e.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},e.prototype.setUnpainted=function(){this.__firstTimePaint=!0},e.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=f("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},e.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var r,o=[],a=this.maxRepaintRectCount,s=!1,u=new l.Z(0,0,0,0);function c(t){if(t.isFinite()&&!t.isZero())if(0===o.length){var e=new l.Z(0,0,0,0);e.copy(t),o.push(e)}else{for(var n=!1,i=1/0,r=0,h=0;h<o.length;++h){var c=o[h];if(c.intersect(t)){var f=new l.Z(0,0,0,0);f.copy(c),f.union(t),o[h]=f,n=!0;break}if(s){u.copy(t),u.union(c);var p=t.width*t.height,d=c.width*c.height,v=u.width*u.height,g=v-p-d;g<i&&(i=g,r=h)}}if(s&&(o[r].union(t),n=!0),!n){e=new l.Z(0,0,0,0);e.copy(t),o.push(e)}s||(s=o.length>=a)}}for(var f=this.__startIndex;f<this.__endIndex;++f){var p=t[f];if(p){var d=p.shouldBePainted(n,i,!0,!0),v=p.__isRendered&&(p.__dirty&h.YV||!d)?p.getPrevPaintRect():null;v&&c(v);var g=d&&(p.__dirty&h.YV||!p.__isRendered)?p.getPaintRect():null;g&&c(g)}}for(f=this.__prevStartIndex;f<this.__prevEndIndex;++f){p=e[f],d=p.shouldBePainted(n,i,!0,!0);if(p&&(!d||!p.__zr)&&p.__isRendered){v=p.getPrevPaintRect();v&&c(v)}}do{r=!1;for(f=0;f<o.length;)if(o[f].isZero())o.splice(f,1);else{for(var y=f+1;y<o.length;)o[f].intersect(o[y])?(r=!0,o[f].union(o[y]),o.splice(y,1)):y++;f++}}while(r);return this._paintRects=o,o},e.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},e.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n&&this.ctxBack.scale(n,n))},e.prototype.clear=function(t,e,n){var i=this.dom,o=this.ctx,a=i.width,l=i.height;e=e||this.clearColor;var h=this.motionBlur&&!t,c=this.lastFrameAlpha,f=this.dpr,p=this;h&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,a/f,l/f));var d=this.domBack;function v(t,n,i,a){if(o.clearRect(t,n,i,a),e&&"transparent"!==e){var l=void 0;r.Qq(e)?(l=e.__canvasGradient||(0,s.ZF)(o,e,{x:0,y:0,width:i,height:a}),e.__canvasGradient=l):r.dL(e)&&(l=(0,u.RZ)(o,e,{dirty:function(){p.setUnpainted(),p.__painter.refresh()}})),o.save(),o.fillStyle=l||e,o.fillRect(t,n,i,a),o.restore()}h&&(o.save(),o.globalAlpha=c,o.drawImage(d,t,n,i,a),o.restore())}!n||h?v(0,0,a,l):n.length&&r.S6(n,(function(t){v(t.x*f,t.y*f,t.width*f,t.height*f)}))},e}(a.Z),d=p,v=n(22795),g=n(66387),y=1e5,m=314159,_=.01,x=.001;function w(t){return!!t&&(!!t.__builtin__||"function"===typeof t.resize&&"function"===typeof t.refresh)}function S(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}var b=function(){function t(t,e,n,o){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=r.l7({},n||{}),this.dpr=n.devicePixelRatio||i.KL,this._singleCanvas=a,this.root=t;var u=t.style;u&&(r.$j(t),t.innerHTML=""),this.storage=e;var l=this._zlevelList;this._prevDisplayList=[];var h=this._layers;if(a){var c=t,f=c.width,p=c.height;null!=n.width&&(f=n.width),null!=n.height&&(p=n.height),this.dpr=n.devicePixelRatio||1,c.width=f*this.dpr,c.height=p*this.dpr,this._width=f,this._height=p;var v=new d(c,this,this.dpr);v.__builtin__=!0,v.initContext(),h[m]=v,v.zlevel=m,l.push(m),this._domRoot=t}else{this._width=(0,s.ap)(t,0,n),this._height=(0,s.ap)(t,1,n);var g=this._domRoot=S(this._width,this._height);t.appendChild(g)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o=i[r],a=this._layers[o];if(!a.__builtin__&&a.refresh){var s=0===r?this._backgroundColor:null;a.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n||(n=this._hoverlayer=this.getLayer(y)),i||(i=n.ctx,i.save()),(0,u.Dm)(i,a,r,o===e-1))}i&&i.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(y)},t.prototype.paintOne=function(t,e){(0,u.RV)(t,e)},t.prototype._paintList=function(t,e,n,i){if(this._redrawId===i){n=n||!1,this._updateLayerStatus(t);var r=this._doPaintList(t,e,n),o=r.finished,a=r.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;(0,v.Z)((function(){s._paintList(t,e,n,i)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(m).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)}))},t.prototype._doPaintList=function(t,e,n){for(var i=this,o=[],a=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var u=this._zlevelList[s],l=this._layers[u];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||n)&&o.push(l)}for(var h=!0,c=!1,f=function(r){var s,u=o[r],l=u.ctx,f=a&&u.createRepaintRects(t,e,p._width,p._height),d=n?u.__startIndex:u.__drawIndex,v=!n&&u.incremental&&Date.now,g=v&&Date.now(),y=u.zlevel===p._zlevelList[0]?p._backgroundColor:null;if(u.__startIndex===u.__endIndex)u.clear(!1,y,f);else if(d===u.__startIndex){var m=t[d];m.incremental&&m.notClear&&!n||u.clear(!1,y,f)}-1===d&&(console.error("For some unknown reason. drawIndex is -1"),d=u.__startIndex);var _=function(e){var n={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(s=d;s<u.__endIndex;s++){var r=t[s];if(r.__inHover&&(c=!0),i._doPaintEl(r,u,a,e,n,s===u.__endIndex-1),v){var o=Date.now()-g;if(o>15)break}}n.prevElClipPaths&&l.restore()};if(f)if(0===f.length)s=u.__endIndex;else for(var x=p.dpr,w=0;w<f.length;++w){var S=f[w];l.save(),l.beginPath(),l.rect(S.x*x,S.y*x,S.width*x,S.height*x),l.clip(),_(S),l.restore()}else l.save(),_(),l.restore();u.__drawIndex=s,u.__drawIndex<u.__endIndex&&(h=!1)},p=this,d=0;d<o.length;d++)f(d);return g.Z.wxa&&r.S6(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:h,needsRefreshHover:c}},t.prototype._doPaintEl=function(t,e,n,i,r,o){var a=e.ctx;if(n){var s=t.getPaintRect();(!i||s&&s.intersect(i))&&((0,u.Dm)(a,t,r,o),t.setPrevPaintRect(s))}else(0,u.Dm)(a,t,r,o)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=m);var n=this._layers[t];return n||(n=new d("zr_"+t,this,this.dpr),n.zlevel=t,n.__builtin__=!0,this._layerConfig[t]?r.TS(n,this._layerConfig[t],!0):this._layerConfig[t-_]&&r.TS(n,this._layerConfig[t-_],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},t.prototype.insertLayer=function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,o=this._domRoot,a=null,s=-1;if(!n[t]&&w(e)){if(r>0&&t>i[0]){for(s=0;s<r-1;s++)if(i[s]<t&&i[s+1]>t)break;a=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(a){var u=a.dom;u.nextSibling?o.insertBefore(e.dom,u.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.__painter=this}},t.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},t.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},t.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){s&&(s.__endIndex!==t&&(s.__dirty=!0),s.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var n=1;n<t.length;n++){var i=t[n];if(i.zlevel!==t[n-1].zlevel||i.incremental){this._needsManuallyCompositing=!0;break}}var o,a,s=null,u=0;for(a=0;a<t.length;a++){i=t[a];var l=i.zlevel,c=void 0;o!==l&&(o=l,u=0),i.incremental?(c=this.getLayer(l+x,this._needsManuallyCompositing),c.incremental=!0,u=1):c=this.getLayer(l+(u>0?_:0),this._needsManuallyCompositing),c.__builtin__||r.H("ZLevel "+l+" has been used by unkown layer "+c.id),c!==s&&(c.__used=!0,c.__startIndex!==a&&(c.__dirty=!0),c.__startIndex=a,c.incremental?c.__drawIndex=-1:c.__drawIndex=a,e(a),s=c),i.__dirty&h.YV&&!i.__inHover&&(c.__dirty=!0,c.incremental&&c.__drawIndex<0&&(c.__drawIndex=a))}e(a),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,r.S6(this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?r.TS(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var o=this._zlevelList[i];if(o===t||o===t+_){var a=this._layers[o];r.TS(a,n[t],!0)}}}},t.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(r.cq(n,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts,r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=(0,s.ap)(r,0,i),e=(0,s.ap)(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(m).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[m].dom;var e=new d("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var n=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer((function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())}))}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var h=a[s];(0,u.Dm)(n,h,o,s===l-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}(),T=b;function M(t){t.registerPainter("canvas",T)}},70103:function(t,e,n){var i=n(70655),r=n(85669),o=n(78988),a=n(60379),s=n(65021),u=r.NM,l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return(0,i.ZT)(e,t),e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return s.XS(t,this._extent)},e.prototype.normalize=function(t){return s.Fv(t,this._extent)},e.prototype.scale=function(t){return s.bA(t,this._extent)},e.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},e.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},e.prototype.getInterval=function(){return this._interval},e.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=s.lb(t)},e.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(!e)return o;var a=1e4;n[0]<i[0]&&(t?o.push({value:u(i[0]-e,r)}):o.push({value:n[0]}));var s=i[0];while(s<=i[1]){if(o.push({value:s}),s=u(s+e,r),s===o[o.length-1].value)break;if(o.length>a)return[]}var l=o.length?o[o.length-1].value:i[1];return n[1]>l&&(t?o.push({value:u(l+e,r)}):o.push({value:n[1]})),o},e.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){var o=e[r],a=e[r-1],s=0,l=[],h=o.value-a.value,c=h/t;while(s<t-1){var f=u(a.value+(s+1)*c);f>i[0]&&f<i[1]&&l.push(f),s++}n.push(l)}return n},e.prototype.getLabel=function(t,e){if(null==t)return"";var n=e&&e.precision;null==n?n=r.p8(t.value)||0:"auto"===n&&(n=this._intervalPrecision);var i=u(t.value,n,!0);return o.OD(i)},e.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var o=s.Qf(i,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},e.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=e[0];t.fixMax||(e[1]+=n/2),e[0]-=n/2}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=u(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=u(Math.ceil(e[1]/r)*r))},e.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},e.type="interval",e}(a.Z);a.Z.registerClass(l),e["Z"]=l},85043:function(t,e,n){var i=n(70655),r=n(60379),o=n(51401),a=n(65021),s=n(33051),u=function(t){function e(e){var n=t.call(this,e)||this;n.type="ordinal";var i=n.getSetting("ordinalMeta");return i||(i=new o.Z({})),(0,s.kJ)(i)&&(i=new o.Z({categories:(0,s.UI)(i,(function(t){return(0,s.Kn)(t)?t.value:t}))})),n._ordinalMeta=i,n._extent=n.getSetting("extent")||[0,i.categories.length-1],n}return(0,i.ZT)(e,t),e.prototype.parse=function(t){return null==t?NaN:(0,s.HD)(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},e.prototype.contain=function(t){return t=this.parse(t),a.XS(t,this._extent)&&null!=this._ordinalMeta.categories[t]},e.prototype.normalize=function(t){return t=this._getTickNumber(this.parse(t)),a.Fv(t,this._extent)},e.prototype.scale=function(t){return t=Math.round(a.bA(t,this._extent)),this.getRawOrdinalNumber(t)},e.prototype.getTicks=function(){var t=[],e=this._extent,n=e[0];while(n<=e[1])t.push({value:n}),n++;return t},e.prototype.getMinorTicks=function(t){},e.prototype.setSortInfo=function(t){if(null!=t){for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];n[r]=s,i[s]=r}for(var u=0;r<o;++r){while(null!=i[u])u++;n.push(u),i[u]=r}}else this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null},e.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getLabel=function(t){if(!this.isBlank()){var e=this.getRawOrdinalNumber(t.value),n=this._ordinalMeta.categories[e];return null==n?"":n+""}},e.prototype.count=function(){return this._extent[1]-this._extent[0]+1},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},e.prototype.getOrdinalMeta=function(){return this._ordinalMeta},e.prototype.calcNiceTicks=function(){},e.prototype.calcNiceExtent=function(){},e.type="ordinal",e}(r.Z);r.Z.registerClass(u),e["Z"]=u},60379:function(t,e,n){var i=n(34251),r=function(){function t(t){this._setting=t||{},this._extent=[1/0,-1/0]}return t.prototype.getSetting=function(t){return this._setting[t]},t.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},t.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},t.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},t.prototype.isBlank=function(){return this._isBlank},t.prototype.setBlank=function(t){this._isBlank=t},t}();i.au(r),e["Z"]=r},61618:function(t,e,n){var i=n(70655),r=n(85669),o=n(15015),a=n(65021),s=n(70103),u=n(60379),l=n(33051),h=function(t,e,n,i){while(n<i){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n},c=function(t){function e(e){var n=t.call(this,e)||this;return n.type="time",n}return(0,i.ZT)(e,t),e.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return(0,o.WU)(t.value,o.V8[(0,o.xC)((0,o.Tj)(this._minLevelUnit))]||o.V8.second,e,this.getSetting("locale"))},e.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC"),r=this.getSetting("locale");return(0,o.k7)(t,e,n,r,i)},e.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];if(!t)return n;n.push({value:e[0],level:0});var i=this.getSetting("useUTC"),r=x(this._minLevelUnit,this._approxInterval,i,e);return n=n.concat(r),n.push({value:e[1],level:0}),n},e.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=o.s2,e[1]+=o.s2),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-o.s2}this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},e.prototype.calcNiceTicks=function(t,e,n){t=t||10;var i=this._extent,r=i[1]-i[0];this._approxInterval=r/t,null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n);var o=f.length,a=Math.min(h(f,this._approxInterval,0,o),o-1);this._interval=f[a][1],this._minLevelUnit=f[Math.max(a-1,0)][0]},e.prototype.parse=function(t){return(0,l.hj)(t)?t:+r.sG(t)},e.prototype.contain=function(t){return a.XS(this.parse(t),this._extent)},e.prototype.normalize=function(t){return a.Fv(this.parse(t),this._extent)},e.prototype.scale=function(t){return a.bA(t,this._extent)},e.type="time",e}(s.Z),f=[["second",o.WT],["minute",o.yR],["hour",o.dV],["quarter-day",6*o.dV],["half-day",12*o.dV],["day",1.2*o.s2],["half-week",3.5*o.s2],["week",7*o.s2],["month",31*o.s2],["quarter",95*o.s2],["half-year",o.P5/2],["year",o.P5]];function p(t,e,n,i){var a=r.sG(e),s=r.sG(n),u=function(t){return(0,o.q5)(a,t,i)===(0,o.q5)(s,t,i)},l=function(){return u("year")},h=function(){return l()&&u("month")},c=function(){return h()&&u("day")},f=function(){return c()&&u("hour")},p=function(){return f()&&u("minute")},d=function(){return p()&&u("second")},v=function(){return d()&&u("millisecond")};switch(t){case"year":return l();case"month":return h();case"day":return c();case"hour":return f();case"minute":return p();case"second":return d();case"millisecond":return v()}}function d(t,e){return t/=o.s2,t>16?16:t>7.5?7:t>3.5?4:t>1.5?2:1}function v(t){var e=30*o.s2;return t/=e,t>6?6:t>3?3:t>2?2:1}function g(t){return t/=o.dV,t>12?12:t>6?6:t>3.5?4:t>2?2:1}function y(t,e){return t/=e?o.yR:o.WT,t>30?30:t>20?20:t>15?15:t>10?10:t>5?5:t>2?2:1}function m(t){return r.kx(t,!0)}function _(t,e,n){var i=new Date(t);switch((0,o.Tj)(e)){case"year":case"month":i[(0,o.vh)(n)](0);case"day":i[(0,o.f5)(n)](1);case"hour":i[(0,o.En)(n)](0);case"minute":i[(0,o.eN)(n)](0);case"second":i[(0,o.rM)(n)](0),i[(0,o.cb)(n)](0)}return i.getTime()}function x(t,e,n,i){var r=1e4,a=o.FW,s=0;function u(t,e,n,r,o,a,s){var u=new Date(e),l=e,h=u[r]();while(l<n&&l<=i[1])s.push({value:l}),h+=t,u[o](h),l=u.getTime();s.push({value:l,notAdd:!0})}function h(t,r,a){var s=[],l=!r.length;if(!p((0,o.Tj)(t),i[0],i[1],n)){l&&(r=[{value:_(new Date(i[0]),t,n)},{value:i[1]}]);for(var h=0;h<r.length-1;h++){var c=r[h].value,f=r[h+1].value;if(c!==f){var x=void 0,w=void 0,S=void 0,b=!1;switch(t){case"year":x=Math.max(1,Math.round(e/o.s2/365)),w=(0,o.sx)(n),S=(0,o.xL)(n);break;case"half-year":case"quarter":case"month":x=v(e),w=(0,o.CW)(n),S=(0,o.vh)(n);break;case"week":case"half-week":case"day":x=d(e,31),w=(0,o.xz)(n),S=(0,o.f5)(n),b=!0;break;case"half-day":case"quarter-day":case"hour":x=g(e),w=(0,o.Wp)(n),S=(0,o.En)(n);break;case"minute":x=y(e,!0),w=(0,o.fn)(n),S=(0,o.eN)(n);break;case"second":x=y(e,!1),w=(0,o.MV)(n),S=(0,o.rM)(n);break;case"millisecond":x=m(e),w=(0,o.RZ)(n),S=(0,o.cb)(n);break}u(x,c,f,w,S,b,s),"year"===t&&a.length>1&&0===h&&a.unshift({value:a[0].value-x})}}for(h=0;h<s.length;h++)a.push(s[h]);return s}}for(var c=[],f=[],x=0,w=0,S=0;S<a.length&&s++<r;++S){var b=(0,o.Tj)(a[S]);if((0,o.$K)(a[S])){h(a[S],c[c.length-1]||[],f);var T=a[S+1]?(0,o.Tj)(a[S+1]):null;if(b!==T){if(f.length){w=x,f.sort((function(t,e){return t.value-e.value}));for(var M=[],k=0;k<f.length;++k){var D=f[k].value;0!==k&&f[k-1].value===D||(M.push(f[k]),D>=i[0]&&D<=i[1]&&x++)}var C=(i[1]-i[0])/e;if(x>1.5*C&&w>C/1.5)break;if(c.push(M),x>C||t===a[S])break}f=[]}}}var I=(0,l.hX)((0,l.UI)(c,(function(t){return(0,l.hX)(t,(function(t){return t.value>=i[0]&&t.value<=i[1]&&!t.notAdd}))})),(function(t){return t.length>0})),P=[],A=I.length-1;for(S=0;S<I.length;++S)for(var O=I[S],Z=0;Z<O.length;++Z)P.push({value:O[Z].value,level:A-S});P.sort((function(t,e){return t.value-e.value}));var L=[];for(S=0;S<P.length;++S)0!==S&&P[S].value===P[S-1].value||L.push(P[S]);return L}u.Z.registerClass(c),e["Z"]=c},65021:function(t,e,n){n.d(e,{Fv:function(){return c},Qf:function(){return o},XS:function(){return h},bA:function(){return f},lM:function(){return r},lb:function(){return s},r1:function(){return a}});var i=n(85669);function r(t){return"interval"===t.type||"log"===t.type}function o(t,e,n,r){var o={},a=t[1]-t[0],u=o.interval=(0,i.kx)(a/e,!0);null!=n&&u<n&&(u=o.interval=n),null!=r&&u>r&&(u=o.interval=r);var h=o.intervalPrecision=s(u),c=o.niceTickExtent=[(0,i.NM)(Math.ceil(t[0]/u)*u,h),(0,i.NM)(Math.floor(t[1]/u)*u,h)];return l(c,t),o}function a(t){var e=Math.pow(10,(0,i.xW)(t)),n=t/e;return n?2===n?n=3:3===n?n=5:n*=2:n=1,(0,i.NM)(n*e)}function s(t){return(0,i.p8)(t)+2}function u(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function l(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),u(t,0,e),u(t,1,e),t[0]>t[1]&&(t[0]=t[1])}function h(t,e){return t>=e[0]&&t<=e[1]}function c(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function f(t,e){return t*(e[1]-e[0])+e[0]}},34251:function(t,e,n){n.d(e,{PT:function(){return h},Qj:function(){return v},au:function(){return m},dm:function(){return c},pw:function(){return p},u9:function(){return u}});var i=n(70655),r=n(33051),o=".",a="___EC__COMPONENT__CONTAINER___",s="___EC__EXTENDED_CLASS___";function u(t){var e={main:"",sub:""};if(t){var n=t.split(o);e.main=n[0]||"",e.sub=n[1]||""}return e}function l(t){r.hu(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function h(t){return!(!t||!t[s])}function c(t,e){t.$constructor=t,t.extend=function(t){var e,n=this;return f(n)?e=function(t){function e(){return t.apply(this,arguments)||this}return(0,i.ZT)(e,t),e}(n):(e=function(){(t.$constructor||n).apply(this,arguments)},r.XW(e,this)),r.l7(e.prototype,t),e[s]=!0,e.extend=this.extend,e.superCall=g,e.superApply=y,e.superClass=n,e}}function f(t){return r.mf(t)&&/^class\s/.test(Function.prototype.toString.call(t))}function p(t,e){t.extend=e.extend}var d=Math.round(10*Math.random());function v(t){var e=["__\0is_clz",d++].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!(!t||!t[e])}}function g(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function y(t,e,n){return this.superClass.prototype[e].apply(t,n)}function m(t){var e={};function n(t){var n=e[t.main];return n&&n[a]||(n=e[t.main]={},n[a]=!0),n}t.registerClass=function(t){var i=t.type||t.prototype.type;if(i){l(i),t.prototype.type=i;var r=u(i);if(r.sub){if(r.sub!==a){var o=n(r);o[r.sub]=t}}else e[r.main]=t}return t},t.getClass=function(t,n,i){var r=e[t];if(r&&r[a]&&(r=n?r[n]:null),i&&!r)throw new Error(n?"Component "+t+"."+(n||"")+" is used but not imported.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){var n=u(t),i=[],o=e[n.main];return o&&o[a]?r.S6(o,(function(t,e){e!==a&&i.push(t)})):i.push(o),i},t.hasClass=function(t){var n=u(t);return!!e[n.main]},t.getAllClassMainTypes=function(){var t=[];return r.S6(e,(function(e,n){t.push(n)})),t},t.hasSubTypes=function(t){var n=u(t),i=e[n.main];return i&&i[a]}}},42151:function(t,e,n){n.d(e,{Kr:function(){return a},ZL:function(){return l},cj:function(){return s},jS:function(){return u}});var i=n(33051),r=n(34251),o=Math.round(10*Math.random());function a(t){return[t||"",o++].join("_")}function s(t){var e={};t.registerSubTypeDefaulter=function(t,n){var i=(0,r.u9)(t);e[i.main]=n},t.determineSubType=function(n,i){var o=i.type;if(!o){var a=(0,r.u9)(n).main;t.hasSubTypes(n)&&e[a]&&(o=e[a](i))}return o}}function u(t,e){function n(t){var n={},a=[];return i.S6(t,(function(s){var u=r(n,s),l=u.originalDeps=e(s),h=o(l,t);u.entryCount=h.length,0===u.entryCount&&a.push(s),i.S6(h,(function(t){i.cq(u.predecessor,t)<0&&u.predecessor.push(t);var e=r(n,t);i.cq(e.successor,t)<0&&e.successor.push(s)}))})),{graph:n,noEntryList:a}}function r(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function o(t,e){var n=[];return i.S6(t,(function(t){i.cq(e,t)>=0&&n.push(t)})),n}t.topologicalTravel=function(t,e,r,o){if(t.length){var a=n(e),s=a.graph,u=a.noEntryList,l={};i.S6(t,(function(t){l[t]=!0}));while(u.length){var h=u.pop(),c=s[h],f=!!l[h];f&&(r.call(o,h,c.originalDeps.slice()),delete l[h]),i.S6(c.successor,f?d:p)}i.S6(l,(function(){var t="";throw new Error(t)}))}function p(t){s[t].entryCount--,0===s[t].entryCount&&u.push(t)}function d(t){l[t]=!0,p(t)}}}function l(t,e){return i.TS(i.TS({},t,!0),e,!0)}},630:function(t,e,n){n.d(e,{I:function(){return g}});var i=Math.round(9*Math.random()),r="function"===typeof Object.defineProperty,o=function(){function t(){this._id="__ec_inner_"+i++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var n=this._guard(t);return r?Object.defineProperty(n,this._id,{value:e,enumerable:!1,configurable:!0}):n[this._id]=e,this},t.prototype["delete"]=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}(),a=o,s=n(92528),u=n(33051),l=n(85669),h=n(41525),c=n(97772),f=n(23132),p=new a,d=new s.ZP(100),v=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function g(t,e){if("none"===t)return null;var n=e.getDevicePixelRatio(),i=e.getZr(),r="svg"===i.painter.type;t.dirty&&p["delete"](t);var o=p.get(t);if(o)return o;var a=(0,u.ce)(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});"none"===a.backgroundColor&&(a.backgroundColor=null);var s={repeat:"repeat"};return g(s),s.rotation=a.rotation,s.scaleX=s.scaleY=r?1:1/n,p.set(t,s),t.dirty=!1,s;function g(t){for(var e,o=[n],s=!0,p=0;p<v.length;++p){var g=a[v[p]];if(null!=g&&!(0,u.kJ)(g)&&!(0,u.HD)(g)&&!(0,u.hj)(g)&&"boolean"!==typeof g){s=!1;break}o.push(g)}if(s){e=o.join(",")+(r?"-svg":"");var S=d.get(e);S&&(r?t.svgElement=S:t.image=S)}var b,T=m(a.dashArrayX),M=_(a.dashArrayY),k=y(a.symbol),D=x(T),C=w(M),I=!r&&f.qW.createCanvas(),P=r&&{tag:"g",attrs:{},key:"dcl",children:[]},A=O();function O(){for(var t=1,e=0,n=D.length;e<n;++e)t=(0,l.nl)(t,D[e]);var i=1;for(e=0,n=k.length;e<n;++e)i=(0,l.nl)(i,k[e].length);t*=i;var r=C*D.length*k.length;return{width:Math.max(1,Math.min(t,a.maxTileWidth)),height:Math.max(1,Math.min(r,a.maxTileHeight))}}function Z(){b&&(b.clearRect(0,0,I.width,I.height),a.backgroundColor&&(b.fillStyle=a.backgroundColor,b.fillRect(0,0,I.width,I.height)));for(var t=0,e=0;e<M.length;++e)t+=M[e];if(!(t<=0)){var o=-C,s=0,u=0,l=0;while(o<A.height){if(s%2===0){var f=u/2%k.length,p=0,d=0,v=0;while(p<2*A.width){var g=0;for(e=0;e<T[l].length;++e)g+=T[l][e];if(g<=0)break;if(d%2===0){var y=.5*(1-a.symbolSize),m=p+T[l][d]*y,_=o+M[s]*y,x=T[l][d]*a.symbolSize,w=M[s]*a.symbolSize,S=v/2%k[f].length;D(m,_,x,w,k[f][S])}p+=T[l][d],++v,++d,d===T[l].length&&(d=0)}++l,l===T.length&&(l=0)}o+=M[s],++u,++s,s===M.length&&(s=0)}}function D(t,e,o,s,u){var l=r?1:n,f=(0,h.th)(u,t*l,e*l,o*l,s*l,a.color,a.symbolKeepAspect);if(r){var p=i.painter.renderOneToVNode(f);p&&P.children.push(p)}else(0,c.RV)(b,f)}}I&&(I.width=A.width*n,I.height=A.height*n,b=I.getContext("2d")),Z(),s&&d.put(e,I||P),t.image=I,t.svgElement=P,t.svgWidth=A.width,t.svgHeight=A.height}}function y(t){if(!t||0===t.length)return[["rect"]];if((0,u.HD)(t))return[[t]];for(var e=!0,n=0;n<t.length;++n)if(!(0,u.HD)(t[n])){e=!1;break}if(e)return y([t]);var i=[];for(n=0;n<t.length;++n)(0,u.HD)(t[n])?i.push([t[n]]):i.push(t[n]);return i}function m(t){if(!t||0===t.length)return[[0,0]];if((0,u.hj)(t)){var e=Math.ceil(t);return[[e,e]]}for(var n=!0,i=0;i<t.length;++i)if(!(0,u.hj)(t[i])){n=!1;break}if(n)return m([t]);var r=[];for(i=0;i<t.length;++i)if((0,u.hj)(t[i])){e=Math.ceil(t[i]);r.push([e,e])}else{e=(0,u.UI)(t[i],(function(t){return Math.ceil(t)}));e.length%2===1?r.push(e.concat(e)):r.push(e)}return r}function _(t){if(!t||"object"===typeof t&&0===t.length)return[0,0];if((0,u.hj)(t)){var e=Math.ceil(t);return[e,e]}var n=(0,u.UI)(t,(function(t){return Math.ceil(t)}));return t.length%2?n.concat(n):n}function x(t){return(0,u.UI)(t,(function(t){return w(t)}))}function w(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2===1?2*e:e}},18310:function(t,e,n){function i(t,e,n){var i;while(t){if(e(t)&&(i=t,n))break;t=t.__hostTarget||t.parent}return i}n.d(e,{o:function(){return i}})},78988:function(t,e,n){n.d(e,{A0:function(){return y},F1:function(){return c},Lz:function(){return x},MI:function(){return w},MY:function(){return u},OD:function(){return a},ew:function(){return _},kF:function(){return v},mr:function(){return m},uX:function(){return f},wx:function(){return g},zW:function(){return s}});var i=n(33051),r=n(85669),o=n(15015);function a(t){if(!(0,r.kE)(t))return i.HD(t)?t:"-";var e=(t+"").split(".");return e[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(e.length>1?"."+e[1]:"")}function s(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,(function(t,e){return e.toUpperCase()})),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}var u=i.MY,l=/([&<>"'])/g,h={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function c(t){return null==t?"":(t+"").replace(l,(function(t,e){return h[e]}))}function f(t,e,n){var s="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function u(t){return t&&i.fy(t)?t:"-"}function l(t){return!(null==t||isNaN(t)||!isFinite(t))}var h="time"===e,c=t instanceof Date;if(h||c){var f=h?(0,r.sG)(t):t;if(!isNaN(+f))return(0,o.WU)(f,s,n);if(c)return"-"}if("ordinal"===e)return i.cd(t)?u(t):i.hj(t)&&l(t)?t+"":"-";var p=(0,r.FK)(t);return l(p)?a(p):i.cd(t)?u(t):"boolean"===typeof t?t+"":"-"}var p=["a","b","c","d","e","f","g"],d=function(t,e){return"{"+t+(null==e?"":e)+"}"};function v(t,e,n){i.kJ(e)||(e=[e]);var r=e.length;if(!r)return"";for(var o=e[0].$vars||[],a=0;a<o.length;a++){var s=p[a];t=t.replace(d(s),d(s,0))}for(var u=0;u<r;u++)for(var l=0;l<o.length;l++){var h=e[u][o[l]];t=t.replace(d(p[l],u),n?c(h):h)}return t}function g(t,e,n){return i.S6(e,(function(e,i){t=t.replace("{"+i+"}",n?c(e):e)})),t}function y(t,e){var n=i.HD(t)?{color:t,extraCssText:e}:t||{},r=n.color,o=n.type;e=n.extraCssText;var a=n.renderMode||"html";if(!r)return"";if("html"===a)return"subItem"===o?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+c(r)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+c(r)+";"+(e||"")+'"></span>';var s=n.markerId||"markerX";return{renderMode:a,content:"{"+s+"|}  ",style:"subItem"===o?{width:4,height:4,borderRadius:2,backgroundColor:r}:{width:10,height:10,borderRadius:5,backgroundColor:r}}}function m(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=(0,r.sG)(e),a=n?"getUTC":"get",s=i[a+"FullYear"](),u=i[a+"Month"]()+1,l=i[a+"Date"](),h=i[a+"Hours"](),c=i[a+"Minutes"](),f=i[a+"Seconds"](),p=i[a+"Milliseconds"]();return t=t.replace("MM",(0,o.vk)(u,2)).replace("M",u).replace("yyyy",s).replace("yy",(0,o.vk)(s%100+"",2)).replace("dd",(0,o.vk)(l,2)).replace("d",l).replace("hh",(0,o.vk)(h,2)).replace("h",h).replace("mm",(0,o.vk)(c,2)).replace("m",c).replace("ss",(0,o.vk)(f,2)).replace("s",f).replace("SSS",(0,o.vk)(p,3)),t}function _(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}function x(t,e){return e=e||"transparent",i.HD(t)?t:i.Kn(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}function w(t,e){if("_blank"===e||"blank"===e){var n=window.open();n.opener=null,n.location.href=t}else window.open(t,e)}},46496:function(t,e,n){n.r(e),n.d(e,{Arc:function(){return x.Z},BezierCurve:function(){return _.Z},BoundingRect:function(){return T.Z},Circle:function(){return c.Z},CompoundPath:function(){return w.Z},Ellipse:function(){return f.Z},Group:function(){return l.Z},Image:function(){return u.ZP},IncrementalDisplayable:function(){return A},Line:function(){return m.Z},LinearGradient:function(){return S.Z},OrientedBoundingRect:function(){return M.Z},Path:function(){return a.ZP},Point:function(){return k.Z},Polygon:function(){return v.Z},Polyline:function(){return g.Z},RadialGradient:function(){return b.Z},Rect:function(){return y.Z},Ring:function(){return d.Z},Sector:function(){return p.C},Text:function(){return h.ZP},applyTransform:function(){return $},clipPointsByRect:function(){return rt},clipRectByRect:function(){return ot},createIcon:function(){return at},extendPath:function(){return H},extendShape:function(){return E},getShapeClass:function(){return V},getTransform:function(){return Q},groupTransition:function(){return it},initProps:function(){return R.KZ},isElementRemoved:function(){return R.eq},lineLineIntersect:function(){return ut},linePolygonIntersect:function(){return st},makeImage:function(){return G},makePath:function(){return U},mergePath:function(){return Y},registerShape:function(){return W},removeElement:function(){return R.bX},removeElementWithFadeOut:function(){return R.XD},resizePath:function(){return q},setTooltipConfig:function(){return ct},subPixelOptimize:function(){return J},subPixelOptimizeLine:function(){return K},subPixelOptimizeRect:function(){return j},transformDirection:function(){return tt},traverseElements:function(){return pt},updateProps:function(){return R.D}});var i=n(80073),r=n(32892),o=n(45280),a=n(8846),s=n(87411),u=n(44535),l=n(38154),h=n(96498),c=n(69538),f=n(92797),p=n(97782),d=n(85795),v=n(95094),g=n(62514),y=n(25293),m=n(22095),_=n(54174),x=n(14826),w=n(52776),S=n(74438),b=n(36369),T=n(60479),M=n(41587),k=n(41610),D=n(70655),C=n(7719),I=[],P=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return(0,D.ZT)(e,t),e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.useStyle=function(){this.style={}},e.prototype.getCursor=function(){return this._cursor},e.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},e.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},e.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},e.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},e.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},e.prototype.getDisplayables=function(){return this._displayables},e.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},e.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},e.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},e.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new T.Z(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(I)),t.union(i)}this._rect=t}return this._rect},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();if(i.contain(n[0],n[1]))for(var r=0;r<this._displayables.length;r++){var o=this._displayables[r];if(o.contain(t,e))return!0}return!1},e}(C.ZP),A=P,O=n(24111),Z=n(33051),L=n(30106),R=n(29266),z=Math.max,B=Math.min,N={};function E(t){return a.ZP.extend(t)}var F=i.Pc;function H(t,e){return F(t,e)}function W(t,e){N[t]=e}function V(t){if(N.hasOwnProperty(t))return N[t]}function U(t,e,n,r){var o=i.iR(t,e);return n&&("center"===r&&(n=X(n,o.getBoundingRect())),q(o,n)),o}function G(t,e,n){var i=new u.ZP({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(X(e,r))}}});return i}function X(t,e){var n,i=e.width/e.height,r=t.height*i;r<=t.width?n=t.height:(r=t.width,n=r/i);var o=t.x+t.width/2,a=t.y+t.height/2;return{x:o-r/2,y:a-n/2,width:r,height:n}}var Y=i.AA;function q(t,e){if(t.applyTransform){var n=t.getBoundingRect(),i=n.calculateTransform(e);t.applyTransform(i)}}function K(t){return O._3(t.shape,t.shape,t.style),t}function j(t){return O.Pw(t.shape,t.shape,t.style),t}var J=O.vu;function Q(t,e){var n=r.yR([]);while(t&&t!==e)r.dC(n,t.getLocalTransform(),n),t=t.parent;return n}function $(t,e,n){return e&&!(0,Z.zG)(e)&&(e=s.ZP.getLocalTransform(e)),n&&(e=r.U_([],e)),o.Ne([],t,e)}function tt(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0];return o=$(o,e,n),Math.abs(o[0])>Math.abs(o[1])?o[0]>0?"right":"left":o[1]>0?"bottom":"top"}function et(t){return!t.isGroup}function nt(t){return null!=t.shape}function it(t,e,n){if(t&&e){var i=r(t);e.traverse((function(t){if(et(t)&&t.anid){var e=i[t.anid];if(e){var r=o(t);t.attr(o(e)),(0,R.D)(t,r,n,(0,L.A)(t).dataIndex)}}}))}function r(t){var e={};return t.traverse((function(t){et(t)&&t.anid&&(e[t.anid]=t)})),e}function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return nt(t)&&(e.shape=(0,Z.l7)({},t.shape)),e}}function rt(t,e){return(0,Z.UI)(t,(function(t){var n=t[0];n=z(n,e.x),n=B(n,e.x+e.width);var i=t[1];return i=z(i,e.y),i=B(i,e.y+e.height),[n,i]}))}function ot(t,e){var n=z(t.x,e.x),i=B(t.x+t.width,e.x+e.width),r=z(t.y,e.y),o=B(t.y+t.height,e.y+e.height);if(i>=n&&o>=r)return{x:n,y:r,width:i-n,height:o-r}}function at(t,e,n){var i=(0,Z.l7)({rectHover:!0},e),r=i.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(r.image=t.slice(8),(0,Z.ce)(r,n),new u.ZP(i)):U(t.replace("path://",""),i,n,"center")}function st(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(ut(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}}function ut(t,e,n,i,r,o,a,s){var u=n-t,l=i-e,h=a-r,c=s-o,f=lt(h,c,u,l);if(ht(f))return!1;var p=t-r,d=e-o,v=lt(p,d,u,l)/f;if(v<0||v>1)return!1;var g=lt(p,d,h,c)/f;return!(g<0||g>1)}function lt(t,e,n,i){return t*i-n*e}function ht(t){return t<=1e-6&&t>=-1e-6}function ct(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,r=(0,Z.HD)(e)?{formatter:e}:e,o=n.mainType,a=n.componentIndex,s={componentType:o,name:i,$vars:["name"]};s[o+"Index"]=a;var u=t.formatterParamsExtra;u&&(0,Z.S6)((0,Z.XP)(u),(function(t){(0,Z.RI)(s,t)||(s[t]=u[t],s.$vars.push(t))}));var l=(0,L.A)(t.el);l.componentMainType=o,l.componentIndex=a,l.tooltipConfig={name:i,option:(0,Z.ce)({content:i,formatterParams:s},r)}}function ft(t,e){var n;t.isGroup&&(n=e(t)),n||t.traverse(e)}function pt(t,e){if(t)if((0,Z.kJ)(t))for(var n=0;n<t.length;n++)ft(t[n],e);else ft(t,e)}W("circle",c.Z),W("ellipse",f.Z),W("sector",p.C),W("ring",d.Z),W("polygon",v.Z),W("polyline",g.Z),W("rect",y.Z),W("line",m.Z),W("bezierCurve",_.Z),W("arc",x.Z)},30106:function(t,e,n){n.d(e,{A:function(){return r},Q:function(){return o}});var i=n(32234),r=(0,i.Yf)(),o=function(t,e,n,i){if(i){var o=r(i);o.dataIndex=n,o.dataType=e,o.seriesIndex=t,"group"===i.type&&i.traverse((function(i){var o=r(i);o.seriesIndex=t,o.dataIndex=n,o.dataType=e}))}}},76172:function(t,e,n){n.d(e,{BZ:function(){return c},ME:function(){return p},QM:function(){return v},QT:function(){return _},YD:function(){return g},dt:function(){return y},gN:function(){return u},lq:function(){return f},p$:function(){return d},tE:function(){return m}});var i=n(33051),r=n(60479),o=n(85669),a=n(78988),s=i.S6,u=["left","right","top","bottom","width","height"],l=[["width","left","right"],["height","top","bottom"]];function h(t,e,n,i,r){var o=0,a=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild((function(u,l){var h,c,f=u.getBoundingRect(),p=e.childAt(l+1),d=p&&p.getBoundingRect();if("horizontal"===t){var v=f.width+(d?-d.x+f.x:0);h=o+v,h>i||u.newline?(o=0,h=v,a+=s+n,s=f.height):s=Math.max(s,f.height)}else{var g=f.height+(d?-d.y+f.y:0);c=a+g,c>r||u.newline?(o+=s+n,a=0,c=g,s=f.width):s=Math.max(s,f.width)}u.newline||(u.x=o,u.y=a,u.markRedraw(),"horizontal"===t?o=h+n:a=c+n)}))}var c=h;i.WA(h,"vertical"),i.WA(h,"horizontal");function f(t,e,n){var i=e.width,r=e.height,s=(0,o.GM)(t.left,i),u=(0,o.GM)(t.top,r),l=(0,o.GM)(t.right,i),h=(0,o.GM)(t.bottom,r);return(isNaN(s)||isNaN(parseFloat(t.left)))&&(s=0),(isNaN(l)||isNaN(parseFloat(t.right)))&&(l=i),(isNaN(u)||isNaN(parseFloat(t.top)))&&(u=0),(isNaN(h)||isNaN(parseFloat(t.bottom)))&&(h=r),n=a.MY(n||0),{width:Math.max(l-s-n[1]-n[3],0),height:Math.max(h-u-n[0]-n[2],0)}}function p(t,e,n){n=a.MY(n||0);var i=e.width,s=e.height,u=(0,o.GM)(t.left,i),l=(0,o.GM)(t.top,s),h=(0,o.GM)(t.right,i),c=(0,o.GM)(t.bottom,s),f=(0,o.GM)(t.width,i),p=(0,o.GM)(t.height,s),d=n[2]+n[0],v=n[1]+n[3],g=t.aspect;switch(isNaN(f)&&(f=i-h-v-u),isNaN(p)&&(p=s-c-d-l),null!=g&&(isNaN(f)&&isNaN(p)&&(g>i/s?f=.8*i:p=.8*s),isNaN(f)&&(f=g*p),isNaN(p)&&(p=f/g)),isNaN(u)&&(u=i-h-f-v),isNaN(l)&&(l=s-c-p-d),t.left||t.right){case"center":u=i/2-f/2-n[3];break;case"right":u=i-f-v;break}switch(t.top||t.bottom){case"middle":case"center":l=s/2-p/2-n[0];break;case"bottom":l=s-p-d;break}u=u||0,l=l||0,isNaN(f)&&(f=i-v-u-(h||0)),isNaN(p)&&(p=s-d-l-(c||0));var y=new r.Z(u+n[3],l+n[0],f,p);return y.margin=n,y}function d(t,e,n,o,a,s){var u,l=!a||!a.hv||a.hv[0],h=!a||!a.hv||a.hv[1],c=a&&a.boundingMode||"all";if(s=s||t,s.x=t.x,s.y=t.y,!l&&!h)return!1;if("raw"===c)u="group"===t.type?new r.Z(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(u=t.getBoundingRect(),t.needLocalTransform()){var f=t.getLocalTransform();u=u.clone(),u.applyTransform(f)}var d=p(i.ce({width:u.width,height:u.height},e),n,o),v=l?d.x-u.x:0,g=h?d.y-u.y:0;return"raw"===c?(s.x=v,s.y=g):(s.x+=v,s.y+=g),s===t&&t.markRedraw(),!0}function v(t,e){return null!=t[l[e][0]]||null!=t[l[e][1]]&&null!=t[l[e][2]]}function g(t){var e=t.layoutMode||t.constructor.layoutMode;return i.Kn(e)?e:e?{type:e}:null}function y(t,e,n){var r=n&&n.ignoreSize;!i.kJ(r)&&(r=[r,r]);var o=u(l[0],0),a=u(l[1],1);function u(n,i){var o={},a=0,u={},l=0,f=2;if(s(n,(function(e){u[e]=t[e]})),s(n,(function(t){h(e,t)&&(o[t]=u[t]=e[t]),c(o,t)&&a++,c(u,t)&&l++})),r[i])return c(e,n[1])?u[n[2]]=null:c(e,n[2])&&(u[n[1]]=null),u;if(l!==f&&a){if(a>=f)return o;for(var p=0;p<n.length;p++){var d=n[p];if(!h(o,d)&&h(t,d)){o[d]=t[d];break}}return o}return u}function h(t,e){return t.hasOwnProperty(e)}function c(t,e){return null!=t[e]&&"auto"!==t[e]}function f(t,e,n){s(t,(function(t){e[t]=n[t]}))}f(l[0],t,o),f(l[1],t,a)}function m(t){return _({},t)}function _(t,e){return e&&t&&s(u,(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t}},70175:function(t,e,n){n.d(e,{Sh:function(){return u},ZK:function(){return s},_y:function(){return l}});var i="[ECharts] ",r={},o="undefined"!==typeof console&&console.warn&&console.log;function a(t,e,n){if(o){if(n){if(r[e])return;r[e]=!0}console[t](i+e)}}function s(t,e){a("warn",t,e)}function u(t){0}function l(t){throw new Error(t)}},32234:function(t,e,n){n.d(e,{C4:function(){return f},C6:function(){return R},Cc:function(){return h},Co:function(){return p},HZ:function(){return B},IL:function(){return E},O0:function(){return D},P$:function(){return N},Td:function(){return c},U5:function(){return b},U9:function(){return F},XI:function(){return I},Yf:function(){return A},ab:function(){return d},g0:function(){return k},gO:function(){return P},iP:function(){return z},kF:function(){return l},lY:function(){return M},pk:function(){return W},pm:function(){return Z},pv:function(){return H},yu:function(){return T},zH:function(){return L}});var i=n(33051),r=n(66387),o=n(85669);function a(t,e,n){return(e-t)*n+t}var s="series\0",u="\0_ec_\0";function l(t){return t instanceof Array?t:null==t?[]:[t]}function h(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var c=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function f(t){return!(0,i.Kn)(t)||(0,i.kJ)(t)||t instanceof Date?t:t.value}function p(t){return(0,i.Kn)(t)&&!(t instanceof Array)}function d(t,e,n){var r="normalMerge"===n,o="replaceMerge"===n,a="replaceAll"===n;t=t||[],e=(e||[]).slice();var s=(0,i.kW)();(0,i.S6)(e,(function(t,n){(0,i.Kn)(t)||(e[n]=null)}));var u=v(t,s,n);return(r||o)&&g(u,t,s,e),r&&y(u,e),r||o?m(u,e,o):a&&_(u,e),x(u),u}function v(t,e,n){var i=[];if("replaceAll"===n)return i;for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||M(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}function g(t,e,n,r){(0,i.S6)(r,(function(o,a){if(o&&null!=o.id){var s=S(o.id),u=n.get(s);if(null!=u){var l=t[u];(0,i.hu)(!l.newOption,'Duplicated option on id "'+s+'".'),l.newOption=o,l.existing=e[u],r[a]=null}}}))}function y(t,e){(0,i.S6)(e,(function(n,i){if(n&&null!=n.name)for(var r=0;r<t.length;r++){var o=t[r].existing;if(!t[r].newOption&&o&&(null==o.id||null==n.id)&&!M(n)&&!M(o)&&w("name",o,n))return t[r].newOption=n,void(e[i]=null)}}))}function m(t,e,n){(0,i.S6)(e,(function(e){if(e){var i,r=0;while((i=t[r])&&(i.newOption||M(i.existing)||i.existing&&null!=e.id&&!w("id",e,i.existing)))r++;i?(i.newOption=e,i.brandNew=n):t.push({newOption:e,brandNew:n,existing:null,keyInfo:null}),r++}}))}function _(t,e){(0,i.S6)(e,(function(e){t.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})}))}function x(t){var e=(0,i.kW)();(0,i.S6)(t,(function(t){var n=t.existing;n&&e.set(n.id,t)})),(0,i.S6)(t,(function(t){var n=t.newOption;(0,i.hu)(!n||null==n.id||!e.get(n.id)||e.get(n.id)===t,"id duplicates: "+(n&&n.id)),n&&null!=n.id&&e.set(n.id,t),!t.keyInfo&&(t.keyInfo={})})),(0,i.S6)(t,(function(t,n){var r=t.existing,o=t.newOption,a=t.keyInfo;if((0,i.Kn)(o)){if(a.name=null!=o.name?S(o.name):r?r.name:s+n,r)a.id=S(r.id);else if(null!=o.id)a.id=S(o.id);else{var u=0;do{a.id="\0"+a.name+"\0"+u++}while(e.get(a.id))}e.set(a.id,t)}}))}function w(t,e,n){var i=b(e[t],null),r=b(n[t],null);return null!=i&&null!=r&&i===r}function S(t){return b(t,"")}function b(t,e){return null==t?e:(0,i.HD)(t)?t:(0,i.hj)(t)||(0,i.cd)(t)?t+"":e}function T(t){var e=t.name;return!(!e||!e.indexOf(s))}function M(t){return t&&null!=t.id&&0===S(t.id).indexOf(u)}function k(t){return u+t}function D(t,e,n){(0,i.S6)(t,(function(t){var r=t.newOption;(0,i.Kn)(r)&&(t.keyInfo.mainType=e,t.keyInfo.subType=C(e,r,t.existing,n))}))}function C(t,e,n,i){var r=e.type?e.type:n?n.subType:i.determineSubType(t,e);return r}function I(t,e){var n={},i={};return r(t||[],n),r(e||[],i,n),[o(n),o(i)];function r(t,e,n){for(var i=0,r=t.length;i<r;i++){var o=b(t[i].seriesId,null);if(null==o)return;for(var a=l(t[i].dataIndex),s=n&&n[o],u=0,h=a.length;u<h;u++){var c=a[u];s&&s[c]?s[c]=null:(e[o]||(e[o]={}))[c]=1}}}function o(t,e){var n=[];for(var i in t)if(t.hasOwnProperty(i)&&null!=t[i])if(e)n.push(+i);else{var r=o(t[i],!0);r.length&&n.push({seriesId:i,dataIndex:r})}return n}}function P(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?(0,i.kJ)(e.dataIndex)?(0,i.UI)(e.dataIndex,(function(e){return t.indexOfRawIndex(e)})):t.indexOfRawIndex(e.dataIndex):null!=e.name?(0,i.kJ)(e.name)?(0,i.UI)(e.name,(function(e){return t.indexOfName(e)})):t.indexOfName(e.name):void 0}function A(){var t="__ec_inner_"+O++;return function(e){return e[t]||(e[t]={})}}var O=(0,o.jj)();function Z(t,e,n){var i=L(e,n),r=i.mainTypeSpecified,o=i.queryOptionMap,a=i.others,s=a,u=n?n.defaultMainType:null;return!r&&u&&o.set(u,{}),o.each((function(e,i){var r=B(t,i,e,{useDefault:u===i,enableAll:!n||null==n.enableAll||n.enableAll,enableNone:!n||null==n.enableNone||n.enableNone});s[i+"Models"]=r.models,s[i+"Model"]=r.models[0]})),s}function L(t,e){var n;if((0,i.HD)(t)){var r={};r[t+"Index"]=0,n=r}else n=t;var o=(0,i.kW)(),a={},s=!1;return(0,i.S6)(n,(function(t,n){if("dataIndex"!==n&&"dataIndexInside"!==n){var r=n.match(/^(\w+)(Index|Id|Name)$/)||[],u=r[1],l=(r[2]||"").toLowerCase();if(u&&l&&!(e&&e.includeMainTypes&&(0,i.cq)(e.includeMainTypes,u)<0)){s=s||!!u;var h=o.get(u)||o.set(u,{});h[l]=t}}else a[n]=t})),{mainTypeSpecified:s,queryOptionMap:o,others:a}}var R={useDefault:!0,enableAll:!1,enableNone:!1},z={useDefault:!1,enableAll:!0,enableNone:!0};function B(t,e,n,r){r=r||R;var o=n.index,a=n.id,s=n.name,u={models:null,specified:null!=o||null!=a||null!=s};if(!u.specified){var l=void 0;return u.models=r.useDefault&&(l=t.getComponent(e))?[l]:[],u}return"none"===o||!1===o?((0,i.hu)(r.enableNone,'`"none"` or `false` is not a valid value on index option.'),u.models=[],u):("all"===o&&((0,i.hu)(r.enableAll,'`"all"` is not a valid value on index option.'),o=a=s=null),u.models=t.queryComponents({mainType:e,index:o,id:a,name:s}),u)}function N(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function E(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function F(t){return"auto"===t?r.Z.domSupported?"html":"richText":t||"html"}function H(t,e){var n=(0,i.kW)(),r=[];return(0,i.S6)(t,(function(t){var i=e(t);(n.get(i)||(r.push(i),n.set(i,[]))).push(t)})),{keys:r,buckets:n}}function W(t,e,n,r,s){var u=null==e||"auto"===e;if(null==r)return r;if((0,i.hj)(r)){var l=a(n||0,r,s);return(0,o.NM)(l,u?Math.max((0,o.p8)(n||0),(0,o.p8)(r)):e)}if((0,i.HD)(r))return s<1?n:r;for(var h=[],c=n,f=r,p=Math.max(c?c.length:0,f.length),d=0;d<p;++d){var v=t.getDimensionInfo(d);if(v&&"ordinal"===v.type)h[d]=(s<1&&c?c:f)[d];else{var g=c&&c[d]?c[d]:0,y=f[d];l=a(g,y,s);h[d]=(0,o.NM)(l,u?Math.max((0,o.p8)(g),(0,o.p8)(y)):e)}}return h}},26357:function(t,e,n){n.d(e,{$l:function(){return rt},Av:function(){return yt},C5:function(){return ut},CX:function(){return d},Gl:function(){return B},Hg:function(){return S},Ib:function(){return ft},JQ:function(){return b},Ki:function(){return x},L1:function(){return g},MA:function(){return V},Mh:function(){return Y},Nj:function(){return gt},RW:function(){return _t},SJ:function(){return J},SX:function(){return q},T5:function(){return $},UL:function(){return nt},VP:function(){return K},WO:function(){return vt},XX:function(){return j},aG:function(){return xt},ci:function(){return st},e9:function(){return St},fD:function(){return X},iK:function(){return T},k5:function(){return ct},oJ:function(){return it},og:function(){return at},qc:function(){return y},th:function(){return mt},vF:function(){return lt},wU:function(){return v},xp:function(){return wt},xr:function(){return ot},yx:function(){return w},zI:function(){return et},zr:function(){return m}});var i=n(92528),r=n(33051),o=n(30106),a=n(21092),s=n(32234),u=n(8846),l=1,h={},c=(0,s.Yf)(),f=(0,s.Yf)(),p=0,d=1,v=2,g=["emphasis","blur","select"],y=["normal","emphasis","blur","select"],m=10,_=9,x="highlight",w="downplay",S="select",b="unselect",T="toggleSelect";function M(t){return null!=t&&"none"!==t}var k=new i.ZP(100);function D(t){if((0,r.HD)(t)){var e=k.get(t);return e||(e=a.xb(t,-.1),k.put(t,e)),e}if((0,r.Qq)(t)){var n=(0,r.l7)({},t);return n.colorStops=(0,r.UI)(t.colorStops,(function(t){return{offset:t.offset,color:a.xb(t.color,-.1)}})),n}return t}function C(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function I(t){C(t,"emphasis",v)}function P(t){t.hoverState===v&&C(t,"normal",p)}function A(t){C(t,"blur",d)}function O(t){t.hoverState===d&&C(t,"normal",p)}function Z(t){t.selected=!0}function L(t){t.selected=!1}function R(t,e,n){e(t,n)}function z(t,e,n){R(t,e,n),t.isGroup&&t.traverse((function(t){R(t,e,n)}))}function B(t,e){switch(e){case"emphasis":t.hoverState=v;break;case"normal":t.hoverState=p;break;case"blur":t.hoverState=d;break;case"select":t.selected=!0}}function N(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],u=r[s];o[s]=null==u?i&&i[s]:u}for(a=0;a<t.animators.length;a++){var l=t.animators[a];l.__fromStateTransition&&l.__fromStateTransition.indexOf(n)<0&&"style"===l.targetName&&l.saveTo(o,e)}return o}function E(t,e,n,i){var o=n&&(0,r.cq)(n,"select")>=0,a=!1;if(t instanceof u.ZP){var s=c(t),l=o&&s.selectFill||s.normalFill,h=o&&s.selectStroke||s.normalStroke;if(M(l)||M(h)){i=i||{};var f=i.style||{};"inherit"===f.fill?(a=!0,i=(0,r.l7)({},i),f=(0,r.l7)({},f),f.fill=l):!M(f.fill)&&M(l)?(a=!0,i=(0,r.l7)({},i),f=(0,r.l7)({},f),f.fill=D(l)):!M(f.stroke)&&M(h)&&(a||(i=(0,r.l7)({},i),f=(0,r.l7)({},f)),f.stroke=D(h)),i.style=f}}if(i&&null==i.z2){a||(i=(0,r.l7)({},i));var p=t.z2EmphasisLift;i.z2=t.z2+(null!=p?p:m)}return i}function F(t,e,n){if(n&&null==n.z2){n=(0,r.l7)({},n);var i=t.z2SelectLift;n.z2=t.z2+(null!=i?i:_)}return n}function H(t,e,n){var i=(0,r.cq)(t.currentStates,e)>=0,o=t.style.opacity,a=i?null:N(t,["opacity"],e,{opacity:1});n=n||{};var s=n.style||{};return null==s.opacity&&(n=(0,r.l7)({},n),s=(0,r.l7)({opacity:i?o:.1*a.opacity},s),n.style=s),n}function W(t,e){var n=this.states[t];if(this.style){if("emphasis"===t)return E(this,t,e,n);if("blur"===t)return H(this,t,n);if("select"===t)return F(this,t,n)}return n}function V(t){t.stateProxy=W;var e=t.getTextContent(),n=t.getTextGuideLine();e&&(e.stateProxy=W),n&&(n.stateProxy=W)}function U(t,e){!Q(t,e)&&!t.__highByOuter&&z(t,I)}function G(t,e){!Q(t,e)&&!t.__highByOuter&&z(t,P)}function X(t,e){t.__highByOuter|=1<<(e||0),z(t,I)}function Y(t,e){!(t.__highByOuter&=~(1<<(e||0)))&&z(t,P)}function q(t){z(t,A)}function K(t){z(t,O)}function j(t){z(t,Z)}function J(t){z(t,L)}function Q(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function $(t){var e=t.getModel(),n=[],i=[];e.eachComponent((function(e,r){var o=f(r),a="series"===e,s=a?t.getViewOfSeriesModel(r):t.getViewOfComponentModel(r);!a&&i.push(s),o.isBlured&&(s.group.traverse((function(t){O(t)})),a&&n.push(r)),o.isBlured=!1})),(0,r.S6)(i,(function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(n,!1,e)}))}function tt(t,e,n,i){var o=i.getModel();function a(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&K(i)}}if(n=n||"coordinateSystem",null!=t&&e&&"none"!==e){var s=o.getSeriesByIndex(t),u=s.coordinateSystem;u&&u.master&&(u=u.master);var l=[];o.eachSeries((function(t){var o=s===t,h=t.coordinateSystem;h&&h.master&&(h=h.master);var c=h&&u?h===u:o;if(!("series"===n&&!o||"coordinateSystem"===n&&!c||"series"===e&&o)){var p=i.getViewOfSeriesModel(t);if(p.group.traverse((function(t){A(t)})),(0,r.zG)(e))a(t.getData(),e);else if((0,r.Kn)(e))for(var d=(0,r.XP)(e),v=0;v<d.length;v++)a(t.getData(d[v]),e[d[v]]);l.push(t),f(t).isBlured=!0}})),o.eachComponent((function(t,e){if("series"!==t){var n=i.getViewOfComponentModel(e);n&&n.toggleBlurSeries&&n.toggleBlurSeries(l,!0,o)}}))}}function et(t,e,n){if(null!=t&&null!=e){var i=n.getModel().getComponent(t,e);if(i){f(i).isBlured=!0;var r=n.getViewOfComponentModel(i);r&&r.focusBlurEnabled&&r.group.traverse((function(t){A(t)}))}}}function nt(t,e,n){var i=t.seriesIndex,a=t.getData(e.dataType);if(a){var u=(0,s.gO)(a,e);u=((0,r.kJ)(u)?u[0]:u)||0;var l=a.getItemGraphicEl(u);if(!l){var h=a.count(),c=0;while(!l&&c<h)l=a.getItemGraphicEl(c++)}if(l){var f=(0,o.A)(l);tt(i,f.focus,f.blurScope,n)}else{var p=t.get(["emphasis","focus"]),d=t.get(["emphasis","blurScope"]);null!=p&&tt(i,p,d,n)}}}function it(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;var a=i.getModel().getComponent(t,e);if(!a)return r;var s=i.getViewOfComponentModel(a);if(!s||!s.findHighDownDispatchers)return r;for(var u,l=s.findHighDownDispatchers(n),h=0;h<l.length;h++)if("self"===(0,o.A)(l[h]).focus){u=!0;break}return{focusSelf:u,dispatchers:l}}function rt(t,e,n){var i=(0,o.A)(t),a=it(i.componentMainType,i.componentIndex,i.componentHighDownName,n),s=a.dispatchers,u=a.focusSelf;s?(u&&et(i.componentMainType,i.componentIndex,n),(0,r.S6)(s,(function(t){return U(t,e)}))):(tt(i.seriesIndex,i.focus,i.blurScope,n),"self"===i.focus&&et(i.componentMainType,i.componentIndex,n),U(t,e))}function ot(t,e,n){$(n);var i=(0,o.A)(t),a=it(i.componentMainType,i.componentIndex,i.componentHighDownName,n).dispatchers;a?(0,r.S6)(a,(function(t){return G(t,e)})):G(t,e)}function at(t,e,n){if(xt(e)){var i=e.dataType,o=t.getData(i),a=(0,s.gO)(o,e);(0,r.kJ)(a)||(a=[a]),t[e.type===T?"toggleSelect":e.type===S?"select":"unselect"](a,i)}}function st(t){var e=t.getAllData();(0,r.S6)(e,(function(e){var n=e.data,i=e.type;n.eachItemGraphicEl((function(e,n){t.isSelected(n,i)?j(e):J(e)}))}))}function ut(t){var e=[];return t.eachSeries((function(t){var n=t.getAllData();(0,r.S6)(n,(function(n){n.data;var i=n.type,r=t.getSelectedDataIndices();if(r.length>0){var o={dataIndex:r,seriesIndex:t.seriesIndex};null!=i&&(o.dataType=i),e.push(o)}}))})),e}function lt(t,e,n){gt(t,!0),z(t,V),ft(t,e,n)}function ht(t){gt(t,!1)}function ct(t,e,n,i){i?ht(t):lt(t,e,n)}function ft(t,e,n){var i=(0,o.A)(t);null!=e?(i.focus=e,i.blurScope=n):i.focus&&(i.focus=null)}var pt=["emphasis","blur","select"],dt={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function vt(t,e,n,i){n=n||"itemStyle";for(var r=0;r<pt.length;r++){var o=pt[r],a=e.getModel([o,n]),s=t.ensureState(o);s.style=i?i(a):a[dt[n]]()}}function gt(t,e){var n=!1===e,i=t;t.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=t.highDownSilentOnTouch),n&&!i.__highDownDispatcher||(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!n)}function yt(t){return!(!t||!t.__highDownDispatcher)}function mt(t,e,n){var i=(0,o.A)(t);i.componentMainType=e.mainType,i.componentIndex=e.componentIndex,i.componentHighDownName=n}function _t(t){var e=h[t];return null==e&&l<=32&&(e=h[t]=l++),e}function xt(t){var e=t.type;return e===S||e===b||e===T}function wt(t){var e=t.type;return e===x||e===w}function St(t){var e=c(t);e.normalFill=t.style.fill,e.normalStroke=t.style.stroke;var n=t.states.select||{};e.selectFill=n.style&&n.style.fill||null,e.selectStroke=n.style&&n.style.stroke||null}},41525:function(t,e,n){n.d(e,{Cq:function(){return b},Pw:function(){return m},th:function(){return w},zp:function(){return S}});var i=n(33051),r=n(8846),o=n(22095),a=n(25293),s=n(69538),u=n(46496),l=n(60479),h=n(80423),c=n(85669),f=r.ZP.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),p=r.ZP.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),d=r.ZP.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),u=i-o+a+s,l=Math.asin(s/a),h=Math.cos(l)*a,c=Math.sin(l),f=Math.cos(l),p=.6*a,d=.7*a;t.moveTo(n-h,u+s),t.arc(n,u,a,Math.PI-l,2*Math.PI+l),t.bezierCurveTo(n+h-c*p,u+s+f*p,n,i-d,n,i),t.bezierCurveTo(n,i-d,n-h+c*p,u+s+f*p,n-h,u+s),t.closePath()}}),v=r.ZP.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),g={line:o.Z,rect:a.Z,roundRect:a.Z,square:a.Z,circle:s.Z,diamond:p,pin:d,arrow:v,triangle:f},y={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},m={};(0,i.S6)(g,(function(t,e){m[e]=new t}));var _=r.ZP.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=(0,h.wI)(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.position&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i=e.symbolType;if("none"!==i){var r=m[i];r||(i="rect",r=m[i]),y[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n)}}});function x(t,e){if("image"!==this.type){var n=this.style;this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw()}}function w(t,e,n,i,r,o,a){var s,h=0===t.indexOf("empty");return h&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),s=0===t.indexOf("image://")?u.makeImage(t.slice(8),new l.Z(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?u.makePath(t.slice(7),{},new l.Z(e,n,i,r),a?"center":"cover"):new _({shape:{symbolType:t,x:e,y:n,width:i,height:r}}),s.__isEmptyBrush=h,s.setColor=x,o&&s.setColor(o),s}function S(t){return(0,i.kJ)(t)||(t=[+t,+t]),[t[0]||0,t[1]||0]}function b(t,e){if(null!=t)return(0,i.kJ)(t)||(t=[t,t]),[(0,c.GM)(t[0],e[0])||0,(0,c.GM)((0,i.pD)(t[1],t[0]),e[1])||0]}},270:function(t,e,n){n.d(e,{P2:function(){return a},T9:function(){return s},ZH:function(){return u}});var i="\0__throttleOriginMethod",r="\0__throttleRate",o="\0__throttleType";function a(t,e,n){var i,r,o,a,s,u=0,l=0,h=null;function c(){l=(new Date).getTime(),h=null,t.apply(o,a||[])}e=e||0;var f=function(){for(var t=[],f=0;f<arguments.length;f++)t[f]=arguments[f];i=(new Date).getTime(),o=this,a=t;var p=s||e,d=s||n;s=null,r=i-(d?u:l)-p,clearTimeout(h),d?h=setTimeout(c,p):r>=0?c():h=setTimeout(c,-r),u=i};return f.clear=function(){h&&(clearTimeout(h),h=null)},f.debounceNextCall=function(t){s=t},f}function s(t,e,n,s){var u=t[e];if(u){var l=u[i]||u,h=u[o],c=u[r];if(c!==n||h!==s){if(null==n||!s)return t[e]=l;u=t[e]=a(l,n,"debounce"===s),u[i]=l,u[o]=s,u[r]=n}return u}}function u(t,e){var n=t[e];n&&n[i]&&(n.clear&&n.clear(),t[e]=n[i])}},15015:function(t,e,n){n.d(e,{$K:function(){return _},CW:function(){return k},En:function(){return R},FW:function(){return g},MV:function(){return P},P5:function(){return c},RZ:function(){return A},Tj:function(){return m},V8:function(){return d},WT:function(){return s},WU:function(){return w},Wp:function(){return C},cb:function(){return N},dV:function(){return l},eN:function(){return z},f5:function(){return L},fn:function(){return I},k7:function(){return S},q5:function(){return T},rM:function(){return B},s2:function(){return h},sx:function(){return M},vh:function(){return Z},vk:function(){return y},xC:function(){return x},xL:function(){return O},xz:function(){return D},yR:function(){return u}});var i=n(33051),r=n(85669),o=n(75212),a=n(12312),s=1e3,u=60*s,l=60*u,h=24*l,c=365*h,f={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},p="{yyyy}-{MM}-{dd}",d={year:"{yyyy}",month:"{yyyy}-{MM}",day:p,hour:p+" "+f.hour,minute:p+" "+f.minute,second:p+" "+f.second,millisecond:f.none},v=["year","month","day","hour","minute","second","millisecond"],g=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function y(t,e){return t+="","0000".substr(0,e-t.length)+t}function m(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function _(t){return t===m(t)}function x(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function w(t,e,n,i){var s=r.sG(t),u=s[M(n)](),l=s[k(n)]()+1,h=Math.floor((l-1)/3)+1,c=s[D(n)](),f=s["get"+(n?"UTC":"")+"Day"](),p=s[C(n)](),d=(p-1)%12+1,v=s[I(n)](),g=s[P(n)](),m=s[A(n)](),_=i instanceof a.Z?i:(0,o.G8)(i||o.sO)||(0,o.Li)(),x=_.getModel("time"),w=x.get("month"),S=x.get("monthAbbr"),b=x.get("dayOfWeek"),T=x.get("dayOfWeekAbbr");return(e||"").replace(/{yyyy}/g,u+"").replace(/{yy}/g,u%100+"").replace(/{Q}/g,h+"").replace(/{MMMM}/g,w[l-1]).replace(/{MMM}/g,S[l-1]).replace(/{MM}/g,y(l,2)).replace(/{M}/g,l+"").replace(/{dd}/g,y(c,2)).replace(/{d}/g,c+"").replace(/{eeee}/g,b[f]).replace(/{ee}/g,T[f]).replace(/{e}/g,f+"").replace(/{HH}/g,y(p,2)).replace(/{H}/g,p+"").replace(/{hh}/g,y(d+"",2)).replace(/{h}/g,d+"").replace(/{mm}/g,y(v,2)).replace(/{m}/g,v+"").replace(/{ss}/g,y(g,2)).replace(/{s}/g,g+"").replace(/{SSS}/g,y(m,3)).replace(/{S}/g,m+"")}function S(t,e,n,r,o){var a=null;if(i.HD(n))a=n;else if(i.mf(n))a=n(t.value,e,{level:t.level});else{var s=i.l7({},f);if(t.level>0)for(var u=0;u<v.length;++u)s[v[u]]="{primary|"+s[v[u]]+"}";var l=n?!1===n.inherit?n:i.ce(n,s):s,h=b(t.value,o);if(l[h])a=l[h];else if(l.inherit){var c=g.indexOf(h);for(u=c-1;u>=0;--u)if(l[h]){a=l[h];break}a=a||s.none}if(i.kJ(a)){var p=null==t.level?0:t.level>=0?t.level:a.length+t.level;p=Math.min(p,a.length-1),a=a[p]}}return w(new Date(t.value),a,o,r)}function b(t,e){var n=r.sG(t),i=n[k(e)]()+1,o=n[D(e)](),a=n[C(e)](),s=n[I(e)](),u=n[P(e)](),l=n[A(e)](),h=0===l,c=h&&0===u,f=c&&0===s,p=f&&0===a,d=p&&1===o,v=d&&1===i;return v?"year":d?"month":p?"day":f?"hour":c?"minute":h?"second":"millisecond"}function T(t,e,n){var o=i.hj(t)?r.sG(t):t;switch(e=e||b(t,n),e){case"year":return o[M(n)]();case"half-year":return o[k(n)]()>=6?1:0;case"quarter":return Math.floor((o[k(n)]()+1)/4);case"month":return o[k(n)]();case"day":return o[D(n)]();case"half-day":return o[C(n)]()/24;case"hour":return o[C(n)]();case"minute":return o[I(n)]();case"second":return o[P(n)]();case"millisecond":return o[A(n)]()}}function M(t){return t?"getUTCFullYear":"getFullYear"}function k(t){return t?"getUTCMonth":"getMonth"}function D(t){return t?"getUTCDate":"getDate"}function C(t){return t?"getUTCHours":"getHours"}function I(t){return t?"getUTCMinutes":"getMinutes"}function P(t){return t?"getUTCSeconds":"getSeconds"}function A(t){return t?"getUTCMilliseconds":"getMilliseconds"}function O(t){return t?"setUTCFullYear":"setFullYear"}function Z(t){return t?"setUTCMonth":"setMonth"}function L(t){return t?"setUTCDate":"setDate"}function R(t){return t?"setUTCHours":"setHours"}function z(t){return t?"setUTCMinutes":"setMinutes"}function B(t){return t?"setUTCSeconds":"setSeconds"}function N(t){return t?"setUTCMilliseconds":"setMilliseconds"}},94279:function(t,e,n){n.d(e,{J5:function(){return l},RA:function(){return h},Wc:function(){return f},XD:function(){return a},cy:function(){return o},f7:function(){return r},fY:function(){return c},hL:function(){return u},qb:function(){return s}});var i=n(33051),r=(0,i.kW)(["tooltip","label","itemName","itemId","itemGroupId","seriesName"]),o="original",a="arrayRows",s="objectRows",u="keyedColumns",l="typedArray",h="unknown",c="column",f="row"},80887:function(t,e,n){n.d(e,{o:function(){return a}});var i=n(33051),r="undefined"!==typeof Float32Array,o=r?Float32Array:Array;function a(t){return(0,i.kJ)(t)?r?new Float32Array(t):t:new o(t)}},75797:function(t,e,n){var i=n(33051),r=n(38154),o=n(42151),a=n(34251),s=n(32234),u=n(26357),l=n(8674),h=n(95682),c=n(46496),f=s.Yf(),p=(0,h.Z)(),d=function(){function t(){this.group=new r.Z,this.uid=o.Kr("viewChart"),this.renderTask=(0,l.v)({plan:y,reset:m}),this.renderTask.context={view:this}}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){0},t.prototype.highlight=function(t,e,n,i){var r=t.getData(i&&i.dataType);r&&g(r,i,"emphasis")},t.prototype.downplay=function(t,e,n,i){var r=t.getData(i&&i.dataType);r&&g(r,i,"normal")},t.prototype.remove=function(t,e){this.group.removeAll()},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.eachRendered=function(t){(0,c.traverseElements)(this.group,t)},t.markUpdateMethod=function(t,e){f(t).updateMethod=e},t.protoInitialize=function(){var e=t.prototype;e.type="chart"}(),t}();function v(t,e,n){t&&(0,u.Av)(t)&&("emphasis"===e?u.fD:u.Mh)(t,n)}function g(t,e,n){var r=s.gO(t,e),o=e&&null!=e.highlightKey?(0,u.RW)(e.highlightKey):null;null!=r?(0,i.S6)(s.kF(r),(function(e){v(t.getItemGraphicEl(e),n,o)})):t.eachItemGraphicEl((function(t){v(t,n,o)}))}function y(t){return p(t.model)}function m(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=r&&f(r).updateMethod,u=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==u&&a[u](e,n,i,r),_[u]}a.dm(d,["dispose"]),a.au(d);var _={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};e["Z"]=d},33166:function(t,e,n){var i=n(38154),r=n(42151),o=n(34251),a=function(){function t(){this.group=new i.Z,this.uid=r.Kr("viewComponent")}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){},t.prototype.updateLayout=function(t,e,n,i){},t.prototype.updateVisual=function(t,e,n,i){},t.prototype.toggleBlurSeries=function(t,e,n){},t.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},t}();o.dm(a),o.au(a),e["Z"]=a},26211:function(t,e,n){function i(t,e,n){switch(n){case"color":var i=t.getItemVisual(e,"style");return i[t.getVisual("drawType")];case"opacity":return t.getItemVisual(e,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getItemVisual(e,n);default:0}}function r(t,e){switch(e){case"color":var n=t.getVisual("style");return n[t.getVisual("drawType")];case"opacity":return t.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getVisual(e);default:0}}function o(t,e,n,i){switch(n){case"color":var r=t.ensureUniqueItemVisual(e,"style");r[t.getVisual("drawType")]=i,t.setItemVisual(e,"colorFromPalette",!1);break;case"opacity":t.ensureUniqueItemVisual(e,"style").opacity=i;break;case"symbol":case"symbolSize":case"liftZ":t.setItemVisual(e,n,i);break;default:0}}n.d(e,{LZ:function(){return o},Or:function(){return i},UL:function(){return r}})},70655:function(t,e,n){n.d(e,{ZT:function(){return r}});
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},i(t,e)};function r(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}Object.create;Object.create},85823:function(t,e,n){var i=n(87411),r=n(51376),o=n(60479),a=n(23510),s=n(80423),u=n(33051),l=n(4990),h=n(21092),c=n(14414),f="__zr_normal__",p=i.dN.concat(["ignore"]),d=(0,u.u4)(i.dN,(function(t,e){return t[e]=!0,t}),{ignore:!1}),v={},g=new o.Z(0,0,0,0),y=function(){function t(t){this.id=(0,u.M8)(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var n=this.textConfig,i=n.local,r=e.innerTransformable,o=void 0,a=void 0,u=!1;r.parent=i?this:null;var l=!1;if(r.copyTransform(e),null!=n.position){var h=g;n.layoutRect?h.copy(n.layoutRect):h.copy(this.getBoundingRect()),i||h.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(v,n,h):(0,s.wI)(v,n,h),r.x=v.x,r.y=v.y,o=v.align,a=v.verticalAlign;var f=n.origin;if(f&&null!=n.rotation){var p=void 0,d=void 0;"center"===f?(p=.5*h.width,d=.5*h.height):(p=(0,s.GM)(f[0],h.width),d=(0,s.GM)(f[1],h.height)),l=!0,r.originX=-r.x+p+(i?0:h.x),r.originY=-r.y+d+(i?0:h.y)}}null!=n.rotation&&(r.rotation=n.rotation);var y=n.offset;y&&(r.x+=y[0],r.y+=y[1],l||(r.originX=-y[0],r.originY=-y[1]));var m=null==n.inside?"string"===typeof n.position&&n.position.indexOf("inside")>=0:n.inside,_=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),x=void 0,w=void 0,S=void 0;m&&this.canBeInsideText()?(x=n.insideFill,w=n.insideStroke,null!=x&&"auto"!==x||(x=this.getInsideTextFill()),null!=w&&"auto"!==w||(w=this.getInsideTextStroke(x),S=!0)):(x=n.outsideFill,w=n.outsideStroke,null!=x&&"auto"!==x||(x=this.getOutsideFill()),null!=w&&"auto"!==w||(w=this.getOutsideStroke(x),S=!0)),x=x||"#000",x===_.fill&&w===_.stroke&&S===_.autoStroke&&o===_.align&&a===_.verticalAlign||(u=!0,_.fill=x,_.stroke=w,_.autoStroke=S,_.align=o,_.verticalAlign=a,e.setDefaultTextStyle(_)),e.__dirty|=c.YV,u&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?l.GD:l.vU},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"===typeof e&&(0,h.Qc)(e);n||(n=[255,255,255,1]);for(var i=n[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,(0,h.Pz)(n,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},(0,u.l7)(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"===typeof t)this.attrKV(t,e);else if((0,u.Kn)(t))for(var n=t,i=(0,u.XP)(n),r=0;r<i.length;r++){var o=i[r];this.attrKV(o,t[o])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;if(!(i.getLoop()||r&&r!==f)){var o=i.targetName,a=o?e[o]:e;i.saveTo(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,p)},t.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(f,!1,t)},t.prototype.useState=function(t,e,n,i){var r=t===f,o=this.hasState();if(o||!r){var a=this.currentStates,s=this.stateTransition;if(!((0,u.cq)(a,t)>=0)||!e&&1!==a.length){var l;if(this.stateProxy&&!r&&(l=this.stateProxy(t)),l||(l=this.states&&this.states[t]),l||r){r||this.saveCurrentToNormalState(l);var h=!!(l&&l.hoverLayer||i);h&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,l,this._normalState,e,!n&&!this.__inHover&&s&&s.duration>0,s);var p=this._textContent,d=this._textGuide;return p&&p.useState(t,e,n,h),d&&d.useState(t,e,n,h),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~c.YV),l}(0,u.H)("State "+t+" not exists.")}}},t.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var u=t[s],l=void 0;this.stateProxy&&(l=this.stateProxy(u,t)),l||(l=this.states[u]),l&&i.push(l)}var h=i[o-1],f=!!(h&&h.hoverLayer||n);f&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(i),d=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var v=this._textContent,g=this._textGuide;v&&v.useStates(t,e,f),g&&g.useStates(t,e,f),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~c.YV)}else this.clearStates()},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=(0,u.cq)(this.currentStates,t);if(e>=0){var n=this.currentStates.slice();n.splice(e,1),this.useStates(n)}},t.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),r=(0,u.cq)(i,t),o=(0,u.cq)(i,e)>=0;r>=0?o?i.splice(r,1):i[r]=e:n&&!o&&i.push(e),this.useStates(i)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];(0,u.l7)(n,r),r.textConfig&&(e=e||{},(0,u.l7)(e,r.textConfig))}return e&&(n.textConfig=e),n},t.prototype._applyStateObj=function(t,e,n,i,r,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=(0,u.l7)({},i?this.textConfig:n.textConfig),(0,u.l7)(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig);for(var s={},l=!1,h=0;h<p.length;h++){var c=p[h],f=r&&d[c];e&&null!=e[c]?f?(l=!0,s[c]=e[c]):this[c]=e[c]:a&&null!=n[c]&&(f?(l=!0,s[c]=n[c]):this[c]=n[c])}if(!r)for(h=0;h<this.animators.length;h++){var v=this.animators[h],g=v.targetName;v.getLoop()||v.__changeFinalValue(g?(e||n)[g]:e||n)}l&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if((!t.__zr||t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new i.ZP,this._attachComponent(t),this._textContent=t,this.markRedraw())},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),(0,u.l7)(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=c.YV;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,n){var i=t?this[t]:this;var o=new r.Z(i,e,n);return t&&(o.targetName=t),this.addAnimator(o,t),o},t.prototype.addAnimator=function(t,e){var n=this.__zr,i=this;t.during((function(){i.updateDuringAnimation(e)})).done((function(){var e=i.animators,n=(0,u.cq)(e,t);n>=0&&e.splice(n,1)})),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},t.prototype.animateTo=function(t,e,n){m(this,t,e,n)},t.prototype.animateFrom=function(t,e,n){m(this,t,e,n,!0)},t.prototype._transitionState=function(t,e,n,i){for(var r=m(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;e.type="element",e.name="",e.ignore=e.silent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=c.YV;function n(t,n,i,r){function o(t,e){Object.defineProperty(e,0,{get:function(){return t[i]},set:function(e){t[i]=e}}),Object.defineProperty(e,1,{get:function(){return t[r]},set:function(e){t[r]=e}})}Object.defineProperty(e,t,{get:function(){if(!this[n]){var t=this[n]=[];o(this,t)}return this[n]},set:function(t){this[i]=t[0],this[r]=t[1],this[n]=t,o(this,t)}})}Object.defineProperty&&(n("position","_legacyPos","x","y"),n("scale","_legacyScale","scaleX","scaleY"),n("origin","_legacyOrigin","originX","originY"))}(),t}();function m(t,e,n,i,r){n=n||{};var o=[];T(t,"",t,e,n,i,o,r);var a=o.length,s=!1,u=n.done,l=n.aborted,h=function(){s=!0,a--,a<=0&&(s?u&&u():l&&l())},c=function(){a--,a<=0&&(s?u&&u():l&&l())};a||u&&u(),o.length>0&&n.during&&o[0].during((function(t,e){n.during(e)}));for(var f=0;f<o.length;f++){var p=o[f];h&&p.done(h),c&&p.aborted(c),n.force&&p.duration(n.duration),p.start(n.easing)}return o}function _(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function x(t){return(0,u.zG)(t[0])}function w(t,e,n){if((0,u.zG)(e[n]))if((0,u.zG)(t[n])||(t[n]=[]),(0,u.fU)(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),_(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(x(r))for(var s=r[0].length,l=0;l<a;l++)o[l]?_(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else _(o,r,a);o.length=r.length}else t[n]=e[n]}function S(t,e){return t===e||(0,u.zG)(t)&&(0,u.zG)(e)&&b(t,e)}function b(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}function T(t,e,n,i,o,a,s,l){for(var h=(0,u.XP)(i),c=o.duration,f=o.delay,p=o.additive,d=o.setToFinal,v=!(0,u.Kn)(a),g=t.animators,y=[],m=0;m<h.length;m++){var _=h[m],x=i[_];if(null!=x&&null!=n[_]&&(v||a[_]))if(!(0,u.Kn)(x)||(0,u.zG)(x)||(0,u.Qq)(x))y.push(_);else{if(e){l||(n[_]=x,t.updateDuringAnimation(e));continue}T(t,_,n[_],x,o,a&&a[_],s,l)}else l||(n[_]=x,t.updateDuringAnimation(e),y.push(_))}var b=y.length;if(!p&&b)for(var M=0;M<g.length;M++){var k=g[M];if(k.targetName===e){var D=k.stopTracks(y);if(D){var C=(0,u.cq)(g,k);g.splice(C,1)}}}if(o.force||(y=(0,u.hX)(y,(function(t){return!S(i[t],n[t])})),b=y.length),b>0||o.force&&!s.length){var I=void 0,P=void 0,A=void 0;if(l){P={},d&&(I={});for(M=0;M<b;M++){_=y[M];P[_]=n[_],d?I[_]=i[_]:n[_]=i[_]}}else if(d){A={};for(M=0;M<b;M++){_=y[M];A[_]=(0,r.V)(n[_]),w(n,i,_)}}k=new r.Z(n,!1,!1,p?(0,u.hX)(g,(function(t){return t.targetName===e})):null);k.targetName=e,o.scope&&(k.scope=o.scope),d&&I&&k.whenWithKeys(0,I,y),A&&k.whenWithKeys(0,A,y),k.whenWithKeys(null==c?500:c,l?P:i,y).delay(f||0),t.addAnimator(k,e),s.push(k)}}(0,u.jB)(y,a.Z),(0,u.jB)(y,i.ZP),e["Z"]=y},51376:function(t,e,n){n.d(e,{V:function(){return _},Z:function(){return L}});var i={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i))},elasticOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/i)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-i.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*i.bounceIn(2*t):.5*i.bounceOut(2*t-1)+.5}},r=i,o=n(33051),a=n(75188),s=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||o.ZT,this.ondestroy=t.ondestroy||o.ZT,this.onrestart=t.onrestart||o.ZT,t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n;r<0&&(r=0),r=Math.min(r,1);var o=this.easingFunc,a=o?o(r):r;if(this.onframe(a),1===r){if(!this.loop)return!0;var s=i%n;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=(0,o.mf)(t)?t:r[t]||(0,a.H)(t)},t}(),u=s,l=n(21092),h=n(24839),c=Array.prototype.slice;function f(t,e,n){return(e-t)*n+t}function p(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=f(e[o],n[o],i);return t}function d(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=f(e[a][s],n[a][s],i)}return t}function v(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function g(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function y(t,e){for(var n=t.length,i=e.length,r=n>i?e:t,o=Math.min(n,i),a=r[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(n,i);s++)r.push({offset:a.offset,color:a.color.slice()})}function m(t,e,n){var i=t,r=e;if(i.push&&r.push){var o=i.length,a=r.length;if(o!==a){var s=o>a;if(s)i.length=a;else for(var u=o;u<a;u++)i.push(1===n?r[u]:c.call(r[u]))}var l=i[0]&&i[0].length;for(u=0;u<i.length;u++)if(1===n)isNaN(i[u])&&(i[u]=r[u]);else for(var h=0;h<l;h++)isNaN(i[u][h])&&(i[u][h]=r[u][h])}}function _(t){if((0,o.zG)(t)){var e=t.length;if((0,o.zG)(t[0])){for(var n=[],i=0;i<e;i++)n.push(c.call(t[i]));return n}return c.call(t)}return t}function x(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function w(t){return(0,o.zG)(t&&t[0])?2:1}var S=0,b=1,T=2,M=3,k=4,D=5,C=6;function I(t){return t===k||t===D}function P(t){return t===b||t===T}var A=[0,0,0,0],O=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i=this.keyframes,s=i.length,u=!1,c=C,f=e;if((0,o.zG)(e)){var p=w(e);c=p,(1===p&&!(0,o.hj)(e[0])||2===p&&!(0,o.hj)(e[0][0]))&&(u=!0)}else if((0,o.hj)(e)&&!(0,o.Bu)(e))c=S;else if((0,o.HD)(e))if(isNaN(+e)){var d=l.Qc(e);d&&(f=d,c=M)}else c=S;else if((0,o.Qq)(e)){var v=(0,o.l7)({},f);v.colorStops=(0,o.UI)(e.colorStops,(function(t){return{offset:t.offset,color:l.Qc(t.color)}})),(0,h.I1)(e)?c=k:(0,h.gO)(e)&&(c=D),f=v}0===s?this.valType=c:c===this.valType&&c!==C||(u=!0),this.discrete=this.discrete||u;var g={time:t,value:f,rawValue:e,percent:0};return n&&(g.easing=n,g.easingFunc=(0,o.mf)(n)?n:r[n]||(0,a.H)(n)),i.push(g),g},t.prototype.prepare=function(t,e){var n=this.keyframes;this._needsSort&&n.sort((function(t,e){return t.time-e.time}));for(var i=this.valType,r=n.length,o=n[r-1],a=this.discrete,s=P(i),u=I(i),l=0;l<r;l++){var h=n[l],c=h.value,f=o.value;h.percent=h.time/t,a||(s&&l!==r-1?m(c,f,i):u&&y(c.colorStops,f.colorStops))}if(!a&&i!==D&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;var p=n[0].value;for(l=0;l<r;l++)i===S?n[l].additiveValue=n[l].value-p:i===M?n[l].additiveValue=v([],n[l].value,p,-1):P(i)&&(n[l].additiveValue=i===b?v([],n[l].value,p,-1):g([],n[l].value,p,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,a=null!=this._additiveTrack,s=a?"additiveValue":"value",u=this.valType,l=this.keyframes,h=l.length,c=this.propName,v=u===M,g=this._lastFr,y=Math.min;if(1===h)i=r=l[0];else{if(e<0)n=0;else if(e<this._lastFrP){var m=y(g+1,h-1);for(n=m;n>=0;n--)if(l[n].percent<=e)break;n=y(n,h-2)}else{for(n=g;n<h;n++)if(l[n].percent>e)break;n=y(n-1,h-2)}r=l[n+1],i=l[n]}if(i&&r){this._lastFr=n,this._lastFrP=e;var _=r.percent-i.percent,w=0===_?1:y((e-i.percent)/_,1);r.easingFunc&&(w=r.easingFunc(w));var S=a?this._additiveValue:v?A:t[c];if(!P(u)&&!v||S||(S=this._additiveValue=[]),this.discrete)t[c]=w<1?i.rawValue:r.rawValue;else if(P(u))u===b?p(S,i[s],r[s],w):d(S,i[s],r[s],w);else if(I(u)){var T=i[s],D=r[s],C=u===k;t[c]={type:C?"linear":"radial",x:f(T.x,D.x,w),y:f(T.y,D.y,w),colorStops:(0,o.UI)(T.colorStops,(function(t,e){var n=D.colorStops[e];return{offset:f(t.offset,n.offset,w),color:x(p([],t.color,n.color,w))}})),global:D.global},C?(t[c].x2=f(T.x2,D.x2,w),t[c].y2=f(T.y2,D.y2,w)):t[c].r=f(T.r,D.r,w)}else if(v)p(S,i[s],r[s],w),a||(t[c]=x(S));else{var O=f(i[s],r[s],w);a?this._additiveValue=O:t[c]=O}a&&this._addToTarget(t)}}},t.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;e===S?t[n]=t[n]+i:e===M?(l.Qc(t[n],A),v(A,A,i,1),t[n]=x(A)):e===b?v(t[n],t[n],i,1):e===T&&g(t[n],t[n],i,1)},t}(),Z=function(){function t(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&i?(0,o.H)("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,(0,o.XP)(e),n)},t.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o],s=r[a];if(!s){s=r[a]=new O(a);var u=void 0,l=this._getAdditiveTrack(a);if(l){var h=l.keyframes,c=h[h.length-1];u=c&&c.value,l.valType===M&&u&&(u=x(u))}else u=this._target[a];if(null==u)continue;t>0&&s.addKeyframe(0,_(u),i),this._trackKeys.push(a)}s.addKeyframe(t,_(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,n=[],i=this._maxTime||0,r=0;r<this._trackKeys.length;r++){var o=this._trackKeys[r],a=this._tracks[o],s=this._getAdditiveTrack(o),l=a.keyframes,h=l.length;if(a.prepare(i,s),a.needsAnimate())if(!this._allowDiscrete&&a.discrete){var c=l[h-1];c&&(e._target[a.propName]=c.rawValue),a.setFinished()}else n.push(a)}if(n.length||this._force){var f=new u({life:i,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var i=e._additiveAnimators;if(i){for(var r=!1,o=0;o<i.length;o++)if(i[o]._clip){r=!0;break}r||(e._additiveAnimators=null)}for(o=0;o<n.length;o++)n[o].step(e._target,t);var a=e._onframeCbs;if(a)for(o=0;o<a.length;o++)a[o](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=f,this.animation&&this.animation.addClip(f),t&&f.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return(0,o.UI)(this._trackKeys,(function(e){return t._tracks[e]}))},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}var a=!0;for(r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];if(o&&!o.isFinished()){var a=o.keyframes,s=a[n?0:a.length-1];s&&(t[r]=_(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||(0,o.XP)(t);for(var n=0;n<e.length;n++){var i=e[n],r=this._tracks[i];if(r){var a=r.keyframes;if(a.length>1){var s=a.pop();r.addKeyframe(s.time,t[i]),r.prepare(this._maxTime,r.getAdditiveTrack())}}}},t}(),L=Z},75188:function(t,e,n){n.d(e,{H:function(){return a}});var i=n(29023),r=n(33051),o=/cubic-bezier\(([0-9,\.e ]+)\)/;function a(t){var e=t&&o.exec(t);if(e){var n=e[1].split(","),a=+(0,r.fy)(n[0]),s=+(0,r.fy)(n[1]),u=+(0,r.fy)(n[2]),l=+(0,r.fy)(n[3]);if(isNaN(a+s+u+l))return;var h=[];return function(t){return t<=0?0:t>=1?1:(0,i.kD)(0,a,u,1,t,h)&&(0,i.af)(0,s,l,1,h[0])}}}},22795:function(t,e,n){var i,r=n(66387);i=r.Z.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},e["Z"]=i},50810:function(t,e,n){n.d(e,{a:function(){return o}});var i=n(33051);function r(t,e){return t&&"solid"!==t&&e>0?"dashed"===t?[4*e,2*e]:"dotted"===t?[e]:(0,i.hj)(t)?[t]:(0,i.kJ)(t)?t:null:null}function o(t){var e=t.style,n=e.lineDash&&e.lineWidth>0&&r(e.lineDash,e.lineWidth),o=e.lineDashOffset;if(n){var a=e.strokeNoScale&&t.getLineScale?t.getLineScale():1;a&&1!==a&&(n=(0,i.UI)(n,(function(t){return t/a})),o/=a)}return[n,o]}},97772:function(t,e,n){n.d(e,{Dm:function(){return F},RV:function(){return E},RZ:function(){return x}});var i=n(7719),r=n(14014),o=n(8007),a=n(5787),s=n(8846),u=n(44535),l=n(71505),h=n(33051),c=n(50810),f=n(14414),p=n(23132),d=new r.Z(!0);function v(t){var e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))}function g(t){return"string"===typeof t&&"none"!==t}function y(t){var e=t.fill;return null!=e&&"none"!==e}function m(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var n=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n}else t.fill()}function _(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var n=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n}else t.stroke()}function x(t,e,n){var i=(0,o.Gq)(e.image,e.__image,n);if((0,o.v5)(i)){var r=t.createPattern(i,e.repeat||"repeat");if("function"===typeof DOMMatrix&&r&&r.setTransform){var a=new DOMMatrix;a.translateSelf(e.x||0,e.y||0),a.rotateSelf(0,0,(e.rotation||0)*h.I3),a.scaleSelf(e.scaleX||1,e.scaleY||1),r.setTransform(a)}return r}}function w(t,e,n,i){var r,o=v(n),s=y(n),u=n.strokePercent,l=u<1,h=!e.path;e.silent&&!l||!h||e.createPathProxy();var p=e.path||d,g=e.__dirty;if(!i){var w=n.fill,S=n.stroke,b=s&&!!w.colorStops,T=o&&!!S.colorStops,M=s&&!!w.image,k=o&&!!S.image,D=void 0,C=void 0,I=void 0,P=void 0,A=void 0;(b||T)&&(A=e.getBoundingRect()),b&&(D=g?(0,a.ZF)(t,w,A):e.__canvasFillGradient,e.__canvasFillGradient=D),T&&(C=g?(0,a.ZF)(t,S,A):e.__canvasStrokeGradient,e.__canvasStrokeGradient=C),M&&(I=g||!e.__canvasFillPattern?x(t,w,e):e.__canvasFillPattern,e.__canvasFillPattern=I),k&&(P=g||!e.__canvasStrokePattern?x(t,S,e):e.__canvasStrokePattern,e.__canvasStrokePattern=I),b?t.fillStyle=D:M&&(I?t.fillStyle=I:s=!1),T?t.strokeStyle=C:k&&(P?t.strokeStyle=P:o=!1)}var O,Z,L=e.getGlobalScale();p.setScale(L[0],L[1],e.segmentIgnoreThreshold),t.setLineDash&&n.lineDash&&(r=(0,c.a)(e),O=r[0],Z=r[1]);var R=!0;(h||g&f.RH)&&(p.setDPR(t.dpr),l?p.setContext(null):(p.setContext(t),R=!1),p.reset(),e.buildPath(p,e.shape,i),p.toStatic(),e.pathUpdated()),R&&p.rebuildPath(t,l?u:1),O&&(t.setLineDash(O),t.lineDashOffset=Z),i||(n.strokeFirst?(o&&_(t,n),s&&m(t,n)):(s&&m(t,n),o&&_(t,n))),O&&t.setLineDash([])}function S(t,e,n){var i=e.__image=(0,o.Gq)(n.image,e.__image,e,e.onload);if(i&&(0,o.v5)(i)){var r=n.x||0,a=n.y||0,s=e.getWidth(),u=e.getHeight(),l=i.width/i.height;if(null==s&&null!=u?s=u*l:null==u&&null!=s?u=s/l:null==s&&null==u&&(s=i.width,u=i.height),n.sWidth&&n.sHeight){var h=n.sx||0,c=n.sy||0;t.drawImage(i,h,c,n.sWidth,n.sHeight,r,a,s,u)}else if(n.sx&&n.sy){h=n.sx,c=n.sy;var f=s-h,p=u-c;t.drawImage(i,h,c,f,p,r,a,s,u)}else t.drawImage(i,r,a,s,u)}}function b(t,e,n){var i,r=n.text;if(null!=r&&(r+=""),r){t.font=n.font||p.Uo,t.textAlign=n.textAlign,t.textBaseline=n.textBaseline;var o=void 0,a=void 0;t.setLineDash&&n.lineDash&&(i=(0,c.a)(e),o=i[0],a=i[1]),o&&(t.setLineDash(o),t.lineDashOffset=a),n.strokeFirst?(v(n)&&t.strokeText(r,n.x,n.y),y(n)&&t.fillText(r,n.x,n.y)):(y(n)&&t.fillText(r,n.x,n.y),v(n)&&t.strokeText(r,n.x,n.y)),o&&t.setLineDash([])}}var T=["shadowBlur","shadowOffsetX","shadowOffsetY"],M=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function k(t,e,n,r,o){var a=!1;if(!r&&(n=n||{},e===n))return!1;if(r||e.opacity!==n.opacity){B(t,o),a=!0;var s=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(s)?i.tj.opacity:s}(r||e.blend!==n.blend)&&(a||(B(t,o),a=!0),t.globalCompositeOperation=e.blend||i.tj.blend);for(var u=0;u<T.length;u++){var l=T[u];(r||e[l]!==n[l])&&(a||(B(t,o),a=!0),t[l]=t.dpr*(e[l]||0))}return(r||e.shadowColor!==n.shadowColor)&&(a||(B(t,o),a=!0),t.shadowColor=e.shadowColor||i.tj.shadowColor),a}function D(t,e,n,i,r){var o=N(e,r.inHover),a=i?null:n&&N(n,r.inHover)||{};if(o===a)return!1;var s=k(t,o,a,i,r);if((i||o.fill!==a.fill)&&(s||(B(t,r),s=!0),g(o.fill)&&(t.fillStyle=o.fill)),(i||o.stroke!==a.stroke)&&(s||(B(t,r),s=!0),g(o.stroke)&&(t.strokeStyle=o.stroke)),(i||o.opacity!==a.opacity)&&(s||(B(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var u=o.lineWidth,l=u/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==l&&(s||(B(t,r),s=!0),t.lineWidth=l)}for(var h=0;h<M.length;h++){var c=M[h],f=c[0];(i||o[f]!==a[f])&&(s||(B(t,r),s=!0),t[f]=o[f]||c[1])}return s}function C(t,e,n,i,r){return k(t,N(e,r.inHover),n&&N(n,r.inHover),i,r)}function I(t,e){var n=e.transform,i=t.dpr||1;n?t.setTransform(i*n[0],i*n[1],i*n[2],i*n[3],i*n[4],i*n[5]):t.setTransform(i,0,0,i,0,0)}function P(t,e,n){for(var i=!1,r=0;r<t.length;r++){var o=t[r];i=i||o.isZeroArea(),I(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}n.allClipped=i}function A(t,e){return t&&e?t[0]!==e[0]||t[1]!==e[1]||t[2]!==e[2]||t[3]!==e[3]||t[4]!==e[4]||t[5]!==e[5]:!(!t&&!e)}var O=1,Z=2,L=3,R=4;function z(t){var e=y(t),n=v(t);return!(t.lineDash||!(+e^+n)||e&&"string"!==typeof t.fill||n&&"string"!==typeof t.stroke||t.strokePercent<1||t.strokeOpacity<1||t.fillOpacity<1)}function B(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function N(t,e){return e&&t.__hoverStyle||t.style}function E(t,e){F(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function F(t,e,n,i){var r=e.transform;if(!e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1))return e.__dirty&=~f.YV,void(e.__isRendered=!1);var o=e.__clipPaths,h=n.prevElClipPaths,c=!1,p=!1;if(h&&!(0,a.cF)(o,h)||(h&&h.length&&(B(t,n),t.restore(),p=c=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),o&&o.length&&(B(t,n),t.save(),P(o,t,n),c=!0),n.prevElClipPaths=o),n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var d=n.prevEl;d||(p=c=!0);var v=e instanceof s.ZP&&e.autoBatch&&z(e.style);c||A(r,d.transform)?(B(t,n),I(t,e)):v||B(t,n);var g=N(e,n.inHover);e instanceof s.ZP?(n.lastDrawType!==O&&(p=!0,n.lastDrawType=O),D(t,e,d,p,n),v&&(n.batchFill||n.batchStroke)||t.beginPath(),w(t,e,g,v),v&&(n.batchFill=g.fill||"",n.batchStroke=g.stroke||"")):e instanceof l.Z?(n.lastDrawType!==L&&(p=!0,n.lastDrawType=L),D(t,e,d,p,n),b(t,e,g)):e instanceof u.ZP?(n.lastDrawType!==Z&&(p=!0,n.lastDrawType=Z),C(t,e,d,p,n),S(t,e,g)):e.getTemporalDisplayables&&(n.lastDrawType!==R&&(p=!0,n.lastDrawType=R),H(t,e,n)),v&&i&&B(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),n.prevEl=e,e.__dirty=0,e.__isRendered=!0}}function H(t,e,n){var i=e.getDisplayables(),r=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:n.viewWidth,viewHeight:n.viewHeight,inHover:n.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){var u=i[o];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),F(t,u,s,o===a-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}for(var l=0,h=r.length;l<h;l++){u=r[l];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),F(t,u,s,l===h-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}},5787:function(t,e,n){function i(t){return isFinite(t)}function r(t,e,n){var r=null==e.x?0:e.x,o=null==e.x2?1:e.x2,a=null==e.y?0:e.y,s=null==e.y2?0:e.y2;e.global||(r=r*n.width+n.x,o=o*n.width+n.x,a=a*n.height+n.y,s=s*n.height+n.y),r=i(r)?r:0,o=i(o)?o:1,a=i(a)?a:0,s=i(s)?s:0;var u=t.createLinearGradient(r,a,o,s);return u}function o(t,e,n){var r=n.width,o=n.height,a=Math.min(r,o),s=null==e.x?.5:e.x,u=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(s=s*r+n.x,u=u*o+n.y,l*=a),s=i(s)?s:.5,u=i(u)?u:.5,l=l>=0&&i(l)?l:.5;var h=t.createRadialGradient(s,u,0,s,u,l);return h}function a(t,e,n){for(var i="radial"===e.type?o(t,e,n):r(t,e,n),a=e.colorStops,s=0;s<a.length;s++)i.addColorStop(a[s].offset,a[s].color);return i}function s(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}function u(t){return parseInt(t,10)}function l(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],a=["paddingRight","paddingBottom"][e];if(null!=n[i]&&"auto"!==n[i])return parseFloat(n[i]);var s=document.defaultView.getComputedStyle(t);return(t[r]||u(s[i])||u(t.style[i]))-(u(s[o])||0)-(u(s[a])||0)|0}n.d(e,{ZF:function(){return a},ap:function(){return l},cF:function(){return s}})},4990:function(t,e,n){n.d(e,{Ak:function(){return a},GD:function(){return u},KL:function(){return o},iv:function(){return l},vU:function(){return s}});var i=n(66387),r=1;i.Z.hasGlobalWindow&&(r=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var o=r,a=.4,s="#333",u="#ccc",l="#eee"},33640:function(t,e,n){function i(t,e,n,i,r,o,a){if(0===r)return!1;var s=r,u=0,l=t;if(a>e+s&&a>i+s||a<e-s&&a<i-s||o>t+s&&o>n+s||o<t-s&&o<n-s)return!1;if(t===n)return Math.abs(o-t)<=s/2;u=(e-i)/(t-n),l=(t*i-n*e)/(t-n);var h=u*o-a+l,c=h*h/(u*u+1);return c<=s/2*s/2}n.d(e,{m:function(){return i}})},25674:function(t,e,n){n.d(e,{m:function(){return r}});var i=n(29023);function r(t,e,n,r,o,a,s,u,l){if(0===s)return!1;var h=s;if(l>e+h&&l>r+h&&l>a+h||l<e-h&&l<r-h&&l<a-h||u>t+h&&u>n+h&&u>o+h||u<t-h&&u<n-h&&u<o-h)return!1;var c=(0,i.Wr)(t,e,n,r,o,a,u,l,null);return c<=h/2}},80423:function(t,e,n){n.d(e,{Dp:function(){return f},GM:function(){return p},M3:function(){return h},dz:function(){return s},lP:function(){return l},mU:function(){return c},wI:function(){return d}});var i=n(60479),r=n(92528),o=n(23132),a={};function s(t,e){e=e||o.Uo;var n=a[e];n||(n=a[e]=new r.ZP(500));var i=n.get(t);return null==i&&(i=o.qW.measureText(t,e).width,n.put(t,i)),i}function u(t,e,n,r){var o=s(t,e),a=f(e),u=h(0,o,n),l=c(0,a,r),p=new i.Z(u,l,o,a);return p}function l(t,e,n,r){var o=((t||"")+"").split("\n"),a=o.length;if(1===a)return u(o[0],e,n,r);for(var s=new i.Z(0,0,0,0),l=0;l<o.length;l++){var h=u(o[l],e,n,r);0===l?s.copy(h):s.union(h)}return s}function h(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function c(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function f(t){return s("国",t)}function p(t,e){return"string"===typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function d(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,u=n.x,l=n.y,h="left",c="top";if(i instanceof Array)u+=p(i[0],n.width),l+=p(i[1],n.height),h=null,c=null;else switch(i){case"left":u-=r,l+=s,h="right",c="middle";break;case"right":u+=r+a,l+=s,c="middle";break;case"top":u+=a/2,l-=r,h="center",c="bottom";break;case"bottom":u+=a/2,l+=o+r,h="center";break;case"inside":u+=a/2,l+=s,h="center",c="middle";break;case"insideLeft":u+=r,l+=s,c="middle";break;case"insideRight":u+=a-r,l+=s,h="right",c="middle";break;case"insideTop":u+=a/2,l+=r,h="center";break;case"insideBottom":u+=a/2,l+=o-r,h="center",c="bottom";break;case"insideTopLeft":u+=r,l+=r;break;case"insideTopRight":u+=a-r,l+=r,h="right";break;case"insideBottomLeft":u+=r,l+=o-r,c="bottom";break;case"insideBottomRight":u+=a-r,l+=o-r,h="right",c="bottom";break}return t=t||{},t.x=u,t.y=l,t.align=h,t.verticalAlign=c,t}},3266:function(t,e,n){n.d(e,{m:function(){return r}});var i=2*Math.PI;function r(t){return t%=i,t<0&&(t+=i),t}},47637:function(t,e,n){function i(t,e,n,i,r,o){if(o>e&&o>i||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!==a&&0!==a||(s=i<e?.5:-.5);var u=a*(n-t)+t;return u===r?1/0:u>r?s:0}n.d(e,{Z:function(){return i}})},60479:function(t,e,n){var i=n(32892),r=n(41610),o=Math.min,a=Math.max,s=new r.Z,u=new r.Z,l=new r.Z,h=new r.Z,c=new r.Z,f=new r.Z,p=function(){function t(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}return t.prototype.union=function(t){var e=o(t.x,this.x),n=o(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=a(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=a(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,n=t.width/e.width,r=t.height/e.height,o=i.Ue();return i.Iu(o,o,[-e.x,-e.y]),i.bA(o,o,[n,r]),i.Iu(o,o,[t.x,t.y]),o},t.prototype.intersect=function(e,n){if(!e)return!1;e instanceof t||(e=t.create(e));var i=this,o=i.x,a=i.x+i.width,s=i.y,u=i.y+i.height,l=e.x,h=e.x+e.width,p=e.y,d=e.y+e.height,v=!(a<l||h<o||u<p||d<s);if(n){var g=1/0,y=0,m=Math.abs(a-l),_=Math.abs(h-o),x=Math.abs(u-p),w=Math.abs(d-s),S=Math.min(m,_),b=Math.min(x,w);a<l||h<o?S>y&&(y=S,m<_?r.Z.set(f,-m,0):r.Z.set(f,_,0)):S<g&&(g=S,m<_?r.Z.set(c,m,0):r.Z.set(c,-_,0)),u<p||d<s?b>y&&(y=b,x<w?r.Z.set(f,0,-x):r.Z.set(f,0,w)):S<g&&(g=S,x<w?r.Z.set(c,0,x):r.Z.set(c,0,-w))}return n&&r.Z.copy(n,v?c:f),v},t.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,n,i){if(i){if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var r=i[0],c=i[3],f=i[4],p=i[5];return e.x=n.x*r+f,e.y=n.y*c+p,e.width=n.width*r,e.height=n.height*c,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}s.x=l.x=n.x,s.y=h.y=n.y,u.x=h.x=n.x+n.width,u.y=l.y=n.y+n.height,s.transform(i),h.transform(i),u.transform(i),l.transform(i),e.x=o(s.x,u.x,l.x,h.x),e.y=o(s.y,u.y,l.y,h.y);var d=a(s.x,u.x,l.x,h.x),v=a(s.y,u.y,l.y,h.y);e.width=d-e.x,e.height=v-e.y}else e!==n&&t.copy(e,n)},t}();e["Z"]=p},23510:function(t,e){var n=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"===typeof e&&(i=n,n=e,e=null),!n||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;var s={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},u=r[t].length-1,l=r[t][u];return l&&l.callAtLast?r[t].splice(u,0,s):r[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var n=this._$handlers;if(!n)return this;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},t.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var u=i[s];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(u.ctx);break;case 1:u.h.call(u.ctx,e[0]);break;case 2:u.h.call(u.ctx,e[0],e[1]);break;default:u.h.apply(u.ctx,e);break}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,u=0;u<s;u++){var l=i[u];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(a);break;case 1:l.h.call(a,e[0]);break;case 2:l.h.call(a,e[0],e[1]);break;default:l.h.apply(a,e.slice(1,o-1));break}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t}();e["Z"]=n},92528:function(t,e,n){var i=function(){function t(t){this.value=t}return t}(),r=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new i(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}(),o=function(){function t(t){this._list=new r,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var n=this._list,r=this._map,o=null;if(null==r[t]){var a=n.len(),s=this._lastRemovedEntry;if(a>=this._maxSize&&a>0){var u=n.head;n.remove(u),delete r[u.key],o=u.value,this._lastRemovedEntry=u}s?s.value=e:s=new i(e),s.key=t,n.insertEntry(s),r[t]=s}return o},t.prototype.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}();e["ZP"]=o},41587:function(t,e,n){var i=n(41610),r=[0,0],o=[0,0],a=new i.Z,s=new i.Z,u=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new i.Z;for(n=0;n<2;n++)this._axes[n]=new i.Z;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var n=this._corners,r=this._axes,o=t.x,a=t.y,s=o+t.width,u=a+t.height;if(n[0].set(o,a),n[1].set(s,a),n[2].set(s,u),n[3].set(o,u),e)for(var l=0;l<4;l++)n[l].transform(e);i.Z.sub(r[0],n[1],n[0]),i.Z.sub(r[1],n[3],n[0]),r[0].normalize(),r[1].normalize();for(l=0;l<2;l++)this._origin[l]=r[l].dot(n[0])},t.prototype.intersect=function(t,e){var n=!0,r=!e;return a.set(1/0,1/0),s.set(0,0),!this._intersectCheckOneSide(this,t,a,s,r,1)&&(n=!1,r)||!this._intersectCheckOneSide(t,this,a,s,r,-1)&&(n=!1,r)||r||i.Z.copy(e,n?a:s),n},t.prototype._intersectCheckOneSide=function(t,e,n,a,s,u){for(var l=!0,h=0;h<2;h++){var c=this._axes[h];if(this._getProjMinMaxOnAxis(h,t._corners,r),this._getProjMinMaxOnAxis(h,e._corners,o),r[1]<o[0]||r[0]>o[1]){if(l=!1,s)return l;var f=Math.abs(o[0]-r[1]),p=Math.abs(r[0]-o[1]);Math.min(f,p)>a.len()&&(f<p?i.Z.scale(a,c,-f*u):i.Z.scale(a,c,p*u))}else if(n){f=Math.abs(o[0]-r[1]),p=Math.abs(r[0]-o[1]);Math.min(f,p)<n.len()&&(f<p?i.Z.scale(n,c,f*u):i.Z.scale(n,c,-p*u))}}return l},t.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,u=1;u<e.length;u++){var l=e[u].dot(i)+r[t];a=Math.min(l,a),s=Math.max(l,s)}n[0]=a,n[1]=s},t}();e["Z"]=u},14014:function(t,e,n){n.d(e,{L:function(){return M}});var i=n(45280),r=n(60479),o=n(4990),a=n(3726),s=n(29023),u={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},l=[],h=[],c=[],f=[],p=[],d=[],v=Math.min,g=Math.max,y=Math.cos,m=Math.sin,_=Math.abs,x=Math.PI,w=2*x,S="undefined"!==typeof Float32Array,b=[];function T(t){var e=Math.round(t/x*1e8)/1e8;return e%2*x}function M(t,e){var n=T(t[0]);n<0&&(n+=w);var i=n-t[0],r=t[1];r+=i,!e&&r-n>=w?r=n+w:e&&n-r>=w?r=n-w:!e&&n>r?r=n+(w-T(n-r)):e&&n<r&&(r=n-(w-T(r-n))),t[0]=n,t[1]=r}var k=function(){function t(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return t.prototype.increaseVersion=function(){this._version++},t.prototype.getVersion=function(){return this._version},t.prototype.setScale=function(t,e,n){n=n||0,n>0&&(this._ux=_(n/o.KL/t)||0,this._uy=_(n/o.KL/e)||0)},t.prototype.setDPR=function(t){this.dpr=t},t.prototype.setContext=function(t){this._ctx=t},t.prototype.getContext=function(){return this._ctx},t.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},t.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},t.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(u.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},t.prototype.lineTo=function(t,e){var n=_(t-this._xi),i=_(e-this._yi),r=n>this._ux||i>this._uy;if(this.addData(u.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=n*n+i*i;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},t.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(u.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},t.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(u.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},t.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),b[0]=i,b[1]=r,M(b,o),i=b[0],r=b[1];var a=r-i;return this.addData(u.A,t,e,n,n,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=y(r)*n+t,this._yi=m(r)*n+e,this},t.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},t.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(u.R,t,e,n,i),this},t.prototype.closePath=function(){this._drawPendingPt(),this.addData(u.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},t.prototype.fill=function(t){t&&t.fill(),this.toStatic()},t.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},t.prototype.len=function(){return this._len},t.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!S||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},t.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();S&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},t.prototype.addData=function(t,e,n,i,r,o,a,s,u){if(this._saveData){var l=this.data;this._len+arguments.length>l.length&&(this._expandData(),l=this.data);for(var h=0;h<arguments.length;h++)l[this._len++]=arguments[h]}},t.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},t.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},t.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,S&&this._len>11&&(this.data=new Float32Array(t)))}},t.prototype.getBoundingRect=function(){c[0]=c[1]=p[0]=p[1]=Number.MAX_VALUE,f[0]=f[1]=d[0]=d[1]=-Number.MAX_VALUE;var t,e=this.data,n=0,o=0,s=0,l=0;for(t=0;t<this._len;){var h=e[t++],v=1===t;switch(v&&(n=e[t],o=e[t+1],s=n,l=o),h){case u.M:n=s=e[t++],o=l=e[t++],p[0]=s,p[1]=l,d[0]=s,d[1]=l;break;case u.L:(0,a.u4)(n,o,e[t],e[t+1],p,d),n=e[t++],o=e[t++];break;case u.C:(0,a.H9)(n,o,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],p,d),n=e[t++],o=e[t++];break;case u.Q:(0,a.mJ)(n,o,e[t++],e[t++],e[t],e[t+1],p,d),n=e[t++],o=e[t++];break;case u.A:var g=e[t++],_=e[t++],x=e[t++],w=e[t++],S=e[t++],b=e[t++]+S;t+=1;var T=!e[t++];v&&(s=y(S)*x+g,l=m(S)*w+_),(0,a.qL)(g,_,x,w,S,b,T,p,d),n=y(b)*x+g,o=m(b)*w+_;break;case u.R:s=n=e[t++],l=o=e[t++];var M=e[t++],k=e[t++];(0,a.u4)(s,l,s+M,l+k,p,d);break;case u.Z:n=s,o=l;break}i.VV(c,c,p),i.Fp(f,f,d)}return 0===t&&(c[0]=c[1]=f[0]=f[1]=0),new r.Z(c[0],c[1],f[0]-c[0],f[1]-c[1])},t.prototype._calculateLength=function(){var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,l=0;this._pathSegLen||(this._pathSegLen=[]);for(var h=this._pathSegLen,c=0,f=0,p=0;p<e;){var d=t[p++],x=1===p;x&&(r=t[p],o=t[p+1],a=r,l=o);var S=-1;switch(d){case u.M:r=a=t[p++],o=l=t[p++];break;case u.L:var b=t[p++],T=t[p++],M=b-r,k=T-o;(_(M)>n||_(k)>i||p===e-1)&&(S=Math.sqrt(M*M+k*k),r=b,o=T);break;case u.C:var D=t[p++],C=t[p++],I=(b=t[p++],T=t[p++],t[p++]),P=t[p++];S=(0,s.Ci)(r,o,D,C,b,T,I,P,10),r=I,o=P;break;case u.Q:D=t[p++],C=t[p++],b=t[p++],T=t[p++];S=(0,s.wQ)(r,o,D,C,b,T,10),r=b,o=T;break;case u.A:var A=t[p++],O=t[p++],Z=t[p++],L=t[p++],R=t[p++],z=t[p++],B=z+R;p+=1;t[p++];x&&(a=y(R)*Z+A,l=m(R)*L+O),S=g(Z,L)*v(w,Math.abs(z)),r=y(B)*Z+A,o=m(B)*L+O;break;case u.R:a=r=t[p++],l=o=t[p++];var N=t[p++],E=t[p++];S=2*N+2*E;break;case u.Z:M=a-r,k=l-o;S=Math.sqrt(M*M+k*k),r=a,o=l;break}S>=0&&(h[f++]=S,c+=S)}return this._pathLen=c,c},t.prototype.rebuildPath=function(t,e){var n,i,r,o,a,c,f,p,d,x,w,S=this.data,b=this._ux,T=this._uy,M=this._len,k=e<1,D=0,C=0,I=0;if(!k||(this._pathSegLen||this._calculateLength(),f=this._pathSegLen,p=this._pathLen,d=e*p,d))t:for(var P=0;P<M;){var A=S[P++],O=1===P;switch(O&&(r=S[P],o=S[P+1],n=r,i=o),A!==u.L&&I>0&&(t.lineTo(x,w),I=0),A){case u.M:n=r=S[P++],i=o=S[P++],t.moveTo(r,o);break;case u.L:a=S[P++],c=S[P++];var Z=_(a-r),L=_(c-o);if(Z>b||L>T){if(k){var R=f[C++];if(D+R>d){var z=(d-D)/R;t.lineTo(r*(1-z)+a*z,o*(1-z)+c*z);break t}D+=R}t.lineTo(a,c),r=a,o=c,I=0}else{var B=Z*Z+L*L;B>I&&(x=a,w=c,I=B)}break;case u.C:var N=S[P++],E=S[P++],F=S[P++],H=S[P++],W=S[P++],V=S[P++];if(k){R=f[C++];if(D+R>d){z=(d-D)/R;(0,s.Vz)(r,N,F,W,z,l),(0,s.Vz)(o,E,H,V,z,h),t.bezierCurveTo(l[1],h[1],l[2],h[2],l[3],h[3]);break t}D+=R}t.bezierCurveTo(N,E,F,H,W,V),r=W,o=V;break;case u.Q:N=S[P++],E=S[P++],F=S[P++],H=S[P++];if(k){R=f[C++];if(D+R>d){z=(d-D)/R;(0,s.Lx)(r,N,F,z,l),(0,s.Lx)(o,E,H,z,h),t.quadraticCurveTo(l[1],h[1],l[2],h[2]);break t}D+=R}t.quadraticCurveTo(N,E,F,H),r=F,o=H;break;case u.A:var U=S[P++],G=S[P++],X=S[P++],Y=S[P++],q=S[P++],K=S[P++],j=S[P++],J=!S[P++],Q=X>Y?X:Y,$=_(X-Y)>.001,tt=q+K,et=!1;if(k){R=f[C++];D+R>d&&(tt=q+K*(d-D)/R,et=!0),D+=R}if($&&t.ellipse?t.ellipse(U,G,X,Y,j,q,tt,J):t.arc(U,G,Q,q,tt,J),et)break t;O&&(n=y(q)*X+U,i=m(q)*Y+G),r=y(tt)*X+U,o=m(tt)*Y+G;break;case u.R:n=r=S[P],i=o=S[P+1],a=S[P++],c=S[P++];var nt=S[P++],it=S[P++];if(k){R=f[C++];if(D+R>d){var rt=d-D;t.moveTo(a,c),t.lineTo(a+v(rt,nt),c),rt-=nt,rt>0&&t.lineTo(a+nt,c+v(rt,it)),rt-=it,rt>0&&t.lineTo(a+g(nt-rt,0),c+it),rt-=nt,rt>0&&t.lineTo(a,c+g(it-rt,0));break t}D+=R}t.rect(a,c,nt,it);break;case u.Z:if(k){R=f[C++];if(D+R>d){z=(d-D)/R;t.lineTo(r*(1-z)+n*z,o*(1-z)+i*z);break t}D+=R}t.closePath(),r=n,o=i}}},t.prototype.clone=function(){var e=new t,n=this.data;return e.data=n.slice?n.slice():Array.prototype.slice.call(n),e._len=this._len,e},t.CMD=u,t.initDefaultProps=function(){var e=t.prototype;e._saveData=!0,e._ux=0,e._uy=0,e._pendingPtDist=0,e._version=0}(),t}();e["Z"]=k},41610:function(t,e){var n=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,n=this.y-t.y;return e*e+n*n},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,n=this.y;return this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,n){t.x=e,t.y=n},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},t.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},t.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},t.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},t.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},t}();e["Z"]=n},87411:function(t,e,n){n.d(e,{dN:function(){return p},kY:function(){return d}});var i=n(32892),r=n(45280),o=i.yR,a=5e-5;function s(t){return t>a||t<-a}var u=[],l=[],h=i.Ue(),c=Math.abs,f=function(){function t(){}return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return s(this.rotation)||s(this.x)||s(this.y)||s(this.scaleX-1)||s(this.scaleY-1)||s(this.skewX)||s(this.skewY)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||i.Ue(),e?this.getLocalTransform(n):o(n),t&&(e?i.dC(n,t,n):i.JG(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&o(n)},t.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(u);var n=u[0]<0?-1:1,r=u[1]<0?-1:1,o=((u[0]-n)*e+n)/u[0]||0,a=((u[1]-r)*e+r)/u[1]||0;t[0]*=o,t[1]*=o,t[2]*=a,t[3]*=a}this.invTransform=this.invTransform||i.Ue(),i.U_(this.invTransform,t)},t.prototype.getComputedTransform=function(){var t=this,e=[];while(t)e.push(t),t=t.parent;while(t=e.pop())t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),r=Math.PI/2+i-Math.atan2(t[3],t[2]);n=Math.sqrt(n)*Math.cos(r),e=Math.sqrt(e),this.skewX=r,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=n,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(i.dC(l,t.invTransform,e),e=l);var n=this.originX,r=this.originY;(n||r)&&(h[4]=n,h[5]=r,i.dC(l,e,h),l[4]-=n,l[5]-=r,e=l),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&r.Ne(n,n,i),n},t.prototype.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&r.Ne(n,n,i),n},t.prototype.getLineScale=function(){var t=this.transform;return t&&c(t[0]-1)>1e-10&&c(t[3]-1)>1e-10?Math.sqrt(c(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){d(this,t)},t.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,r=t.originY||0,o=t.scaleX,a=t.scaleY,s=t.anchorX,u=t.anchorY,l=t.rotation||0,h=t.x,c=t.y,f=t.skewX?Math.tan(t.skewX):0,p=t.skewY?Math.tan(-t.skewY):0;if(n||r||s||u){var d=n+s,v=r+u;e[4]=-d*o-f*v*a,e[5]=-v*a-p*d*o}else e[4]=e[5]=0;return e[0]=o,e[3]=a,e[1]=p*o,e[2]=f*a,l&&i.U1(e,e,l),e[4]+=n+h,e[5]+=r+c,e},t.initDefaultProps=function(){var e=t.prototype;e.scaleX=e.scaleY=e.globalScaleRatio=1,e.x=e.y=e.originX=e.originY=e.skewX=e.skewY=e.rotation=e.anchorX=e.anchorY=0}(),t}(),p=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function d(t,e){for(var n=0;n<p.length;n++){var i=p[n];t[i]=e[i]}}e["ZP"]=f},3726:function(t,e,n){n.d(e,{H9:function(){return y},mJ:function(){return m},qL:function(){return _},u4:function(){return d},zk:function(){return p}});var i=n(45280),r=n(29023),o=Math.min,a=Math.max,s=Math.sin,u=Math.cos,l=2*Math.PI,h=i.Ue(),c=i.Ue(),f=i.Ue();function p(t,e,n){if(0!==t.length){for(var i=t[0],r=i[0],s=i[0],u=i[1],l=i[1],h=1;h<t.length;h++)i=t[h],r=o(r,i[0]),s=a(s,i[0]),u=o(u,i[1]),l=a(l,i[1]);e[0]=r,e[1]=u,n[0]=s,n[1]=l}}function d(t,e,n,i,r,s){r[0]=o(t,n),r[1]=o(e,i),s[0]=a(t,n),s[1]=a(e,i)}var v=[],g=[];function y(t,e,n,i,s,u,l,h,c,f){var p=r.pP,d=r.af,y=p(t,n,s,l,v);c[0]=1/0,c[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var m=0;m<y;m++){var _=d(t,n,s,l,v[m]);c[0]=o(_,c[0]),f[0]=a(_,f[0])}y=p(e,i,u,h,g);for(m=0;m<y;m++){var x=d(e,i,u,h,g[m]);c[1]=o(x,c[1]),f[1]=a(x,f[1])}c[0]=o(t,c[0]),f[0]=a(t,f[0]),c[0]=o(l,c[0]),f[0]=a(l,f[0]),c[1]=o(e,c[1]),f[1]=a(e,f[1]),c[1]=o(h,c[1]),f[1]=a(h,f[1])}function m(t,e,n,i,s,u,l,h){var c=r.QC,f=r.Zm,p=a(o(c(t,n,s),1),0),d=a(o(c(e,i,u),1),0),v=f(t,n,s,p),g=f(e,i,u,d);l[0]=o(t,s,v),l[1]=o(e,u,g),h[0]=a(t,s,v),h[1]=a(e,u,g)}function _(t,e,n,r,o,a,p,d,v){var g=i.VV,y=i.Fp,m=Math.abs(o-a);if(m%l<1e-4&&m>1e-4)return d[0]=t-n,d[1]=e-r,v[0]=t+n,void(v[1]=e+r);if(h[0]=u(o)*n+t,h[1]=s(o)*r+e,c[0]=u(a)*n+t,c[1]=s(a)*r+e,g(d,h,c),y(v,h,c),o%=l,o<0&&(o+=l),a%=l,a<0&&(a+=l),o>a&&!p?a+=l:o<a&&p&&(o+=l),p){var _=a;a=o,o=_}for(var x=0;x<a;x+=Math.PI/2)x>o&&(f[0]=u(x)*n+t,f[1]=s(x)*r+e,g(d,f,d),y(v,f,v))}},29023:function(t,e,n){n.d(e,{AZ:function(){return b},Ci:function(){return w},Jz:function(){return T},Lx:function(){return k},QC:function(){return M},Vz:function(){return _},Wr:function(){return D},X_:function(){return g},Zm:function(){return S},af:function(){return v},kD:function(){return y},pP:function(){return m},t1:function(){return x},wQ:function(){return C}});var i=n(45280),r=Math.pow,o=Math.sqrt,a=1e-8,s=1e-4,u=o(3),l=1/3,h=(0,i.Ue)(),c=(0,i.Ue)(),f=(0,i.Ue)();function p(t){return t>-a&&t<a}function d(t){return t>a||t<-a}function v(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function g(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function y(t,e,n,i,a,s){var h=i+3*(e-n)-t,c=3*(n-2*e+t),f=3*(e-t),d=t-a,v=c*c-3*h*f,g=c*f-9*h*d,y=f*f-3*c*d,m=0;if(p(v)&&p(g))if(p(c))s[0]=0;else{var _=-f/c;_>=0&&_<=1&&(s[m++]=_)}else{var x=g*g-4*v*y;if(p(x)){var w=g/v,S=(_=-c/h+w,-w/2);_>=0&&_<=1&&(s[m++]=_),S>=0&&S<=1&&(s[m++]=S)}else if(x>0){var b=o(x),T=v*c+1.5*h*(-g+b),M=v*c+1.5*h*(-g-b);T=T<0?-r(-T,l):r(T,l),M=M<0?-r(-M,l):r(M,l);_=(-c-(T+M))/(3*h);_>=0&&_<=1&&(s[m++]=_)}else{var k=(2*v*c-3*h*g)/(2*o(v*v*v)),D=Math.acos(k)/3,C=o(v),I=Math.cos(D),P=(_=(-c-2*C*I)/(3*h),S=(-c+C*(I+u*Math.sin(D)))/(3*h),(-c+C*(I-u*Math.sin(D)))/(3*h));_>=0&&_<=1&&(s[m++]=_),S>=0&&S<=1&&(s[m++]=S),P>=0&&P<=1&&(s[m++]=P)}}return m}function m(t,e,n,i,r){var a=6*n-12*e+6*t,s=9*e+3*i-3*t-9*n,u=3*e-3*t,l=0;if(p(s)){if(d(a)){var h=-u/a;h>=0&&h<=1&&(r[l++]=h)}}else{var c=a*a-4*s*u;if(p(c))r[0]=-a/(2*s);else if(c>0){var f=o(c),v=(h=(-a+f)/(2*s),(-a-f)/(2*s));h>=0&&h<=1&&(r[l++]=h),v>=0&&v<=1&&(r[l++]=v)}}return l}function _(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,u=(i-n)*r+n,l=(s-a)*r+a,h=(u-s)*r+s,c=(h-l)*r+l;o[0]=t,o[1]=a,o[2]=l,o[3]=c,o[4]=c,o[5]=h,o[6]=u,o[7]=i}function x(t,e,n,r,a,u,l,p,d,g,y){var m,_,x,w,S,b=.005,T=1/0;h[0]=d,h[1]=g;for(var M=0;M<1;M+=.05)c[0]=v(t,n,a,l,M),c[1]=v(e,r,u,p,M),w=(0,i.WU)(h,c),w<T&&(m=M,T=w);T=1/0;for(var k=0;k<32;k++){if(b<s)break;_=m-b,x=m+b,c[0]=v(t,n,a,l,_),c[1]=v(e,r,u,p,_),w=(0,i.WU)(c,h),_>=0&&w<T?(m=_,T=w):(f[0]=v(t,n,a,l,x),f[1]=v(e,r,u,p,x),S=(0,i.WU)(f,h),x<=1&&S<T?(m=x,T=S):b*=.5)}return y&&(y[0]=v(t,n,a,l,m),y[1]=v(e,r,u,p,m)),o(T)}function w(t,e,n,i,r,o,a,s,u){for(var l=t,h=e,c=0,f=1/u,p=1;p<=u;p++){var d=p*f,g=v(t,n,r,a,d),y=v(e,i,o,s,d),m=g-l,_=y-h;c+=Math.sqrt(m*m+_*_),l=g,h=y}return c}function S(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function b(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function T(t,e,n,i,r){var a=t-2*e+n,s=2*(e-t),u=t-i,l=0;if(p(a)){if(d(s)){var h=-u/s;h>=0&&h<=1&&(r[l++]=h)}}else{var c=s*s-4*a*u;if(p(c)){h=-s/(2*a);h>=0&&h<=1&&(r[l++]=h)}else if(c>0){var f=o(c),v=(h=(-s+f)/(2*a),(-s-f)/(2*a));h>=0&&h<=1&&(r[l++]=h),v>=0&&v<=1&&(r[l++]=v)}}return l}function M(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function k(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}function D(t,e,n,r,a,u,l,p,d){var v,g=.005,y=1/0;h[0]=l,h[1]=p;for(var m=0;m<1;m+=.05){c[0]=S(t,n,a,m),c[1]=S(e,r,u,m);var _=(0,i.WU)(h,c);_<y&&(v=m,y=_)}y=1/0;for(var x=0;x<32;x++){if(g<s)break;var w=v-g,b=v+g;c[0]=S(t,n,a,w),c[1]=S(e,r,u,w);_=(0,i.WU)(c,h);if(w>=0&&_<y)v=w,y=_;else{f[0]=S(t,n,a,b),f[1]=S(e,r,u,b);var T=(0,i.WU)(f,h);b<=1&&T<y?(v=b,y=T):g*=.5}}return d&&(d[0]=S(t,n,a,v),d[1]=S(e,r,u,v)),o(y)}function C(t,e,n,i,r,o,a){for(var s=t,u=e,l=0,h=1/a,c=1;c<=a;c++){var f=c*h,p=S(t,n,r,f),d=S(e,i,o,f),v=p-s,g=d-u;l+=Math.sqrt(v*v+g*g),s=p,u=d}return l}},84602:function(t,e,n){n.d(e,{UK:function(){return p},A4:function(){return h},YB:function(){return l}});var i=n(66387),r=Math.log(2);function o(t,e,n,i,a,s){var u=i+"-"+a,l=t.length;if(s.hasOwnProperty(u))return s[u];if(1===e){var h=Math.round(Math.log((1<<l)-1&~a)/r);return t[n][h]}var c=i|1<<n,f=n+1;while(i&1<<f)f++;for(var p=0,d=0,v=0;d<l;d++){var g=1<<d;g&a||(p+=(v%2?-1:1)*t[n][d]*o(t,e-1,f,c,a|g,s),v++)}return s[u]=p,p}function a(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=o(n,8,0,0,0,i);if(0!==r){for(var a=[],s=0;s<8;s++)for(var u=0;u<8;u++)null==a[u]&&(a[u]=0),a[u]+=((s+u)%2?-1:1)*o(n,7,0===s?1:0,1<<s,1<<u,i)/r*e[s];return function(t,e,n){var i=e*a[6]+n*a[7]+1;t[0]=(e*a[0]+n*a[1]+a[2])/i,t[1]=(e*a[3]+n*a[4]+a[5])/i}}}var s="___zrEVENTSAVED",u=[];function l(t,e,n,i,r){return h(u,e,i,r,!0)&&h(t,n,u[0],u[1])}function h(t,e,n,r,o){if(e.getBoundingClientRect&&i.Z.domSupported&&!p(e)){var a=e[s]||(e[s]={}),u=c(e,a),l=f(u,a,o);if(l)return l(t,n,r),!0}return!1}function c(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,u=o%2,l=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[u]+":0",r[l]+":0",i[1-u]+":auto",r[1-l]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return n}function f(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,s=[],u=[],l=!0,h=0;h<4;h++){var c=t[h].getBoundingClientRect(),f=2*h,p=c.left,d=c.top;s.push(p,d),l=l&&o&&p===o[f]&&d===o[f+1],u.push(t[h].offsetLeft,t[h].offsetTop)}return l&&r?r:(e.srcCoords=s,e[i]=n?a(u,s):a(s,u))}function p(t){return"CANVAS"===t.nodeName.toUpperCase()}},66387:function(t,e){var n=function(){function t(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return t}(),i=function(){function t(){this.browser=new n,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!==typeof window}return t}(),r=new i;function o(t,e){var n=e.browser,i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(n.firefox=!0,n.version=i[1]),r&&(n.ie=!0,n.version=r[1]),o&&(n.edge=!0,n.version=o[1],n.newEdge=+o[1].split(".")[0]>18),a&&(n.weChat=!0),e.svgSupported="undefined"!==typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!n.ie&&!n.edge,e.pointerEventsSupported="onpointerdown"in window&&(n.edge||n.ie&&+n.version>=11),e.domSupported="undefined"!==typeof document;var s=document.documentElement.style;e.transform3dSupported=(n.ie&&"transition"in s||n.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||n.ie&&+n.version>=9}"object"===typeof wx&&"function"===typeof wx.getSystemInfoSync?(r.wxa=!0,r.touchEventsSupported=!0):"undefined"===typeof document&&"undefined"!==typeof self?r.worker=!0:"undefined"===typeof navigator?(r.node=!0,r.svgSupported=!0):o(navigator.userAgent,r),e["Z"]=r},61158:function(t,e,n){n.d(e,{OD:function(){return c},Oo:function(){return p},eV:function(){return u},iP:function(){return h},sT:function(){return v},x1:function(){return g},xg:function(){return d}});var i=n(66387),r=n(84602),o=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,a=[],s=i.Z.browser.firefox&&+i.Z.browser.version.split(".")[0]<39;function u(t,e,n,i){return n=n||{},i?l(t,e,n):s&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):l(t,e,n),n}function l(t,e,n){if(i.Z.domSupported&&t.getBoundingClientRect){var o=e.clientX,s=e.clientY;if((0,r.UK)(t)){var u=t.getBoundingClientRect();return n.zrX=o-u.left,void(n.zrY=s-u.top)}if((0,r.A4)(a,t,o,s))return n.zrX=a[0],void(n.zrY=a[1])}n.zrX=n.zrY=0}function h(t){return t||window.event}function c(t,e,n){if(e=h(e),null!=e.zrX)return e;var i=e.type,r=i&&i.indexOf("touch")>=0;if(r){var a="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];a&&u(t,a,e,n)}else{u(t,e,e,n);var s=f(e);e.zrDelta=s?s/120:-(e.detail||0)/3}var l=e.button;return null==e.which&&void 0!==l&&o.test(e.type)&&(e.which=1&l?1:2&l?3:4&l?2:0),e}function f(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,i=t.deltaY;if(null==n||null==i)return e;var r=0!==i?Math.abs(i):Math.abs(n),o=i>0?-1:i<0?1:n>0?-1:1;return 3*r*o}function p(t,e,n,i){t.addEventListener(e,n,i)}function d(t,e,n,i){t.removeEventListener(e,n,i)}var v=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0};function g(t){return 2===t.which||3===t.which}},32892:function(t,e,n){function i(){return[1,0,0,1,0,0]}function r(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function o(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function a(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],u=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=u,t}function s(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function u(t,e,n){var i=e[0],r=e[2],o=e[4],a=e[1],s=e[3],u=e[5],l=Math.sin(n),h=Math.cos(n);return t[0]=i*h+a*l,t[1]=-i*l+a*h,t[2]=r*h+s*l,t[3]=-r*l+h*s,t[4]=h*o+l*u,t[5]=h*u-l*o,t}function l(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function h(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],u=n*a-o*i;return u?(u=1/u,t[0]=a*u,t[1]=-o*u,t[2]=-i*u,t[3]=n*u,t[4]=(i*s-a*r)*u,t[5]=(o*r-n*s)*u,t):null}function c(t){var e=i();return o(e,t),e}n.d(e,{Iu:function(){return s},JG:function(){return o},U1:function(){return u},U_:function(){return h},Ue:function(){return i},bA:function(){return l},d9:function(){return c},dC:function(){return a},yR:function(){return r}})},19455:function(t,e,n){n.d(e,{Z:function(){return f}});var i=32,r=7;function o(t){var e=0;while(t>=i)e|=1&t,t>>=1;return t+e}function a(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){while(r<n&&i(t[r],t[r-1])<0)r++;s(t,e,r)}else while(r<n&&i(t[r],t[r-1])>=0)r++;return r-e}function s(t,e,n){n--;while(e<n){var i=t[e];t[e++]=t[n],t[n--]=i}}function u(t,e,n,i,r){for(i===e&&i++;i<n;i++){var o,a=t[i],s=e,u=i;while(s<u)o=s+u>>>1,r(a,t[o])<0?u=o:s=o+1;var l=i-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:while(l>0)t[s+l]=t[s+l-1],l--}t[s]=a}}function l(t,e,n,i,r,o){var a=0,s=0,u=1;if(o(t,e[n+r])>0){s=i-r;while(u<s&&o(t,e[n+r+u])>0)a=u,u=1+(u<<1),u<=0&&(u=s);u>s&&(u=s),a+=r,u+=r}else{s=r+1;while(u<s&&o(t,e[n+r-u])<=0)a=u,u=1+(u<<1),u<=0&&(u=s);u>s&&(u=s);var l=a;a=r-u,u=r-l}a++;while(a<u){var h=a+(u-a>>>1);o(t,e[n+h])>0?a=h+1:u=h}return u}function h(t,e,n,i,r,o){var a=0,s=0,u=1;if(o(t,e[n+r])<0){s=r+1;while(u<s&&o(t,e[n+r-u])<0)a=u,u=1+(u<<1),u<=0&&(u=s);u>s&&(u=s);var l=a;a=r-u,u=r-l}else{s=i-r;while(u<s&&o(t,e[n+r+u])>=0)a=u,u=1+(u<<1),u<=0&&(u=s);u>s&&(u=s),a+=r,u+=r}a++;while(a<u){var h=a+(u-a>>>1);o(t,e[n+h])<0?u=h:a=h+1}return u}function c(t,e){var n,i,o=r,a=0,s=0;a=t.length;var u=[];function c(t,e){n[s]=t,i[s]=e,s+=1}function f(){while(s>1){var t=s-2;if(t>=1&&i[t-1]<=i[t]+i[t+1]||t>=2&&i[t-2]<=i[t]+i[t-1])i[t-1]<i[t+1]&&t--;else if(i[t]>i[t+1])break;d(t)}}function p(){while(s>1){var t=s-2;t>0&&i[t-1]<i[t+1]&&t--,d(t)}}function d(r){var o=n[r],a=i[r],u=n[r+1],c=i[r+1];i[r]=a+c,r===s-3&&(n[r+1]=n[r+2],i[r+1]=i[r+2]),s--;var f=h(t[u],t,o,a,0,e);o+=f,a-=f,0!==a&&(c=l(t[o+a-1],t,u,c,c-1,e),0!==c&&(a<=c?v(o,a,u,c):g(o,a,u,c)))}function v(n,i,a,s){var c=0;for(c=0;c<i;c++)u[c]=t[n+c];var f=0,p=a,d=n;if(t[d++]=t[p++],0!==--s)if(1!==i){var v,g,y,m=o;while(1){v=0,g=0,y=!1;do{if(e(t[p],u[f])<0){if(t[d++]=t[p++],g++,v=0,0===--s){y=!0;break}}else if(t[d++]=u[f++],v++,g=0,1===--i){y=!0;break}}while((v|g)<m);if(y)break;do{if(v=h(t[p],u,f,i,0,e),0!==v){for(c=0;c<v;c++)t[d+c]=u[f+c];if(d+=v,f+=v,i-=v,i<=1){y=!0;break}}if(t[d++]=t[p++],0===--s){y=!0;break}if(g=l(u[f],t,p,s,0,e),0!==g){for(c=0;c<g;c++)t[d+c]=t[p+c];if(d+=g,p+=g,s-=g,0===s){y=!0;break}}if(t[d++]=u[f++],1===--i){y=!0;break}m--}while(v>=r||g>=r);if(y)break;m<0&&(m=0),m+=2}if(o=m,o<1&&(o=1),1===i){for(c=0;c<s;c++)t[d+c]=t[p+c];t[d+s]=u[f]}else{if(0===i)throw new Error;for(c=0;c<i;c++)t[d+c]=u[f+c]}}else{for(c=0;c<s;c++)t[d+c]=t[p+c];t[d+s]=u[f]}else for(c=0;c<i;c++)t[d+c]=u[f+c]}function g(n,i,a,s){var c=0;for(c=0;c<s;c++)u[c]=t[a+c];var f=n+i-1,p=s-1,d=a+s-1,v=0,g=0;if(t[d--]=t[f--],0!==--i)if(1!==s){var y=o;while(1){var m=0,_=0,x=!1;do{if(e(u[p],t[f])<0){if(t[d--]=t[f--],m++,_=0,0===--i){x=!0;break}}else if(t[d--]=u[p--],_++,m=0,1===--s){x=!0;break}}while((m|_)<y);if(x)break;do{if(m=i-h(u[p],t,n,i,i-1,e),0!==m){for(d-=m,f-=m,i-=m,g=d+1,v=f+1,c=m-1;c>=0;c--)t[g+c]=t[v+c];if(0===i){x=!0;break}}if(t[d--]=u[p--],1===--s){x=!0;break}if(_=s-l(t[f],u,0,s,s-1,e),0!==_){for(d-=_,p-=_,s-=_,g=d+1,v=p+1,c=0;c<_;c++)t[g+c]=u[v+c];if(s<=1){x=!0;break}}if(t[d--]=t[f--],0===--i){x=!0;break}y--}while(m>=r||_>=r);if(x)break;y<0&&(y=0),y+=2}if(o=y,o<1&&(o=1),1===s){for(d-=i,f-=i,g=d+1,v=f+1,c=i-1;c>=0;c--)t[g+c]=t[v+c];t[d]=u[p]}else{if(0===s)throw new Error;for(v=d-(s-1),c=0;c<s;c++)t[v+c]=u[c]}}else{for(d-=i,f-=i,g=d+1,v=f+1,c=i-1;c>=0;c--)t[g+c]=t[v+c];t[d]=u[p]}else for(v=d-(s-1),c=0;c<s;c++)t[v+c]=u[c]}return a<120?5:a<1542?10:a<119151?19:40,n=[],i=[],{mergeRuns:f,forceMergeRuns:p,pushRun:c}}function f(t,e,n,r){n||(n=0),r||(r=t.length);var s=r-n;if(!(s<2)){var l=0;if(s<i)return l=a(t,n,r,e),void u(t,n,r,n+l,e);var h=c(t,e),f=o(s);do{if(l=a(t,n,r,e),l<f){var p=s;p>f&&(p=f),u(t,n,n+p,n+l,e),l=p}h.pushRun(n,l),h.mergeRuns(),s-=l,n+=l}while(0!==s);h.forceMergeRuns()}}},45280:function(t,e,n){function i(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function r(t,e){return t[0]=e[0],t[1]=e[1],t}function o(t){return[t[0],t[1]]}function a(t,e,n){return t[0]=e,t[1]=n,t}function s(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function u(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t}function l(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function h(t){return Math.sqrt(c(t))}n.d(e,{Fp:function(){return w},Fv:function(){return p},IH:function(){return s},JG:function(){return r},Ne:function(){return _},TE:function(){return d},TK:function(){return v},Ue:function(){return i},VV:function(){return x},WU:function(){return y},Zh:function(){return h},bA:function(){return f},d9:function(){return o},lu:function(){return l},od:function(){return u},t7:function(){return m},t8:function(){return a}});function c(t){return t[0]*t[0]+t[1]*t[1]}function f(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function p(t,e){var n=h(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function d(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var v=d;function g(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var y=g;function m(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function _(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function x(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function w(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}},52776:function(t,e,n){var i=n(70655),r=n(8846),o=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="compound",e}return(0,i.ZT)(e,t),e.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},e.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},e.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},e.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},e.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),r.ZP.prototype.getBoundingRect.call(this)},e}(r.ZP);e["Z"]=o},7719:function(t,e,n){n.d(e,{ik:function(){return h},tj:function(){return l}});var i=n(70655),r=n(85823),o=n(60479),a=n(33051),s=n(14414),u="__zr_style_"+Math.round(10*Math.random()),l={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},h={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};l[u]=!0;var c=["z","z2","invisible"],f=["invisible"],p=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype._init=function(e){for(var n=(0,a.XP)(e),i=0;i<n.length;i++){var r=n[i];"style"===r?this.useStyle(e[r]):t.prototype.attrKV.call(this,r,e[r])}this.style||this.useStyle({})},e.prototype.beforeBrush=function(){},e.prototype.afterBrush=function(){},e.prototype.innerBeforeBrush=function(){},e.prototype.innerAfterBrush=function(){},e.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&g(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent){var a=this.parent;while(a){if(a.ignore)return!1;a=a.parent}}return!0},e.prototype.contain=function(t,e){return this.rectContain(t,e)},e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.rectContain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return i.contain(n[0],n[1])},e.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,n=this.getBoundingRect(),i=this.style,r=i.shadowBlur||0,a=i.shadowOffsetX||0,s=i.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new o.Z(0,0,0,0)),e?o.Z.applyTransform(t,n,e):t.copy(n),(r||a||s)&&(t.width+=2*r+Math.abs(a),t.height+=2*r+Math.abs(s),t.x=Math.min(t.x,t.x+a-r),t.y=Math.min(t.y,t.y+s-r));var u=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-u),t.y=Math.floor(t.y-u),t.width=Math.ceil(t.width+1+2*u),t.height=Math.ceil(t.height+1+2*u))}return t},e.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new o.Z(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},e.prototype.getPrevPaintRect=function(){return this._prevPaintRect},e.prototype.animateStyle=function(t){return this.animate("style",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},e.prototype.attrKV=function(e,n){"style"!==e?t.prototype.attrKV.call(this,e,n):this.style?this.setStyle(n):this.useStyle(n)},e.prototype.setStyle=function(t,e){return"string"===typeof t?this.style[t]=e:(0,a.l7)(this.style,t),this.dirtyStyle(),this},e.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=s.SE,this._rect&&(this._rect=null)},e.prototype.dirty=function(){this.dirtyStyle()},e.prototype.styleChanged=function(){return!!(this.__dirty&s.SE)},e.prototype.styleUpdated=function(){this.__dirty&=~s.SE},e.prototype.createStyle=function(t){return(0,a.nW)(l,t)},e.prototype.useStyle=function(t){t[u]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},e.prototype.isStyleObject=function(t){return t[u]},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.style&&!n.style&&(n.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,n,c)},e.prototype._applyStateObj=function(e,n,i,r,o,s){t.prototype._applyStateObj.call(this,e,n,i,r,o,s);var u,l=!(n&&r);if(n&&n.style?o?r?u=n.style:(u=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(u,n.style)):(u=this._mergeStyle(this.createStyle(),r?this.style:i.style),this._mergeStyle(u,n.style)):l&&(u=i.style),u)if(o){var h=this.style;if(this.style=this.createStyle(l?{}:h),l)for(var p=(0,a.XP)(h),d=0;d<p.length;d++){var v=p[d];v in u&&(u[v]=u[v],this.style[v]=h[v])}var g=(0,a.XP)(u);for(d=0;d<g.length;d++){v=g[d];this.style[v]=this.style[v]}this._transitionState(e,{style:u},s,this.getAnimationStyleProps())}else this.useStyle(u);var y=this.__inHover?f:c;for(d=0;d<y.length;d++){v=y[d];n&&null!=n[v]?this[v]=n[v]:l&&null!=i[v]&&(this[v]=i[v])}},e.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},e.prototype._mergeStyle=function(t,e){return(0,a.l7)(t,e),t},e.prototype.getAnimationStyleProps=function(){return h},e.initDefaultProps=function(){var t=e.prototype;t.type="displayable",t.invisible=!1,t.z=0,t.z2=0,t.zlevel=0,t.culling=!1,t.cursor="pointer",t.rectHover=!1,t.incremental=!1,t._rect=null,t.dirtyRectTolerance=0,t.__dirty=s.YV|s.SE}(),e}(r.Z),d=new o.Z(0,0,0,0),v=new o.Z(0,0,0,0);function g(t,e,n){return d.copy(t.getBoundingRect()),t.transform&&d.applyTransform(t.transform),v.width=e,v.height=n,!d.intersect(v)}e["ZP"]=p},31797:function(t,e){var n=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}();e["Z"]=n},38154:function(t,e,n){var i=n(70655),r=n(33051),o=n(85823),a=n(60479),s=function(t){function e(e){var n=t.call(this)||this;return n.isGroup=!0,n._children=[],n.attr(e),n}return(0,i.ZT)(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.children=function(){return this._children.slice()},e.prototype.childAt=function(t){return this._children[t]},e.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},e.prototype.childCount=function(){return this._children.length},e.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},e.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},e.prototype.replace=function(t,e){var n=r.cq(this._children,t);return n>=0&&this.replaceAt(e,n),this},e.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];if(t&&t!==this&&t.parent!==this&&t!==i){n[e]=t,i.parent=null;var r=this.__zr;r&&i.removeSelfFromZr(r),this._doAdd(t)}return this},e.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},e.prototype.remove=function(t){var e=this.__zr,n=this._children,i=r.cq(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},e.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},e.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},e.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++){var i=this._children[n];i.addSelfToZr(e)}},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++){var i=this._children[n];i.removeSelfFromZr(e)}},e.prototype.getBoundingRect=function(t){for(var e=new a.Z(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var s=n[o];if(!s.ignore&&!s.invisible){var u=s.getBoundingRect(),l=s.getLocalTransform(i);l?(a.Z.applyTransform(e,u,l),r=r||e.clone(),r.union(e)):(r=r||u.clone(),r.union(u))}}return r||e},e}(o.Z);s.prototype.type="group",e["Z"]=s},44535:function(t,e,n){var i=n(70655),r=n(7719),o=n(60479),a=n(33051),s=(0,a.ce)({x:0,y:0},r.tj),u={style:(0,a.ce)({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},r.ik.style)};function l(t){return!!(t&&"string"!==typeof t&&t.width&&t.height)}var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,i.ZT)(e,t),e.prototype.createStyle=function(t){return(0,a.nW)(s,t)},e.prototype._getSize=function(t){var e=this.style,n=e[t];if(null!=n)return n;var i=l(e.image)?e.image:this.__image;if(!i)return 0;var r="width"===t?"height":"width",o=e[r];return null==o?i[t]:i[t]/i[r]*o},e.prototype.getWidth=function(){return this._getSize("width")},e.prototype.getHeight=function(){return this._getSize("height")},e.prototype.getAnimationStyleProps=function(){return u},e.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new o.Z(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},e}(r.ZP);h.prototype.type="image",e["ZP"]=h},74438:function(t,e,n){var i=n(70655),r=n(31797),o=function(t){function e(e,n,i,r,o,a){var s=t.call(this,o)||this;return s.x=null==e?0:e,s.y=null==n?0:n,s.x2=null==i?1:i,s.y2=null==r?0:r,s.type="linear",s.global=a||!1,s}return(0,i.ZT)(e,t),e}(r.Z);e["Z"]=o},8846:function(t,e,n){n.d(e,{$t:function(){return O},ZP:function(){return z}});var i=n(70655),r=n(7719),o=n(14014),a=n(33640),s=n(29023);function u(t,e,n,i,r,o,a,u,l,h,c){if(0===l)return!1;var f=l;if(c>e+f&&c>i+f&&c>o+f&&c>u+f||c<e-f&&c<i-f&&c<o-f&&c<u-f||h>t+f&&h>n+f&&h>r+f&&h>a+f||h<t-f&&h<n-f&&h<r-f&&h<a-f)return!1;var p=s.t1(t,e,n,i,r,o,a,u,h,c,null);return p<=f/2}var l=n(25674),h=n(3266),c=2*Math.PI;function f(t,e,n,i,r,o,a,s,u){if(0===a)return!1;var l=a;s-=t,u-=e;var f=Math.sqrt(s*s+u*u);if(f-l>n||f+l<n)return!1;if(Math.abs(i-r)%c<1e-4)return!0;if(o){var p=i;i=(0,h.m)(r),r=(0,h.m)(p)}else i=(0,h.m)(i),r=(0,h.m)(r);i>r&&(r+=c);var d=Math.atan2(u,s);return d<0&&(d+=c),d>=i&&d<=r||d+c>=i&&d+c<=r}var p=n(47637),d=o.Z.CMD,v=2*Math.PI,g=1e-4;function y(t,e){return Math.abs(t-e)<g}var m=[-1,-1,-1],_=[-1,-1];function x(){var t=_[0];_[0]=_[1],_[1]=t}function w(t,e,n,i,r,o,a,u,l,h){if(h>e&&h>i&&h>o&&h>u||h<e&&h<i&&h<o&&h<u)return 0;var c=s.kD(e,i,o,u,h,m);if(0===c)return 0;for(var f=0,p=-1,d=void 0,v=void 0,g=0;g<c;g++){var y=m[g],w=0===y||1===y?.5:1,S=s.af(t,n,r,a,y);S<l||(p<0&&(p=s.pP(e,i,o,u,_),_[1]<_[0]&&p>1&&x(),d=s.af(e,i,o,u,_[0]),p>1&&(v=s.af(e,i,o,u,_[1]))),2===p?y<_[0]?f+=d<e?w:-w:y<_[1]?f+=v<d?w:-w:f+=u<v?w:-w:y<_[0]?f+=d<e?w:-w:f+=u<d?w:-w)}return f}function S(t,e,n,i,r,o,a,u){if(u>e&&u>i&&u>o||u<e&&u<i&&u<o)return 0;var l=s.Jz(e,i,o,u,m);if(0===l)return 0;var h=s.QC(e,i,o);if(h>=0&&h<=1){for(var c=0,f=s.Zm(e,i,o,h),p=0;p<l;p++){var d=0===m[p]||1===m[p]?.5:1,v=s.Zm(t,n,r,m[p]);v<a||(m[p]<h?c+=f<e?d:-d:c+=o<f?d:-d)}return c}d=0===m[0]||1===m[0]?.5:1,v=s.Zm(t,n,r,m[0]);return v<a?0:o<e?d:-d}function b(t,e,n,i,r,o,a,s){if(s-=e,s>n||s<-n)return 0;var u=Math.sqrt(n*n-s*s);m[0]=-u,m[1]=u;var l=Math.abs(i-r);if(l<1e-4)return 0;if(l>=v-1e-4){i=0,r=v;var h=o?1:-1;return a>=m[0]+t&&a<=m[1]+t?h:0}if(i>r){var c=i;i=r,r=c}i<0&&(i+=v,r+=v);for(var f=0,p=0;p<2;p++){var d=m[p];if(d+t>a){var g=Math.atan2(s,d);h=o?1:-1;g<0&&(g=v+g),(g>=i&&g<=r||g+v>=i&&g+v<=r)&&(g>Math.PI/2&&g<1.5*Math.PI&&(h=-h),f+=h)}}return f}function T(t,e,n,i,r){for(var o,s,h=t.data,c=t.len(),v=0,g=0,m=0,_=0,x=0,T=0;T<c;){var M=h[T++],k=1===T;switch(M===d.M&&T>1&&(n||(v+=(0,p.Z)(g,m,_,x,i,r))),k&&(g=h[T],m=h[T+1],_=g,x=m),M){case d.M:_=h[T++],x=h[T++],g=_,m=x;break;case d.L:if(n){if(a.m(g,m,h[T],h[T+1],e,i,r))return!0}else v+=(0,p.Z)(g,m,h[T],h[T+1],i,r)||0;g=h[T++],m=h[T++];break;case d.C:if(n){if(u(g,m,h[T++],h[T++],h[T++],h[T++],h[T],h[T+1],e,i,r))return!0}else v+=w(g,m,h[T++],h[T++],h[T++],h[T++],h[T],h[T+1],i,r)||0;g=h[T++],m=h[T++];break;case d.Q:if(n){if(l.m(g,m,h[T++],h[T++],h[T],h[T+1],e,i,r))return!0}else v+=S(g,m,h[T++],h[T++],h[T],h[T+1],i,r)||0;g=h[T++],m=h[T++];break;case d.A:var D=h[T++],C=h[T++],I=h[T++],P=h[T++],A=h[T++],O=h[T++];T+=1;var Z=!!(1-h[T++]);o=Math.cos(A)*I+D,s=Math.sin(A)*P+C,k?(_=o,x=s):v+=(0,p.Z)(g,m,o,s,i,r);var L=(i-D)*P/I+D;if(n){if(f(D,C,P,A,A+O,Z,e,L,r))return!0}else v+=b(D,C,P,A,A+O,Z,L,r);g=Math.cos(A+O)*I+D,m=Math.sin(A+O)*P+C;break;case d.R:_=g=h[T++],x=m=h[T++];var R=h[T++],z=h[T++];if(o=_+R,s=x+z,n){if(a.m(_,x,o,x,e,i,r)||a.m(o,x,o,s,e,i,r)||a.m(o,s,_,s,e,i,r)||a.m(_,s,_,x,e,i,r))return!0}else v+=(0,p.Z)(o,x,o,s,i,r),v+=(0,p.Z)(_,s,_,x,i,r);break;case d.Z:if(n){if(a.m(g,m,_,x,e,i,r))return!0}else v+=(0,p.Z)(g,m,_,x,i,r);g=_,m=x;break}}return n||y(m,x)||(v+=(0,p.Z)(g,m,_,x,i,r)||0),0!==v}function M(t,e,n){return T(t,0,!1,e,n)}function k(t,e,n,i){return T(t,e,!0,n,i)}var D=n(33051),C=n(21092),I=n(4990),P=n(14414),A=n(87411),O=(0,D.ce)({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},r.tj),Z={style:(0,D.ce)({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},r.ik.style)},L=A.dN.concat(["invisible","culling","z","z2","zlevel","parent"]),R=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.update=function(){var n=this;t.prototype.update.call(this);var i=this.style;if(i.decal){var r=this._decalEl=this._decalEl||new e;r.buildPath===e.prototype.buildPath&&(r.buildPath=function(t){n.buildPath(t,n.shape)}),r.silent=!0;var o=r.style;for(var a in i)o[a]!==i[a]&&(o[a]=i[a]);o.fill=i.fill?i.decal:null,o.decal=null,o.shadowColor=null,i.strokeFirst&&(o.stroke=null);for(var s=0;s<L.length;++s)r[L[s]]=this[L[s]];r.__dirty|=P.YV}else this._decalEl&&(this._decalEl=null)},e.prototype.getDecalElement=function(){return this._decalEl},e.prototype._init=function(e){var n=(0,D.XP)(e);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var r=0;r<n.length;r++){var o=n[r],a=e[o];"style"===o?this.style?(0,D.l7)(this.style,a):this.useStyle(a):"shape"===o?(0,D.l7)(this.shape,a):t.prototype.attrKV.call(this,o,a)}this.style||this.useStyle({})},e.prototype.getDefaultStyle=function(){return null},e.prototype.getDefaultShape=function(){return{}},e.prototype.canBeInsideText=function(){return this.hasFill()},e.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if((0,D.HD)(t)){var e=(0,C.L0)(t,0);return e>.5?I.vU:e>.2?I.iv:I.GD}if(t)return I.GD}return I.vU},e.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if((0,D.HD)(e)){var n=this.__zr,i=!(!n||!n.isDarkMode()),r=(0,C.L0)(t,0)<I.Ak;if(i===r)return e}},e.prototype.buildPath=function(t,e,n){},e.prototype.pathUpdated=function(){this.__dirty&=~P.RH},e.prototype.getUpdatedPathProxy=function(t){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},e.prototype.createPathProxy=function(){this.path=new o.Z(!1)},e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))},e.prototype.hasFill=function(){var t=this.style,e=t.fill;return null!=e&&"none"!==e},e.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,n=!t;if(n){var i=!1;this.path||(i=!0,this.createPathProxy());var r=this.path;(i||this.__dirty&P.RH)&&(r.beginPath(),this.buildPath(r,this.shape,!1),this.pathUpdated()),t=r.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||n){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var u=this.strokeContainThreshold;s=Math.max(s,null==u?4:u)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),k(o,a/s,t,e)))return!0}if(this.hasFill())return M(o,t,e)}return!1},e.prototype.dirtyShape=function(){this.__dirty|=P.RH,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},e.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},e.prototype.animateShape=function(t){return this.animate("shape",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},e.prototype.attrKV=function(e,n){"shape"===e?this.setShape(n):t.prototype.attrKV.call(this,e,n)},e.prototype.setShape=function(t,e){var n=this.shape;return n||(n=this.shape={}),"string"===typeof t?n[t]=e:(0,D.l7)(n,t),this.dirtyShape(),this},e.prototype.shapeChanged=function(){return!!(this.__dirty&P.RH)},e.prototype.createStyle=function(t){return(0,D.nW)(O,t)},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.shape&&!n.shape&&(n.shape=(0,D.l7)({},this.shape))},e.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,u=!(n&&r);if(n&&n.shape?o?r?s=n.shape:(s=(0,D.l7)({},i.shape),(0,D.l7)(s,n.shape)):(s=(0,D.l7)({},r?this.shape:i.shape),(0,D.l7)(s,n.shape)):u&&(s=i.shape),s)if(o){this.shape=(0,D.l7)({},this.shape);for(var l={},h=(0,D.XP)(s),c=0;c<h.length;c++){var f=h[c];"object"===typeof s[f]?this.shape[f]=s[f]:l[f]=s[f]}this._transitionState(e,{shape:l},a)}else this.shape=s,this.dirtyShape()},e.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},e.prototype.getAnimationStyleProps=function(){return Z},e.prototype.isZeroArea=function(){return!1},e.extend=function(t){var n=function(e){function n(n){var i=e.call(this,n)||this;return t.init&&t.init.call(i,n),i}return(0,i.ZT)(n,e),n.prototype.getDefaultStyle=function(){return(0,D.d9)(t.style)},n.prototype.getDefaultShape=function(){return(0,D.d9)(t.shape)},n}(e);for(var r in t)"function"===typeof t[r]&&(n.prototype[r]=t[r]);return n},e.initDefaultProps=function(){var t=e.prototype;t.type="path",t.strokeContainThreshold=5,t.segmentIgnoreThreshold=0,t.subPixelOptimize=!1,t.autoBatch=!1,t.__dirty=P.YV|P.SE|P.RH}(),e}(r.ZP),z=R},36369:function(t,e,n){var i=n(70655),r=n(31797),o=function(t){function e(e,n,i,r,o){var a=t.call(this,r)||this;return a.x=null==e?.5:e,a.y=null==n?.5:n,a.r=null==i?.5:i,a.type="radial",a.global=o||!1,a}return(0,i.ZT)(e,t),e}(r.Z);e["Z"]=o},71505:function(t,e,n){var i=n(70655),r=n(7719),o=n(80423),a=n(8846),s=n(33051),u=n(23132),l=(0,s.ce)({strokeFirst:!0,font:u.Uo,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},a.$t),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,i.ZT)(e,t),e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},e.prototype.hasFill=function(){var t=this.style,e=t.fill;return null!=e&&"none"!==e},e.prototype.createStyle=function(t){return(0,s.nW)(l,t)},e.prototype.setBoundingRect=function(t){this._rect=t},e.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+="":e="";var n=(0,o.lP)(e,t.font,t.textAlign,t.textBaseline);if(n.x+=t.x||0,n.y+=t.y||0,this.hasStroke()){var i=t.lineWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect},e.initDefaultProps=function(){var t=e.prototype;t.dirtyRectTolerance=10}(),e}(r.ZP);h.prototype.type="tspan",e["Z"]=h},96498:function(t,e,n){n.d(e,{VG:function(){return x},Y1:function(){return S}});var i=n(70655),r=n(66918),o=n(71505),a=n(33051),s=n(80423),u=n(44535),l=n(25293),h=n(60479),c=n(7719),f=n(23132),p={fill:"#000"},d=2,v={style:(0,a.ce)({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},c.ik.style)},g=function(t){function e(e){var n=t.call(this)||this;return n.type="text",n._children=[],n._defaultStyle=p,n.attr(e),n}return(0,i.ZT)(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.update=function(){t.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var n=this._children[e];n.zlevel=this.zlevel,n.z=this.z,n.z2=this.z2,n.culling=this.culling,n.cursor=this.cursor,n.invisible=this.invisible}},e.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):t.prototype.updateTransform.call(this)},e.prototype.getLocalTransform=function(e){var n=this.innerTransformable;return n?n.getLocalTransform(e):t.prototype.getLocalTransform.call(this,e)},e.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),t.prototype.getComputedTransform.call(this)},e.prototype._updateSubTexts=function(){this._childCursor=0,b(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=e},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=null},e.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new h.Z(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),s=o.getLocalTransform(n);s?(t.copy(a),t.applyTransform(s),i=i||t.clone(),i.union(t)):(i=i||a.clone(),i.union(a))}this._rect=i||t}return this._rect},e.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||p},e.prototype.setTextContent=function(t){0},e.prototype._mergeStyle=function(t,e){if(!e)return t;var n=e.rich,i=t.rich||n&&{};return(0,a.l7)(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i),t},e.prototype._mergeRich=function(t,e){for(var n=(0,a.XP)(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},(0,a.l7)(t[r],e[r])}},e.prototype.getAnimationStyleProps=function(){return v},e.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},e.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||f.Uo,n=t.padding,i=C(t),a=(0,r.NY)(i,t),u=I(t),l=!!t.backgroundColor,c=a.outerHeight,p=a.outerWidth,v=a.contentWidth,g=a.lines,y=a.lineHeight,m=this._defaultStyle,_=t.x||0,x=t.y||0,S=t.align||m.align||"left",b=t.verticalAlign||m.verticalAlign||"top",T=_,P=(0,s.mU)(x,a.contentHeight,b);if(u||n){var A=(0,s.M3)(_,p,S),O=(0,s.mU)(x,c,b);u&&this._renderBackground(t,t,A,O,p,c)}P+=y/2,n&&(T=D(_,S,n),"top"===b?P+=n[0]:"bottom"===b&&(P-=n[2]));for(var Z=0,L=!1,R=(k("fill"in t?t.fill:(L=!0,m.fill))),z=(M("stroke"in t?t.stroke:l||m.autoStroke&&!L?null:(Z=d,m.stroke))),B=t.textShadowBlur>0,N=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),E=a.calculatedLineHeight,F=0;F<g.length;F++){var H=this._getOrCreateChild(o.Z),W=H.createStyle();H.useStyle(W),W.text=g[F],W.x=T,W.y=P,S&&(W.textAlign=S),W.textBaseline="middle",W.opacity=t.opacity,W.strokeFirst=!0,B&&(W.shadowBlur=t.textShadowBlur||0,W.shadowColor=t.textShadowColor||"transparent",W.shadowOffsetX=t.textShadowOffsetX||0,W.shadowOffsetY=t.textShadowOffsetY||0),W.stroke=z,W.fill=R,z&&(W.lineWidth=t.lineWidth||Z,W.lineDash=t.lineDash,W.lineDashOffset=t.lineDashOffset||0),W.font=e,w(W,t),P+=y,N&&H.setBoundingRect(new h.Z((0,s.M3)(W.x,t.width,W.textAlign),(0,s.mU)(W.y,E,W.textBaseline),v,E))}},e.prototype._updateRichTexts=function(){var t=this.style,e=C(t),n=(0,r.$F)(e,t),i=n.width,o=n.outerWidth,a=n.outerHeight,u=t.padding,l=t.x||0,h=t.y||0,c=this._defaultStyle,f=t.align||c.align,p=t.verticalAlign||c.verticalAlign,d=(0,s.M3)(l,o,f),v=(0,s.mU)(h,a,p),g=d,y=v;u&&(g+=u[3],y+=u[0]);var m=g+i;I(t)&&this._renderBackground(t,t,d,v,o,a);for(var _=!!t.backgroundColor,x=0;x<n.lines.length;x++){var w=n.lines[x],S=w.tokens,b=S.length,T=w.lineHeight,M=w.width,k=0,D=g,P=m,A=b-1,O=void 0;while(k<b&&(O=S[k],!O.align||"left"===O.align))this._placeToken(O,t,T,y,D,"left",_),M-=O.width,D+=O.width,k++;while(A>=0&&(O=S[A],"right"===O.align))this._placeToken(O,t,T,y,P,"right",_),M-=O.width,P-=O.width,A--;D+=(i-(D-g)-(m-P)-M)/2;while(k<=A)O=S[k],this._placeToken(O,t,T,y,D+O.width/2,"center",_),D+=O.width,k++;y+=T}},e.prototype._placeToken=function(t,e,n,i,r,u,l){var c=e.rich[t.styleName]||{};c.text=t.text;var p=t.verticalAlign,v=i+n/2;"top"===p?v=i+t.height/2:"bottom"===p&&(v=i+n-t.height/2);var g=!t.isLineHolder&&I(c);g&&this._renderBackground(c,e,"right"===u?r-t.width:"center"===u?r-t.width/2:r,v-t.height/2,t.width,t.height);var y=!!c.backgroundColor,m=t.textPadding;m&&(r=D(r,u,m),v-=t.height/2-m[0]-t.innerHeight/2);var _=this._getOrCreateChild(o.Z),x=_.createStyle();_.useStyle(x);var S=this._defaultStyle,b=!1,T=0,C=k("fill"in c?c.fill:"fill"in e?e.fill:(b=!0,S.fill)),P=M("stroke"in c?c.stroke:"stroke"in e?e.stroke:y||l||S.autoStroke&&!b?null:(T=d,S.stroke)),A=c.textShadowBlur>0||e.textShadowBlur>0;x.text=t.text,x.x=r,x.y=v,A&&(x.shadowBlur=c.textShadowBlur||e.textShadowBlur||0,x.shadowColor=c.textShadowColor||e.textShadowColor||"transparent",x.shadowOffsetX=c.textShadowOffsetX||e.textShadowOffsetX||0,x.shadowOffsetY=c.textShadowOffsetY||e.textShadowOffsetY||0),x.textAlign=u,x.textBaseline="middle",x.font=t.font||f.Uo,x.opacity=(0,a.R1)(c.opacity,e.opacity,1),w(x,c),P&&(x.lineWidth=(0,a.R1)(c.lineWidth,e.lineWidth,T),x.lineDash=(0,a.pD)(c.lineDash,e.lineDash),x.lineDashOffset=e.lineDashOffset||0,x.stroke=P),C&&(x.fill=C);var O=t.contentWidth,Z=t.contentHeight;_.setBoundingRect(new h.Z((0,s.M3)(x.x,O,x.textAlign),(0,s.mU)(x.y,Z,x.textBaseline),O,Z))},e.prototype._renderBackground=function(t,e,n,i,r,o){var s,h,c=t.backgroundColor,f=t.borderWidth,p=t.borderColor,d=c&&c.image,v=c&&!d,g=t.borderRadius,y=this;if(v||t.lineHeight||f&&p){s=this._getOrCreateChild(l.Z),s.useStyle(s.createStyle()),s.style.fill=null;var m=s.shape;m.x=n,m.y=i,m.width=r,m.height=o,m.r=g,s.dirtyShape()}if(v){var _=s.style;_.fill=c||null,_.fillOpacity=(0,a.pD)(t.fillOpacity,1)}else if(d){h=this._getOrCreateChild(u.ZP),h.onload=function(){y.dirtyStyle()};var x=h.style;x.image=c.image,x.x=n,x.y=i,x.width=r,x.height=o}if(f&&p){_=s.style;_.lineWidth=f,_.stroke=p,_.strokeOpacity=(0,a.pD)(t.strokeOpacity,1),_.lineDash=t.borderDash,_.lineDashOffset=t.borderDashOffset||0,s.strokeContainThreshold=0,s.hasFill()&&s.hasStroke()&&(_.strokeFirst=!0,_.lineWidth*=2)}var w=(s||h).style;w.shadowBlur=t.shadowBlur||0,w.shadowColor=t.shadowColor||"transparent",w.shadowOffsetX=t.shadowOffsetX||0,w.shadowOffsetY=t.shadowOffsetY||0,w.opacity=(0,a.R1)(t.opacity,e.opacity,1)},e.makeFont=function(t){var e="";return S(t)&&(e=[t.fontStyle,t.fontWeight,x(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&(0,a.fy)(e)||t.textFont||t.font},e}(c.ZP),y={left:!0,right:1,center:1},m={top:1,bottom:1,middle:1},_=["fontStyle","fontWeight","fontSize","fontFamily"];function x(t){return"string"!==typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?f.n5+"px":t+"px":t}function w(t,e){for(var n=0;n<_.length;n++){var i=_[n],r=e[i];null!=r&&(t[i]=r)}}function S(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function b(t){return T(t),(0,a.S6)(t.rich,T),t}function T(t){if(t){t.font=g.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||y[e]?e:"left";var n=t.verticalAlign;"center"===n&&(n="middle"),t.verticalAlign=null==n||m[n]?n:"top";var i=t.padding;i&&(t.padding=(0,a.MY)(t.padding))}}function M(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function k(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function D(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function C(t){var e=t.text;return null!=e&&(e+=""),e}function I(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}e["ZP"]=g},14414:function(t,e,n){n.d(e,{RH:function(){return o},SE:function(){return r},YV:function(){return i}});var i=1,r=2,o=4},8007:function(t,e,n){n.d(e,{Gq:function(){return s},ko:function(){return a},v5:function(){return l}});var i=n(92528),r=n(23132),o=new i.ZP(50);function a(t){if("string"===typeof t){var e=o.get(t);return e&&e.image}return t}function s(t,e,n,i,a){if(t){if("string"===typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var s=o.get(t),h={hostEl:n,cb:i,cbPayload:a};return s?(e=s.image,!l(e)&&s.pending.push(h)):(e=r.qW.loadImage(t,u,u),e.__zrImageSrc=t,o.put(t,e.__cachedImgObj={image:e,pending:[h]})),e}return t}return e}function u(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function l(t){return t&&t.width&&t.height}},66918:function(t,e,n){n.d(e,{$F:function(){return v},NY:function(){return c},aF:function(){return s}});var i=n(8007),r=n(33051),o=n(80423),a=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function s(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=u(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=l(o[a],r);return o.join("\n")}function u(t,e,n,i){i=i||{};var a=(0,r.l7)({},i);a.font=e,n=(0,r.pD)(n,"..."),a.maxIterations=(0,r.pD)(i.maxIterations,2);var s=a.minChar=(0,r.pD)(i.minChar,0);a.cnCharWidth=(0,o.dz)("国",e);var u=a.ascCharWidth=(0,o.dz)("a",e);a.placeholder=(0,r.pD)(i.placeholder,"");for(var l=t=Math.max(0,t-1),h=0;h<s&&l>=u;h++)l-=u;var c=(0,o.dz)(n,e);return c>l&&(n="",c=0),l=t-c,a.ellipsis=n,a.ellipsisWidth=c,a.contentWidth=l,a.containerWidth=t,a}function l(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var a=(0,o.dz)(t,i);if(a<=n)return t;for(var s=0;;s++){if(a<=r||s>=e.maxIterations){t+=e.ellipsis;break}var u=0===s?h(t,r,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*r/a):0;t=t.substr(0,u),a=(0,o.dz)(t,i)}return""===t&&(t=e.placeholder),t}function h(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}function c(t,e){null!=t&&(t+="");var n,i=e.overflow,a=e.padding,s=e.font,h="truncate"===i,c=(0,o.Dp)(s),f=(0,r.pD)(e.lineHeight,c),p=!!e.backgroundColor,d="truncate"===e.lineOverflow,v=e.width;n=null==v||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?x(t,e.font,v,"breakAll"===i,0).lines:[];var g=n.length*f,y=(0,r.pD)(e.height,g);if(g>y&&d){var m=Math.floor(y/f);n=n.slice(0,m)}if(t&&h&&null!=v)for(var _=u(v,s,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),w=0;w<n.length;w++)n[w]=l(n[w],_);var S=y,b=0;for(w=0;w<n.length;w++)b=Math.max((0,o.dz)(n[w],s),b);null==v&&(v=b);var T=b;return a&&(S+=a[0]+a[2],T+=a[1]+a[3],v+=a[1]+a[3]),p&&(T=v),{lines:n,height:y,outerWidth:T,outerHeight:S,lineHeight:f,calculatedLineHeight:c,contentWidth:b,contentHeight:g,width:v}}var f=function(){function t(){}return t}(),p=function(){function t(t){this.tokens=[],t&&(this.tokens=t)}return t}(),d=function(){function t(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]}return t}();function v(t,e){var n=new d;if(null!=t&&(t+=""),!t)return n;var u,l=e.width,h=e.height,c=e.overflow,f="break"!==c&&"breakAll"!==c||null==l?null:{width:l,accumWidth:0,breakAll:"breakAll"===c},p=a.lastIndex=0;while(null!=(u=a.exec(t))){var v=u.index;v>p&&g(n,t.substring(p,v),e,f),g(n,u[2],e,f,u[1]),p=a.lastIndex}p<t.length&&g(n,t.substring(p,t.length),e,f);var y=[],m=0,_=0,x=e.padding,w="truncate"===c,S="truncate"===e.lineOverflow;function b(t,e,n){t.width=e,t.lineHeight=n,m+=n,_=Math.max(_,e)}t:for(var T=0;T<n.lines.length;T++){for(var M=n.lines[T],k=0,D=0,C=0;C<M.tokens.length;C++){var I=M.tokens[C],P=I.styleName&&e.rich[I.styleName]||{},A=I.textPadding=P.padding,O=A?A[1]+A[3]:0,Z=I.font=P.font||e.font;I.contentHeight=(0,o.Dp)(Z);var L=(0,r.pD)(P.height,I.contentHeight);if(I.innerHeight=L,A&&(L+=A[0]+A[2]),I.height=L,I.lineHeight=(0,r.R1)(P.lineHeight,e.lineHeight,L),I.align=P&&P.align||e.align,I.verticalAlign=P&&P.verticalAlign||"middle",S&&null!=h&&m+I.lineHeight>h){C>0?(M.tokens=M.tokens.slice(0,C),b(M,D,k),n.lines=n.lines.slice(0,T+1)):n.lines=n.lines.slice(0,T);break t}var R=P.width,z=null==R||"auto"===R;if("string"===typeof R&&"%"===R.charAt(R.length-1))I.percentWidth=R,y.push(I),I.contentWidth=(0,o.dz)(I.text,Z);else{if(z){var B=P.backgroundColor,N=B&&B.image;N&&(N=i.ko(N),i.v5(N)&&(I.width=Math.max(I.width,N.width*L/N.height)))}var E=w&&null!=l?l-D:null;null!=E&&E<I.width?!z||E<O?(I.text="",I.width=I.contentWidth=0):(I.text=s(I.text,E-O,Z,e.ellipsis,{minChar:e.truncateMinChar}),I.width=I.contentWidth=(0,o.dz)(I.text,Z)):I.contentWidth=(0,o.dz)(I.text,Z)}I.width+=O,D+=I.width,P&&(k=Math.max(k,I.lineHeight))}b(M,D,k)}n.outerWidth=n.width=(0,r.pD)(l,_),n.outerHeight=n.height=(0,r.pD)(h,m),n.contentHeight=m,n.contentWidth=_,x&&(n.outerWidth+=x[1]+x[3],n.outerHeight+=x[0]+x[2]);for(T=0;T<y.length;T++){I=y[T];var F=I.percentWidth;I.width=parseInt(F,10)/100*n.width}return n}function g(t,e,n,i,r){var a,s,u=""===e,l=r&&n.rich[r]||{},h=t.lines,c=l.font||n.font,d=!1;if(i){var v=l.padding,g=v?v[1]+v[3]:0;if(null!=l.width&&"auto"!==l.width){var y=(0,o.GM)(l.width,i.width)+g;h.length>0&&y+i.accumWidth>i.width&&(a=e.split("\n"),d=!0),i.accumWidth=y}else{var m=x(e,c,i.width,i.breakAll,i.accumWidth);i.accumWidth=m.accumWidth+g,s=m.linesWidths,a=m.lines}}else a=e.split("\n");for(var _=0;_<a.length;_++){var w=a[_],S=new f;if(S.styleName=r,S.text=w,S.isLineHolder=!w&&!u,"number"===typeof l.width?S.width=l.width:S.width=s?s[_]:(0,o.dz)(w,c),_||d)h.push(new p([S]));else{var b=(h[h.length-1]||(h[0]=new p)).tokens,T=b.length;1===T&&b[0].isLineHolder?b[0]=S:(w||!T||u)&&b.push(S)}}}function y(t){var e=t.charCodeAt(0);return e>=33&&e<=383}var m=(0,r.u4)(",&?/;] ".split(""),(function(t,e){return t[e]=!0,t}),{});function _(t){return!y(t)||!!m[t]}function x(t,e,n,i,r){for(var a=[],s=[],u="",l="",h=0,c=0,f=0;f<t.length;f++){var p=t.charAt(f);if("\n"!==p){var d=(0,o.dz)(p,e),v=!i&&!_(p);(a.length?c+d>n:r+c+d>n)?c?(u||l)&&(v?(u||(u=l,l="",h=0,c=h),a.push(u),s.push(c-h),l+=p,h+=d,u="",c=h):(l&&(u+=l,l="",h=0),a.push(u),s.push(c),u=p,c=d)):v?(a.push(l),s.push(h),l=p,h=d):(a.push(p),s.push(d)):(c+=d,v?(l+=p,h+=d):(l&&(u+=l,l="",h=0),u+=p))}else l&&(u+=l,c+=h),a.push(u),s.push(c),u="",l="",h=0,c=0}return a.length||u||(u=t,l="",h=0),l&&(u+=l),u&&(a.push(u),s.push(c)),1===a.length&&(c+=r),{accumWidth:c,lines:a,linesWidths:s}}},12525:function(t,e,n){n.d(e,{L:function(){return o}});var i=n(45280);function r(t,e,n,r){var o,a,s,u,l=[],h=[],c=[],f=[];if(r){s=[1/0,1/0],u=[-1/0,-1/0];for(var p=0,d=t.length;p<d;p++)(0,i.VV)(s,s,t[p]),(0,i.Fp)(u,u,t[p]);(0,i.VV)(s,s,r[0]),(0,i.Fp)(u,u,r[1])}for(p=0,d=t.length;p<d;p++){var v=t[p];if(n)o=t[p?p-1:d-1],a=t[(p+1)%d];else{if(0===p||p===d-1){l.push((0,i.d9)(t[p]));continue}o=t[p-1],a=t[p+1]}(0,i.lu)(h,a,o),(0,i.bA)(h,h,e);var g=(0,i.TE)(v,o),y=(0,i.TE)(v,a),m=g+y;0!==m&&(g/=m,y/=m),(0,i.bA)(c,h,-g),(0,i.bA)(f,h,y);var _=(0,i.IH)([],v,c),x=(0,i.IH)([],v,f);r&&((0,i.Fp)(_,_,s),(0,i.VV)(_,_,u),(0,i.Fp)(x,x,s),(0,i.VV)(x,x,u)),l.push(_),l.push(x)}return n&&l.push(l.shift()),l}function o(t,e,n){var i=e.smooth,o=e.points;if(o&&o.length>=2){if(i){var a=r(o,i,n,e.smoothConstraint);t.moveTo(o[0][0],o[0][1]);for(var s=o.length,u=0;u<(n?s:s-1);u++){var l=a[2*u],h=a[2*u+1],c=o[(u+1)%s];t.bezierCurveTo(l[0],l[1],h[0],h[1],c[0],c[1])}}else{t.moveTo(o[0][0],o[0][1]);u=1;for(var f=o.length;u<f;u++)t.lineTo(o[u][0],o[u][1])}n&&t.closePath()}}},24111:function(t,e,n){n.d(e,{Pw:function(){return o},_3:function(){return r},vu:function(){return a}});var i=Math.round;function r(t,e,n){if(e){var r=e.x1,o=e.x2,s=e.y1,u=e.y2;t.x1=r,t.x2=o,t.y1=s,t.y2=u;var l=n&&n.lineWidth;return l?(i(2*r)===i(2*o)&&(t.x1=t.x2=a(r,l,!0)),i(2*s)===i(2*u)&&(t.y1=t.y2=a(s,l,!0)),t):t}}function o(t,e,n){if(e){var i=e.x,r=e.y,o=e.width,s=e.height;t.x=i,t.y=r,t.width=o,t.height=s;var u=n&&n.lineWidth;return u?(t.x=a(i,u,!0),t.y=a(r,u,!0),t.width=Math.max(a(i+o,u,!1)-t.x,0===o?0:1),t.height=Math.max(a(r+s,u,!1)-t.y,0===s?0:1),t):t}}function a(t,e,n){if(!e)return t;var r=i(2*t);return(r+i(e))%2===0?r/2:(r+(n?1:-1))/2}},14826:function(t,e,n){var i=n(70655),r=n(8846),o=function(){function t(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0}return t}(),a=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,u=Math.cos(o),l=Math.sin(o);t.moveTo(u*r+n,l*r+i),t.arc(n,i,r,o,a,!s)},e}(r.ZP);a.prototype.type="arc",e["Z"]=a},54174:function(t,e,n){var i=n(70655),r=n(8846),o=n(45280),a=n(29023),s=[],u=function(){function t(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return t}();function l(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?a.X_:a.af)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?a.X_:a.af)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?a.AZ:a.Zm)(t.x1,t.cpx1,t.x2,e),(n?a.AZ:a.Zm)(t.y1,t.cpy1,t.y2,e)]}var h=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new u},e.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,u=e.cpx1,l=e.cpy1,h=e.cpx2,c=e.cpy2,f=e.percent;0!==f&&(t.moveTo(n,i),null==h||null==c?(f<1&&((0,a.Lx)(n,u,r,f,s),u=s[1],r=s[2],(0,a.Lx)(i,l,o,f,s),l=s[1],o=s[2]),t.quadraticCurveTo(u,l,r,o)):(f<1&&((0,a.Vz)(n,u,h,r,f,s),u=s[1],h=s[2],r=s[3],(0,a.Vz)(i,l,c,o,f,s),l=s[1],c=s[2],o=s[3]),t.bezierCurveTo(u,l,h,c,r,o)))},e.prototype.pointAt=function(t){return l(this.shape,t,!1)},e.prototype.tangentAt=function(t){var e=l(this.shape,t,!0);return o.Fv(e,e)},e}(r.ZP);h.prototype.type="bezier-curve",e["Z"]=h},69538:function(t,e,n){var i=n(70655),r=n(8846),o=function(){function t(){this.cx=0,this.cy=0,this.r=0}return t}(),a=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},e}(r.ZP);a.prototype.type="circle",e["Z"]=a},92797:function(t,e,n){var i=n(70655),r=n(8846),o=function(){function t(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return t}(),a=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var n=.5522848,i=e.cx,r=e.cy,o=e.rx,a=e.ry,s=o*n,u=a*n;t.moveTo(i-o,r),t.bezierCurveTo(i-o,r-u,i-s,r-a,i,r-a),t.bezierCurveTo(i+s,r-a,i+o,r-u,i+o,r),t.bezierCurveTo(i+o,r+u,i+s,r+a,i,r+a),t.bezierCurveTo(i-s,r+a,i-o,r+u,i-o,r),t.closePath()},e}(r.ZP);a.prototype.type="ellipse",e["Z"]=a},22095:function(t,e,n){var i=n(70655),r=n(8846),o=n(24111),a={},s=function(){function t(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return t}(),u=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new s},e.prototype.buildPath=function(t,e){var n,i,r,s;if(this.subPixelOptimize){var u=(0,o._3)(a,e,this.style);n=u.x1,i=u.y1,r=u.x2,s=u.y2}else n=e.x1,i=e.y1,r=e.x2,s=e.y2;var l=e.percent;0!==l&&(t.moveTo(n,i),l<1&&(r=n*(1-l)+r*l,s=i*(1-l)+s*l),t.lineTo(r,s))},e.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},e}(r.ZP);u.prototype.type="line",e["Z"]=u},95094:function(t,e,n){var i=n(70655),r=n(8846),o=n(12525),a=function(){function t(){this.points=null,this.smooth=0,this.smoothConstraint=null}return t}(),s=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){o.L(t,e,!0)},e}(r.ZP);s.prototype.type="polygon",e["Z"]=s},62514:function(t,e,n){var i=n(70655),r=n(8846),o=n(12525),a=function(){function t(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return t}(),s=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){o.L(t,e,!1)},e}(r.ZP);s.prototype.type="polyline",e["Z"]=s},25293:function(t,e,n){n.d(e,{Z:function(){return h}});var i=n(70655),r=n(8846);function o(t,e){var n,i,r,o,a,s=e.x,u=e.y,l=e.width,h=e.height,c=e.r;l<0&&(s+=l,l=-l),h<0&&(u+=h,h=-h),"number"===typeof c?n=i=r=o=c:c instanceof Array?1===c.length?n=i=r=o=c[0]:2===c.length?(n=r=c[0],i=o=c[1]):3===c.length?(n=c[0],i=o=c[1],r=c[2]):(n=c[0],i=c[1],r=c[2],o=c[3]):n=i=r=o=0,n+i>l&&(a=n+i,n*=l/a,i*=l/a),r+o>l&&(a=r+o,r*=l/a,o*=l/a),i+r>h&&(a=i+r,i*=h/a,r*=h/a),n+o>h&&(a=n+o,n*=h/a,o*=h/a),t.moveTo(s+n,u),t.lineTo(s+l-i,u),0!==i&&t.arc(s+l-i,u+i,i,-Math.PI/2,0),t.lineTo(s+l,u+h-r),0!==r&&t.arc(s+l-r,u+h-r,r,0,Math.PI/2),t.lineTo(s+o,u+h),0!==o&&t.arc(s+o,u+h-o,o,Math.PI/2,Math.PI),t.lineTo(s,u+n),0!==n&&t.arc(s+n,u+n,n,Math.PI,1.5*Math.PI)}var a=n(24111),s=function(){function t(){this.x=0,this.y=0,this.width=0,this.height=0}return t}(),u={},l=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new s},e.prototype.buildPath=function(t,e){var n,i,r,s;if(this.subPixelOptimize){var l=(0,a.Pw)(u,e,this.style);n=l.x,i=l.y,r=l.width,s=l.height,l.r=e.r,e=l}else n=e.x,i=e.y,r=e.width,s=e.height;e.r?o(t,e):t.rect(n,i,r,s)},e.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},e}(r.ZP);l.prototype.type="rect";var h=l},85795:function(t,e,n){var i=n(70655),r=n(8846),o=function(){function t(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return t}(),a=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},e}(r.ZP);a.prototype.type="ring",e["Z"]=a},97782:function(t,e,n){n.d(e,{C:function(){return b}});var i=n(70655),r=n(8846),o=n(33051),a=Math.PI,s=2*a,u=Math.sin,l=Math.cos,h=Math.acos,c=Math.atan2,f=Math.abs,p=Math.sqrt,d=Math.max,v=Math.min,g=1e-4;function y(t,e,n,i,r,o,a,s){var u=n-t,l=i-e,h=a-r,c=s-o,f=c*u-h*l;if(!(f*f<g))return f=(h*(e-o)-c*(t-r))/f,[t+f*u,e+f*l]}function m(t,e,n,i,r,o,a){var s=t-n,u=e-i,l=(a?o:-o)/p(s*s+u*u),h=l*u,c=-l*s,f=t+h,v=e+c,g=n+h,y=i+c,m=(f+g)/2,_=(v+y)/2,x=g-f,w=y-v,S=x*x+w*w,b=r-o,T=f*y-g*v,M=(w<0?-1:1)*p(d(0,b*b*S-T*T)),k=(T*w-x*M)/S,D=(-T*x-w*M)/S,C=(T*w+x*M)/S,I=(-T*x+w*M)/S,P=k-m,A=D-_,O=C-m,Z=I-_;return P*P+A*A>O*O+Z*Z&&(k=C,D=I),{cx:k,cy:D,x0:-h,y0:-c,x1:k*(r/b-1),y1:D*(r/b-1)}}function _(t){var e;if((0,o.kJ)(t)){var n=t.length;if(!n)return t;e=1===n?[t[0],t[0],0,0]:2===n?[t[0],t[0],t[1],t[1]]:3===n?t.concat(t[2]):t}else e=[t,t,t,t];return e}function x(t,e){var n,i=d(e.r,0),r=d(e.r0||0,0),o=i>0,x=r>0;if(o||x){if(o||(i=r,r=0),r>i){var w=i;i=r,r=w}var S=e.startAngle,b=e.endAngle;if(!isNaN(S)&&!isNaN(b)){var T=e.cx,M=e.cy,k=!!e.clockwise,D=f(b-S),C=D>s&&D%s;if(C>g&&(D=C),i>g)if(D>s-g)t.moveTo(T+i*l(S),M+i*u(S)),t.arc(T,M,i,S,b,!k),r>g&&(t.moveTo(T+r*l(b),M+r*u(b)),t.arc(T,M,r,b,S,k));else{var I=void 0,P=void 0,A=void 0,O=void 0,Z=void 0,L=void 0,R=void 0,z=void 0,B=void 0,N=void 0,E=void 0,F=void 0,H=void 0,W=void 0,V=void 0,U=void 0,G=i*l(S),X=i*u(S),Y=r*l(b),q=r*u(b),K=D>g;if(K){var j=e.cornerRadius;j&&(n=_(j),I=n[0],P=n[1],A=n[2],O=n[3]);var J=f(i-r)/2;if(Z=v(J,A),L=v(J,O),R=v(J,I),z=v(J,P),E=B=d(Z,L),F=N=d(R,z),(B>g||N>g)&&(H=i*l(b),W=i*u(b),V=r*l(S),U=r*u(S),D<a)){var Q=y(G,X,V,U,H,W,Y,q);if(Q){var $=G-Q[0],tt=X-Q[1],et=H-Q[0],nt=W-Q[1],it=1/u(h(($*et+tt*nt)/(p($*$+tt*tt)*p(et*et+nt*nt)))/2),rt=p(Q[0]*Q[0]+Q[1]*Q[1]);E=v(B,(i-rt)/(it+1)),F=v(N,(r-rt)/(it-1))}}}if(K)if(E>g){var ot=v(A,E),at=v(O,E),st=m(V,U,G,X,i,ot,k),ut=m(H,W,Y,q,i,at,k);t.moveTo(T+st.cx+st.x0,M+st.cy+st.y0),E<B&&ot===at?t.arc(T+st.cx,M+st.cy,E,c(st.y0,st.x0),c(ut.y0,ut.x0),!k):(ot>0&&t.arc(T+st.cx,M+st.cy,ot,c(st.y0,st.x0),c(st.y1,st.x1),!k),t.arc(T,M,i,c(st.cy+st.y1,st.cx+st.x1),c(ut.cy+ut.y1,ut.cx+ut.x1),!k),at>0&&t.arc(T+ut.cx,M+ut.cy,at,c(ut.y1,ut.x1),c(ut.y0,ut.x0),!k))}else t.moveTo(T+G,M+X),t.arc(T,M,i,S,b,!k);else t.moveTo(T+G,M+X);if(r>g&&K)if(F>g){ot=v(I,F),at=v(P,F),st=m(Y,q,H,W,r,-at,k),ut=m(G,X,V,U,r,-ot,k);t.lineTo(T+st.cx+st.x0,M+st.cy+st.y0),F<N&&ot===at?t.arc(T+st.cx,M+st.cy,F,c(st.y0,st.x0),c(ut.y0,ut.x0),!k):(at>0&&t.arc(T+st.cx,M+st.cy,at,c(st.y0,st.x0),c(st.y1,st.x1),!k),t.arc(T,M,r,c(st.cy+st.y1,st.cx+st.x1),c(ut.cy+ut.y1,ut.cx+ut.x1),k),ot>0&&t.arc(T+ut.cx,M+ut.cy,ot,c(ut.y1,ut.x1),c(ut.y0,ut.x0),!k))}else t.lineTo(T+Y,M+q),t.arc(T,M,r,b,S,k);else t.lineTo(T+Y,M+q)}else t.moveTo(T,M);t.closePath()}}}var w=function(){function t(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0}return t}(),S=function(t){function e(e){return t.call(this,e)||this}return(0,i.ZT)(e,t),e.prototype.getDefaultShape=function(){return new w},e.prototype.buildPath=function(t,e){x(t,e)},e.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},e}(r.ZP);S.prototype.type="sector";var b=S},24839:function(t,e,n){n.d(e,{Cv:function(){return y},Gk:function(){return T},H3:function(){return S},I1:function(){return x},Pn:function(){return c},R:function(){return _},gA:function(){return M},gO:function(){return w},i2:function(){return v},jY:function(){return p},m1:function(){return b},mU:function(){return d},n1:function(){return g},oF:function(){return k},qV:function(){return f},ut:function(){return s},zT:function(){return l}});var i=n(33051),r=n(21092),o=n(66387),a=Math.round;function s(t){var e;if(t&&"transparent"!==t){if("string"===typeof t&&t.indexOf("rgba")>-1){var n=(0,r.Qc)(t);n&&(t="rgb("+n[0]+","+n[1]+","+n[2]+")",e=n[3])}}else t="none";return{color:t,opacity:null==e?1:e}}var u=1e-4;function l(t){return t<u&&t>-u}function h(t){return a(1e3*t)/1e3}function c(t){return a(1e4*t)/1e4}function f(t){return"matrix("+h(t[0])+","+h(t[1])+","+h(t[2])+","+h(t[3])+","+c(t[4])+","+c(t[5])+")"}var p={left:"start",right:"end",center:"middle",middle:"middle"};function d(t,e,n){return"top"===n?t+=e/2:"bottom"===n&&(t-=e/2),t}function v(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}function g(t){var e=t.style,n=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),n[0],n[1]].join(",")}function y(t){return t&&!!t.image}function m(t){return t&&!!t.svgElement}function _(t){return y(t)||m(t)}function x(t){return"linear"===t.type}function w(t){return"radial"===t.type}function S(t){return t&&("linear"===t.type||"radial"===t.type)}function b(t){return"url(#"+t+")"}function T(t){var e=t.getGlobalScale(),n=Math.max(e[0],e[1]);return Math.max(Math.ceil(Math.log(n)/Math.log(10)),1)}function M(t){var e=t.x||0,n=t.y||0,r=(t.rotation||0)*i.I3,o=(0,i.pD)(t.scaleX,1),s=(0,i.pD)(t.scaleY,1),u=t.skewX||0,l=t.skewY||0,h=[];return(e||n)&&h.push("translate("+e+"px,"+n+"px)"),r&&h.push("rotate("+r+")"),1===o&&1===s||h.push("scale("+o+","+s+")"),(u||l)&&h.push("skew("+a(u*i.I3)+"deg, "+a(l*i.I3)+"deg)"),h.join(" ")}var k=function(){return o.Z.hasGlobalWindow&&(0,i.mf)(window.btoa)?function(t){return window.btoa(unescape(t))}:"undefined"!==typeof Buffer?function(t){return Buffer.from(t).toString("base64")}:function(t){return null}}()},21092:function(t,e,n){n.d(e,{L0:function(){return k},Pz:function(){return M},Qc:function(){return y},Uu:function(){return w},m8:function(){return T},ox:function(){return b},t7:function(){return S},xb:function(){return x}});var i=n(92528),r={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function o(t){return t=Math.round(t),t<0?0:t>255?255:t}function a(t){return t=Math.round(t),t<0?0:t>360?360:t}function s(t){return t<0?0:t>1?1:t}function u(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?o(parseFloat(e)/100*255):o(parseInt(e,10))}function l(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?s(parseFloat(e)/100):s(parseFloat(e))}function h(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function c(t,e,n){return t+(e-t)*n}function f(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function p(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var d=new i.ZP(20),v=null;function g(t,e){v&&p(v,e),v=d.put(t,v||e.slice())}function y(t,e){if(t){e=e||[];var n=d.get(t);if(n)return p(e,n);t+="";var i=t.replace(/ /g,"").toLowerCase();if(i in r)return p(e,r[i]),g(t,e),e;var o=i.length;if("#"!==i.charAt(0)){var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var h=i.substr(0,a),c=i.substr(a+1,s-(a+1)).split(","),v=1;switch(h){case"rgba":if(4!==c.length)return 3===c.length?f(e,+c[0],+c[1],+c[2],1):f(e,0,0,0,1);v=l(c.pop());case"rgb":return 3!==c.length?void f(e,0,0,0,1):(f(e,u(c[0]),u(c[1]),u(c[2]),v),g(t,e),e);case"hsla":return 4!==c.length?void f(e,0,0,0,1):(c[3]=l(c[3]),m(c,e),g(t,e),e);case"hsl":return 3!==c.length?void f(e,0,0,0,1):(m(c,e),g(t,e),e);default:return}}f(e,0,0,0,1)}else{if(4===o||5===o){var y=parseInt(i.slice(1,4),16);return y>=0&&y<=4095?(f(e,(3840&y)>>4|(3840&y)>>8,240&y|(240&y)>>4,15&y|(15&y)<<4,5===o?parseInt(i.slice(4),16)/15:1),g(t,e),e):void f(e,0,0,0,1)}if(7===o||9===o){y=parseInt(i.slice(1,7),16);return y>=0&&y<=16777215?(f(e,(16711680&y)>>16,(65280&y)>>8,255&y,9===o?parseInt(i.slice(7),16)/255:1),g(t,e),e):void f(e,0,0,0,1)}}}}function m(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=l(t[1]),r=l(t[2]),a=r<=.5?r*(i+1):r+i-r*i,s=2*r-a;return e=e||[],f(e,o(255*h(s,a,n+1/3)),o(255*h(s,a,n)),o(255*h(s,a,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function _(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),u=s-a,l=(s+a)/2;if(0===u)e=0,n=0;else{n=l<.5?u/(s+a):u/(2-s-a);var h=((s-i)/6+u/2)/u,c=((s-r)/6+u/2)/u,f=((s-o)/6+u/2)/u;i===s?e=f-c:r===s?e=1/3+h-f:o===s&&(e=2/3+c-h),e<0&&(e+=1),e>1&&(e-=1)}var p=[360*e,n,l];return null!=t[3]&&p.push(t[3]),p}}function x(t,e){var n=y(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:n[i]<0&&(n[i]=0);return M(n,4===n.length?"rgba":"rgb")}}function w(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),a=Math.ceil(i),u=e[r],l=e[a],h=i-r;return n[0]=o(c(u[0],l[0],h)),n[1]=o(c(u[1],l[1],h)),n[2]=o(c(u[2],l[2],h)),n[3]=s(c(u[3],l[3],h)),n}}function S(t,e,n){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),r=Math.floor(i),a=Math.ceil(i),u=y(e[r]),l=y(e[a]),h=i-r,f=M([o(c(u[0],l[0],h)),o(c(u[1],l[1],h)),o(c(u[2],l[2],h)),s(c(u[3],l[3],h))],"rgba");return n?{color:f,leftIndex:r,rightIndex:a,value:i}:f}}function b(t,e,n,i){var r=y(t);if(t)return r=_(r),null!=e&&(r[0]=a(e)),null!=n&&(r[1]=l(n)),null!=i&&(r[2]=l(i)),M(m(r),"rgba")}function T(t,e){var n=y(t);if(n&&null!=e)return n[3]=s(e),M(n,"rgba")}function M(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}function k(t,e){var n=y(t);return n?(.299*n[0]+.587*n[1]+.114*n[2])*n[3]/255+(1-n[3])*e:0}},80073:function(t,e,n){n.d(e,{U5:function(){return P},iR:function(){return D},Pc:function(){return C},AA:function(){return I}});var i=n(70655),r=n(8846),o=n(14014),a=n(45280),s=o.Z.CMD,u=[[],[],[]],l=Math.sqrt,h=Math.atan2;function c(t,e){if(e){var n,i,r,o,c,f,p=t.data,d=t.len(),v=s.M,g=s.C,y=s.L,m=s.R,_=s.A,x=s.Q;for(r=0,o=0;r<d;){switch(n=p[r++],o=r,i=0,n){case v:i=1;break;case y:i=1;break;case g:i=3;break;case x:i=2;break;case _:var w=e[4],S=e[5],b=l(e[0]*e[0]+e[1]*e[1]),T=l(e[2]*e[2]+e[3]*e[3]),M=h(-e[1]/T,e[0]/b);p[r]*=b,p[r++]+=w,p[r]*=T,p[r++]+=S,p[r++]*=b,p[r++]*=T,p[r++]+=M,p[r++]+=M,r+=2,o=r;break;case m:f[0]=p[r++],f[1]=p[r++],(0,a.Ne)(f,f,e),p[o++]=f[0],p[o++]=f[1],f[0]+=p[r++],f[1]+=p[r++],(0,a.Ne)(f,f,e),p[o++]=f[0],p[o++]=f[1]}for(c=0;c<i;c++){var k=u[c];k[0]=p[r++],k[1]=p[r++],(0,a.Ne)(k,k,e),p[o++]=k[0],p[o++]=k[1]}}t.increaseVersion()}}var f=n(33051),p=Math.sqrt,d=Math.sin,v=Math.cos,g=Math.PI;function y(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function m(t,e){return(t[0]*e[0]+t[1]*e[1])/(y(t)*y(e))}function _(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(m(t,e))}function x(t,e,n,i,r,o,a,s,u,l,h){var c=u*(g/180),f=v(c)*(t-n)/2+d(c)*(e-i)/2,y=-1*d(c)*(t-n)/2+v(c)*(e-i)/2,x=f*f/(a*a)+y*y/(s*s);x>1&&(a*=p(x),s*=p(x));var w=(r===o?-1:1)*p((a*a*(s*s)-a*a*(y*y)-s*s*(f*f))/(a*a*(y*y)+s*s*(f*f)))||0,S=w*a*y/s,b=w*-s*f/a,T=(t+n)/2+v(c)*S-d(c)*b,M=(e+i)/2+d(c)*S+v(c)*b,k=_([1,0],[(f-S)/a,(y-b)/s]),D=[(f-S)/a,(y-b)/s],C=[(-1*f-S)/a,(-1*y-b)/s],I=_(D,C);if(m(D,C)<=-1&&(I=g),m(D,C)>=1&&(I=0),I<0){var P=Math.round(I/g*1e6)/1e6;I=2*g+P%2*g}h.addData(l,T,M,a,s,k,I,c,o)}var w=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,S=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function b(t){var e=new o.Z;if(!t)return e;var n,i=0,r=0,a=i,s=r,u=o.Z.CMD,l=t.match(w);if(!l)return e;for(var h=0;h<l.length;h++){for(var c=l[h],f=c.charAt(0),p=void 0,d=c.match(S)||[],v=d.length,g=0;g<v;g++)d[g]=parseFloat(d[g]);var y=0;while(y<v){var m=void 0,_=void 0,b=void 0,T=void 0,M=void 0,k=void 0,D=void 0,C=i,I=r,P=void 0,A=void 0;switch(f){case"l":i+=d[y++],r+=d[y++],p=u.L,e.addData(p,i,r);break;case"L":i=d[y++],r=d[y++],p=u.L,e.addData(p,i,r);break;case"m":i+=d[y++],r+=d[y++],p=u.M,e.addData(p,i,r),a=i,s=r,f="l";break;case"M":i=d[y++],r=d[y++],p=u.M,e.addData(p,i,r),a=i,s=r,f="L";break;case"h":i+=d[y++],p=u.L,e.addData(p,i,r);break;case"H":i=d[y++],p=u.L,e.addData(p,i,r);break;case"v":r+=d[y++],p=u.L,e.addData(p,i,r);break;case"V":r=d[y++],p=u.L,e.addData(p,i,r);break;case"C":p=u.C,e.addData(p,d[y++],d[y++],d[y++],d[y++],d[y++],d[y++]),i=d[y-2],r=d[y-1];break;case"c":p=u.C,e.addData(p,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r),i+=d[y-2],r+=d[y-1];break;case"S":m=i,_=r,P=e.len(),A=e.data,n===u.C&&(m+=i-A[P-4],_+=r-A[P-3]),p=u.C,C=d[y++],I=d[y++],i=d[y++],r=d[y++],e.addData(p,m,_,C,I,i,r);break;case"s":m=i,_=r,P=e.len(),A=e.data,n===u.C&&(m+=i-A[P-4],_+=r-A[P-3]),p=u.C,C=i+d[y++],I=r+d[y++],i+=d[y++],r+=d[y++],e.addData(p,m,_,C,I,i,r);break;case"Q":C=d[y++],I=d[y++],i=d[y++],r=d[y++],p=u.Q,e.addData(p,C,I,i,r);break;case"q":C=d[y++]+i,I=d[y++]+r,i+=d[y++],r+=d[y++],p=u.Q,e.addData(p,C,I,i,r);break;case"T":m=i,_=r,P=e.len(),A=e.data,n===u.Q&&(m+=i-A[P-4],_+=r-A[P-3]),i=d[y++],r=d[y++],p=u.Q,e.addData(p,m,_,i,r);break;case"t":m=i,_=r,P=e.len(),A=e.data,n===u.Q&&(m+=i-A[P-4],_+=r-A[P-3]),i+=d[y++],r+=d[y++],p=u.Q,e.addData(p,m,_,i,r);break;case"A":b=d[y++],T=d[y++],M=d[y++],k=d[y++],D=d[y++],C=i,I=r,i=d[y++],r=d[y++],p=u.A,x(C,I,i,r,k,D,b,T,M,p,e);break;case"a":b=d[y++],T=d[y++],M=d[y++],k=d[y++],D=d[y++],C=i,I=r,i+=d[y++],r+=d[y++],p=u.A,x(C,I,i,r,k,D,b,T,M,p,e);break}}"z"!==f&&"Z"!==f||(p=u.Z,e.addData(p),i=a,r=s),n=p}return e.toStatic(),e}var T=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,i.ZT)(e,t),e.prototype.applyTransform=function(t){},e}(r.ZP);function M(t){return null!=t.setData}function k(t,e){var n=b(t),i=(0,f.l7)({},e);return i.buildPath=function(t){if(M(t)){t.setData(n.data);var e=t.getContext();e&&t.rebuildPath(e,1)}else{e=t;n.rebuildPath(e,1)}},i.applyTransform=function(t){c(n,t),this.dirtyShape()},i}function D(t,e){return new T(k(t,e))}function C(t,e){var n=k(t,e),r=function(t){function e(e){var i=t.call(this,e)||this;return i.applyTransform=n.applyTransform,i.buildPath=n.buildPath,i}return(0,i.ZT)(e,t),e}(T);return r}function I(t,e){for(var n=[],i=t.length,o=0;o<i;o++){var a=t[o];n.push(a.getUpdatedPathProxy(!0))}var s=new r.ZP(e);return s.createPathProxy(),s.buildPath=function(t){if(M(t)){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e,1)}},s}function P(t,e){e=e||{};var n=new r.ZP;return t.shape&&n.setShape(t.shape),n.setStyle(t.style),e.bakeTransform?c(n.path,t.getComputedTransform()):e.toLocal?n.setLocalTransform(t.getComputedTransform()):n.copyTransform(t),n.buildPath=t.buildPath,n.applyTransform=n.applyTransform,n.z=t.z,n.z2=t.z2,n.zlevel=t.zlevel,n}},56641:function(t,e,n){n.d(e,{S1:function(){return ft},wm:function(){return pt}});var i=n(66387),r=n(33051),o=n(70655),a=n(45280),s=function(){function t(t,e){this.target=t,this.topTarget=e&&e.topTarget}return t}(),u=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){var e=t.target;while(e&&!e.draggable)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new s(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,r=n-this._x,o=i-this._y;this._x=n,this._y=i,e.drift(r,o,t),this.handler.dispatchToElement(new s(e,t),"drag",t.event);var a=this.handler.findHover(n,i,e).target,u=this._dropTarget;this._dropTarget=a,e!==a&&(u&&a!==u&&this.handler.dispatchToElement(new s(u,t),"dragleave",t.event),a&&a!==u&&this.handler.dispatchToElement(new s(a,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new s(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new s(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}(),l=u,h=n(23510),c=n(61158),f=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],u=c.eV(n,s,{});r.points.push([u.zrX,u.zrY]),r.touches.push(s)}this._track.push(r)}},t.prototype._recognize=function(t){for(var e in v)if(v.hasOwnProperty(e)){var n=v[e](this._track,t);if(n)return n}},t}();function p(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}function d(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}var v={pinch:function(t,e){var n=t.length;if(n){var i=(t[n-1]||{}).points,r=(t[n-2]||{}).points||i;if(r&&r.length>1&&i&&i.length>1){var o=p(i)/p(r);!isFinite(o)&&(o=1),e.pinchScale=o;var a=d(i);return e.pinchX=a[0],e.pinchY=a[1],{type:"pinch",target:t[0].target,event:e}}}}},g="silent";function y(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:m}}function m(){c.sT(this.event)}var _=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.handler=null,e}return(0,o.ZT)(e,t),e.prototype.dispose=function(){},e.prototype.setCursor=function(){},e}(h.Z),x=function(){function t(t,e){this.x=t,this.y=e}return t}(),w=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],S=function(t){function e(e,n,i,r){var o=t.call(this)||this;return o._hovered=new x(0,0),o.storage=e,o.painter=n,o.painterRoot=r,i=i||new _,o.proxy=null,o.setHandlerProxy(i),o._draggingMgr=new l(o),o}return(0,o.ZT)(e,t),e.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(r.S6(w,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},e.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=T(this,e,n),r=this._hovered,o=r.target;o&&!o.__zr&&(r=this.findHover(r.x,r.y),o=r.target);var a=this._hovered=i?new x(e,n):this.findHover(e,n),s=a.target,u=this.proxy;u.setCursor&&u.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},e.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},e.prototype.resize=function(){this._hovered=new x(0,0)},e.prototype.dispatch=function(t,e){var n=this[t];n&&n.call(this,e)},e.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},e.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},e.prototype.dispatchToElement=function(t,e,n){t=t||{};var i=t.target;if(!i||!i.silent){var r="on"+e,o=y(e,t,n);while(i)if(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget?i.__hostTarget:i.parent,o.cancelBubble)break;o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){"function"===typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)})))}},e.prototype.findHover=function(t,e,n){for(var i=this.storage.getDisplayList(),r=new x(t,e),o=i.length-1;o>=0;o--){var a=void 0;if(i[o]!==n&&!i[o].ignore&&(a=b(i[o],t,e))&&(!r.topTarget&&(r.topTarget=i[o]),a!==g)){r.target=i[o];break}}return r},e.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new f);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r;var o=new x;o.target=i.target,this.dispatchToElement(o,r,i.event)}},e}(h.Z);function b(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){var i=t,r=void 0,o=!1;while(i){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1;i.silent&&(r=!0)}var s=i.__hostTarget;i=s||i.parent}return!r||g}return!1}function T(t,e,n){var i=t.painter;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}r.S6(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){S.prototype[t]=function(e){var n,i,r=e.zrX,o=e.zrY,s=T(this,r,o);if("mouseup"===t&&s||(n=this.findHover(r,o),i=n.target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||a.TK(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}));var M=S,k=n(19455),D=n(14414),C=!1;function I(){C||(C=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function P(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var A=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=P}return t.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,(0,k.Z)(n,P)},t.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];var r=i,o=t;while(r)r.parent=o,r.updateTransform(),e.push(r),o=r,r=r.getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var u=a[s];t.__dirty&&(u.__dirty|=D.YV),this._updateAndAddDisplayable(u,e,n)}t.__dirty=0}else{var l=t;e&&e.length?l.__clipPaths=e:l.__clipPaths&&l.__clipPaths.length>0&&(l.__clipPaths=[]),isNaN(l.z)&&(I(),l.z=0),isNaN(l.z2)&&(I(),l.z2=0),isNaN(l.zlevel)&&(I(),l.zlevel=0),this._displayList[this._displayListLen++]=l}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,n);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,n);var f=t.getTextContent();f&&this._updateAndAddDisplayable(f,e,n)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=r.cq(this._roots,t);i>=0&&this._roots.splice(i,1)}},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}(),O=A,Z=n(22795),L=n(51376);function R(){return(new Date).getTime()}var z=function(t){function e(e){var n=t.call(this)||this;return n._running=!1,n._time=0,n._pausedTime=0,n._pauseStart=0,n._paused=!1,e=e||{},n.stage=e.stage||{},n}return(0,o.ZT)(e,t),e.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},e.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},e.prototype.removeClip=function(t){if(t.animation){var e=t.prev,n=t.next;e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},e.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},e.prototype.update=function(t){var e=R()-this._pausedTime,n=e-this._time,i=this._head;while(i){var r=i.next,o=i.step(e,n);o?(i.ondestroy(),this.removeClip(i),i=r):i=r}this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},e.prototype._startLoop=function(){var t=this;function e(){t._running&&((0,Z.Z)(e),!t._paused&&t.update())}this._running=!0,(0,Z.Z)(e)},e.prototype.start=function(){this._running||(this._time=R(),this._pausedTime=0,this._startLoop())},e.prototype.stop=function(){this._running=!1},e.prototype.pause=function(){this._paused||(this._pauseStart=R(),this._paused=!0)},e.prototype.resume=function(){this._paused&&(this._pausedTime+=R()-this._pauseStart,this._paused=!1)},e.prototype.clear=function(){var t=this._head;while(t){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},e.prototype.isFinished=function(){return null==this._head},e.prototype.animate=function(t,e){e=e||{},this.start();var n=new L.Z(t,e.loop);return this.addAnimator(n),n},e}(h.Z),B=z,N=300,E=i.Z.domSupported,F=function(){var t=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],e=["touchstart","touchend","touchmove"],n={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=r.UI(t,(function(t){var e=t.replace("mouse","pointer");return n.hasOwnProperty(e)?e:t}));return{mouse:t,touch:e,pointer:i}}(),H={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},W=!1;function V(t){var e=t.pointerType;return"pen"===e||"touch"===e}function U(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}function G(t){t&&(t.zrByTouch=!0)}function X(t,e){return(0,c.OD)(t.dom,new q(t,e),!0)}function Y(t,e){var n=e,i=!1;while(n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot))n=n.parentNode;return i}var q=function(){function t(t,e){this.stopPropagation=r.ZT,this.stopImmediatePropagation=r.ZT,this.preventDefault=r.ZT,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return t}(),K={mousedown:function(t){t=(0,c.OD)(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=(0,c.OD)(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=(0,c.OD)(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){t=(0,c.OD)(this.dom,t);var e=t.toElement||t.relatedTarget;Y(this,e)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){W=!0,t=(0,c.OD)(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){W||(t=(0,c.OD)(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){t=(0,c.OD)(this.dom,t),G(t),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),K.mousemove.call(this,t),K.mousedown.call(this,t)},touchmove:function(t){t=(0,c.OD)(this.dom,t),G(t),this.handler.processGesture(t,"change"),K.mousemove.call(this,t)},touchend:function(t){t=(0,c.OD)(this.dom,t),G(t),this.handler.processGesture(t,"end"),K.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<N&&K.click.call(this,t)},pointerdown:function(t){K.mousedown.call(this,t)},pointermove:function(t){V(t)||K.mousemove.call(this,t)},pointerup:function(t){K.mouseup.call(this,t)},pointerout:function(t){V(t)||K.mouseout.call(this,t)}};r.S6(["click","dblclick","contextmenu"],(function(t){K[t]=function(e){e=(0,c.OD)(this.dom,e),this.trigger(t,e)}}));var j={pointermove:function(t){V(t)||j.mousemove.call(this,t)},pointerup:function(t){j.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function J(t,e){var n=e.domHandlers;i.Z.pointerEventsSupported?r.S6(F.pointer,(function(i){$(e,i,(function(e){n[i].call(t,e)}))})):(i.Z.touchEventsSupported&&r.S6(F.touch,(function(i){$(e,i,(function(r){n[i].call(t,r),U(e)}))})),r.S6(F.mouse,(function(i){$(e,i,(function(r){r=(0,c.iP)(r),e.touching||n[i].call(t,r)}))})))}function Q(t,e){function n(n){function i(i){i=(0,c.iP)(i),Y(t,i.target)||(i=X(t,i),e.domHandlers[n].call(t,i))}$(e,n,i,{capture:!0})}i.Z.pointerEventsSupported?r.S6(H.pointer,n):i.Z.touchEventsSupported||r.S6(H.mouse,n)}function $(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,(0,c.Oo)(t.domTarget,e,n,i)}function tt(t){var e=t.mounted;for(var n in e)e.hasOwnProperty(n)&&(0,c.xg)(t.domTarget,n,e[n],t.listenerOpts[n]);t.mounted={}}var et=function(){function t(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return t}(),nt=function(t){function e(e,n){var i=t.call(this)||this;return i.__pointerCapturing=!1,i.dom=e,i.painterRoot=n,i._localHandlerScope=new et(e,K),E&&(i._globalHandlerScope=new et(document,j)),J(i,i._localHandlerScope),i}return(0,o.ZT)(e,t),e.prototype.dispose=function(){tt(this._localHandlerScope),E&&tt(this._globalHandlerScope)},e.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},e.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,E&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?Q(this,e):tt(e)}},e}(h.Z),it=nt,rt=n(21092),ot=n(4990),at=n(38154),st={},ut={};function lt(t){delete ut[t]}function ht(t){if(!t)return!1;if("string"===typeof t)return(0,rt.L0)(t,1)<ot.Ak;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=(0,rt.L0)(e[r].color,1);return n/=i,n<ot.Ak}return!1}var ct=function(){function t(t,e,n){var o=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t;var a=new O,s=n.renderer||"canvas";st[s]||(s=r.XP(st)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect;var u=new st[s](e,a,n,t),l=n.ssr||u.ssrOnly;this.storage=a,this.painter=u;var h=i.Z.node||i.Z.worker||l?null:new it(u.getViewportRoot(),u.root);this.handler=new M(a,u,h,u.root),this.animation=new B({stage:{update:l?null:function(){return o._flush(!0)}}}),l||this.animation.start()}return t.prototype.add=function(t){t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh()},t.prototype.setBackgroundColor=function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=ht(t)},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},t.prototype.refresh=function(){this._needsRefresh=!0,this.animation.start()},t.prototype.flush=function(){this._flush(!1)},t.prototype._flush=function(t){var e,n=R();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=R();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-n})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this.animation.start(),this._stillFrameAccum=0},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover()},t.prototype.resize=function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},t.prototype.clearAnimation=function(){this.animation.clear()},t.prototype.getWidth=function(){return this.painter.getWidth()},t.prototype.getHeight=function(){return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){return this.handler.findHover(t,e)},t.prototype.on=function(t,e,n){return this.handler.on(t,e,n),this},t.prototype.off=function(t,e){this.handler.off(t,e)},t.prototype.trigger=function(t,e){this.handler.trigger(t,e)},t.prototype.clear=function(){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof at.Z&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()},t.prototype.dispose=function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,lt(this.id)},t}();function ft(t,e){var n=new ct(r.M8(),t,e);return ut[n.id]=n,n}function pt(t,e){st[t]=e}}}]);