"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9793],{59793:function(e,l,t){t.r(l),t.d(l,{columns:function(){return a},searchFormConfig:function(){return o}});const a=[{width:45,type:"selection",columnKey:"expand",align:"center",slot:"expand"},{prop:"orderId",label:"订单号",align:"center",showOverflowTooltip:!0},{prop:"customerName",label:"客户姓名",align:"center",showOverflowTooltip:!0},{prop:"channelName",label:"渠道名称",align:"center",showOverflowTooltip:!0},{prop:"goodsName",label:"商品名称",align:"center",showOverflowTooltip:!0},{prop:"createdDate",label:"创建时间",align:"center",showOverflowTooltip:!0},{prop:"status",label:"订单状态",align:"center",showOverflowTooltip:!0,formatter:e=>1==e.orderStatus?"激活成功":2==e.orderStatus?"支付成功":3==e.orderStatus?"待支付":4==e.orderStatus?"订单超时":5==e.orderStatus?"取消订单":6==e.orderStatus?"受理失败":7==e.orderStatus?"已退款":void 0},{prop:"schoolChannelType",label:"渠道类型",align:"center",showOverflowTooltip:!0,formatter:e=>1==e.schoolChannelType?"全渠道":2==e.schoolChannelType?"电渠互联网卡渠道":3==e.schoolChannelType?"分公司渠道运营渠道":"其他"},{columnKey:"action",label:"操作",width:220,align:"center",resizable:!1,slot:"action",showOverflowTooltip:!0}],o={labelWidth:"100px",itemStyle:{padding:"10px"},colLayout:{span:4},formItems:[{field:"saflType",type:"select",label:"订单类型",placeholder:"请选择订单类型",options:[{title:"全部",value:""},{title:"号卡新装",value:1}]},{field:"orderStatus",type:"select",label:"状态",placeholder:"请选择",options:[{title:"全部",value:""},{title:"激活成功",value:1},{title:"支付成功",value:2},{title:"待支付",value:3},{title:"订单超时",value:4},{title:"取消订单",value:5},{title:"受理失败",value:8},{title:"已退款",value:9}]},{field:"customerName",type:"input",label:"身份证|姓名|预占号码",placeholder:"身份证|姓名|预占号码"},{field:"schoolName",type:"input",label:"渠道",placeholder:"请输入渠道名称"},{field:"createdDateList",type:"twoDatePicker",label:"创建时间",otherOptions:{placeholder:"请选择创建时间",type:"datetimerange","value-format":"yyyy-MM-dd HH:mm"}},{field:"orderId",type:"input",label:"订单号",placeholder:"请输入订单号"},{field:"bpsOrderId",type:"input",label:"BPS订单号",placeholder:"请输入订单号"},{field:"schoolChannelType",type:"select",label:"渠道类型",placeholder:"请选择渠道类型",options:[{title:"全部",value:""},{title:"全渠道",value:1},{title:"分公司渠道运营渠道",value:2},{title:"电渠互联网卡渠道",value:3},{title:"其他",value:4}]},{field:"citycode",type:"select",label:"地市",placeholder:"请选择地市",options:[{value:"",title:"全省"},{value:"731",title:"长沙市"},{value:"733",title:"株洲市"},{value:"732",title:"湘潭市"},{value:"734",title:"衡阳市"},{value:"739",title:"邵阳市"},{value:"730",title:"岳阳市"},{value:"736",title:"常德市"},{value:"744",title:"张家界市"},{value:"737",title:"益阳市"},{value:"735",title:"郴州市"},{value:"746",title:"永州市"},{value:"745",title:"怀化市"},{value:"738",title:"娄底市"},{value:"743",title:"湘西市"}]},{field:"goodsName",type:"input",label:"商品名称",placeholder:"请输入商品名称"},{field:"crmOrderId",type:"input",label:"CRM订单号",placeholder:"请输入CRM订单号"}]}}}]);