"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9612,8042,322],{74952:function(e,t,o){o.d(t,{Z:function(){return h}});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-search"},[t("hl-form",e._b({scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"handle-btns"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery<PERSON>lick}},[e._v("搜索")]),t("el-button",{on:{click:e.handleResetClick}},[e._v("重置")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:"formData"}},"hl-form",e.searchFormConfig,!1))],1)},l=[],s=(o(33948),function(){var e=this,t=e._self._c;return t("div",{staticClass:"hl-form"},[t("div",{staticClass:"header"},[e._t("header")],2),t("el-form",{attrs:{"label-width":e.labelWidth,model:e.formData,rules:e.rules}},[t("el-row",[e._l(e.formItems,(function(o){return[t("el-col",e._b({key:o.label},"el-col",e.colLayout,!1),[o.isHidden?e._e():t("el-form-item",{style:e.itemStyle,attrs:{label:o.label,rules:o.rules}},["input"===o.type||"password"===o.type?[t("el-input",e._b({attrs:{placeholder:o.placeholder,"show-password":"password"===o.type},model:{value:e.formData[`${o.field}`],callback:function(t){e.$set(e.formData,`${o.field}`,t)},expression:"formData[`${item.field}`]"}},"el-input",o.otherOptions,!1))]:"select"===o.type?[t("el-select",e._b({staticStyle:{width:"100%"},attrs:{placeholder:o.placeholder},model:{value:e.formData[`${o.field}`],callback:function(t){e.$set(e.formData,`${o.field}`,t)},expression:"formData[`${item.field}`]"}},"el-select",o.otherOptions,!1),e._l(o.options,(function(a){var l,s,r,i;return t("el-option",{key:null!==(l=a[o.optionValue])&&void 0!==l?l:a.value,attrs:{value:null!==(s=a[o.optionValue])&&void 0!==s?s:a.value,label:null!==(r=a[o.optionLabel])&&void 0!==r?r:a.title}},[e._v(e._s(null!==(i=a[o.optionLabel])&&void 0!==i?i:a.title))])})),1)]:"datepicker"===o.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},model:{value:e.formData[`${o.field}`],callback:function(t){e.$set(e.formData,`${o.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",o.otherOptions,!1))]:"twoDatePicker"===o.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.formData[`${o.field}`],callback:function(t){e.$set(e.formData,`${o.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",o.otherOptions,!1))]:"section"===o.type?[t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:o.placeholder},model:{value:e.formData[`${o.field[0]}`],callback:function(t){e.$set(e.formData,`${o.field[0]}`,t)},expression:"formData[`${item.field[0]}`]"}},"el-input",o.otherOptions,!1))],1),t("el-col",{staticClass:"line",attrs:{span:2}},[e._v("-")]),t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:o.placeholder},model:{value:e.formData[`${o.field[1]}`],callback:function(t){e.$set(e.formData,`${o.field[1]}`,t)},expression:"formData[`${item.field[1]}`]"}},"el-input",o.otherOptions,!1))],1)]:e._e()],2)],1)]})),e._t("footer")],2)],1)],1)}),r=[],i={props:{value:{type:Object,required:!0},formItems:{type:Array,default:()=>[]},rules:{type:Object,default:()=>{}},labelWidth:{type:String,default:"100px"},itemStyle:{type:Object,default:()=>({padding:"10px 40px"})},colLayout:{type:Object,default:()=>({xl:6,lg:8,md:12,sm:24,xs:24})}},data(){return{formData:{...this.value},pickerOptions:{shortcuts:[{text:"最近一周",onClick(e){const t=new Date,o=new Date;o.setTime(o.getTime()-6048e5),e.$emit("pick",[o,t])}},{text:"最近一个月",onClick(e){const t=new Date,o=new Date;o.setTime(o.getTime()-2592e6),e.$emit("pick",[o,t])}},{text:"最近三个月",onClick(e){const t=new Date,o=new Date;o.setTime(o.getTime()-7776e6),e.$emit("pick",[o,t])}}]}}},watch:{formData:{handler(e){this.$emit("input",e)},deep:!0}}},n=i,c=o(1001),d=(0,c.Z)(n,s,r,!1,null,"3585fc65",null),u=d.exports,p={components:{HlForm:u},props:{searchFormConfig:{type:Object,reuqired:!0}},data(){return{formOriginData:{},formData:{}}},created(){var e,t;const o=null!==(e=null===(t=this.searchFormConfig)||void 0===t?void 0:t.formItems)&&void 0!==e?e:[];for(const a of o)Array.isArray(a.field)?a.field.forEach((e=>{this.formOriginData[e]=""})):this.formOriginData[a.field]="";this.formData=this.formOriginData},methods:{handleResetClick(){for(const e in this.formOriginData)this.formData[`${e}`]=this.formOriginData[e];this.$emit("resetBtnClick")},handleQueryClick(){const e={...this.formData};for(const t in e)""===e[t]&&delete e[t];this.$emit("queryBtnClick",e)}}},f=p,m=(0,c.Z)(f,a,l,!1,null,"a0a65568",null),h=m.exports},78042:function(e,t,o){o.r(t),o.d(t,{default:function(){return u}});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"add-terminal"},[t("el-form",{ref:"ruleFormRef",staticClass:"el-form",attrs:{"label-position":"right",rules:e.rules,"label-width":"140px",model:e.formOptions}},[t("el-form-item",{attrs:{label:"号池编码",prop:"cardPoolNumber"}},[t("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入号池编码"},model:{value:e.formOptions.cardPoolNumber,callback:function(t){e.$set(e.formOptions,"cardPoolNumber",t)},expression:"formOptions.cardPoolNumber"}})],1),t("el-form-item",{attrs:{label:"号池名称",prop:"cardPoolName"}},[t("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入号池名称"},model:{value:e.formOptions.cardPoolName,callback:function(t){e.$set(e.formOptions,"cardPoolName",t)},expression:"formOptions.cardPoolName"}})],1),t("el-form-item",{attrs:{label:"地市",prop:"cityCode"}},[t("el-select",{attrs:{placeholder:"请选择地市"},on:{change:e.changeAddress},model:{value:e.formOptions.cityCode,callback:function(t){e.$set(e.formOptions,"cityCode",t)},expression:"formOptions.cityCode"}},e._l(e.addressInfo,(function(e){return t("el-option",{key:e.value,attrs:{label:e.title,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-select",{attrs:{clearable:"",placeholder:"状态"},model:{value:e.formOptions.status,callback:function(t){e.$set(e.formOptions,"status",t)},expression:"formOptions.status"}},e._l(["无效","有效"],(function(e,o){return t("el-option",{key:e,attrs:{label:e,value:o}})})),1)],1),t("el-form-item",{attrs:{label:"所属学校",prop:"schoolCode"}},[t("el-select",{attrs:{multiple:"",loading:e.isLoading,placeholder:"请输入所属学校"},model:{value:e.formOptions.schoolCode,callback:function(t){e.$set(e.formOptions,"schoolCode",t)},expression:"formOptions.schoolCode"}},e._l(e.options,(function(e){return t("el-option",{key:e.schoolCode,attrs:{label:e.schoolName,value:e.schoolCode}})})),1)],1),t("el-form-item",{attrs:{label:"受理类型",prop:"cardPoolType"}},[t("el-select",{attrs:{clearable:"",placeholder:"受理类型"},model:{value:e.formOptions.cardPoolType,callback:function(t){e.$set(e.formOptions,"cardPoolType",t)},expression:"formOptions.cardPoolType"}},[t("el-option",{attrs:{label:"省内3.0",value:"1"}}),t("el-option",{attrs:{label:"集团号池",value:"2"}})],1)],1),t("el-form-item",{attrs:{label:"渠道ID",prop:"channelId"}},[t("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入渠道ID"},model:{value:e.formOptions.channelId,callback:function(t){e.$set(e.formOptions,"channelId",t)},expression:"formOptions.channelId"}})],1),t("el-form-item",{attrs:{label:"MD5加密值",prop:"md5Key"}},[t("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入MD5加密值"},model:{value:e.formOptions.md5Key,callback:function(t){e.$set(e.formOptions,"md5Key",t)},expression:"formOptions.md5Key"}})],1)],1),t("div",{staticClass:"btn-submit"},[e._t("default")],2)],1)},l=[],s=o(29557),r=o(10322),i={components:{},props:{addModuleInfo:{type:Object,default:()=>{}}},data(){return{rules:r.rules,addressInfo:r.addressInfo,options:[],isLoading:!0,formOptions:{cardPoolNumber:"",cardPoolName:"",cityCode:"",status:"",schoolCode:[]}}},watch:{addModuleInfo(e){this.initialize(e)}},created(){this.initialize(this.addModuleInfo),this.getSchoolInfo()},methods:{changeAddress(e){this.options=[],this.formOptions.schoolCode=[],this.getSchoolInfo(e)},getSchoolInfo(e){this.isLoading=!0,(0,s.K$)({limit:500,schoolCode:e}).then((e=>{this.options=e.list,this.isLoading=!1}))},initialize(e){Object.keys(e).length?(this.formOptions={...e},this.formOptions.schoolCode=[e.schoolCode]):this.formOptions=this.$options.data().formOptions},handleSubmit(){let e=!1;return this.$refs["ruleFormRef"].validate((t=>{e=!!t})),{isSubmit:e,formOptions:this.formOptions}}}},n=i,c=o(1001),d=(0,c.Z)(n,a,l,!1,null,"bfbc8066",null),u=d.exports},59612:function(e,t,o){o.r(t),o.d(t,{default:function(){return g}});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("el-card",{attrs:{shadow:"never"}},[t("pageSearch",{attrs:{searchFormConfig:e.searchFormConfig},on:{queryBtnClick:e.reload}}),t("ele-pro-table",{key:e.itemKey,ref:"table",attrs:{columns:e.columns,datasource:e.datasource,"cache-key":"playeCityModuleTable"},scopedSlots:e._u([{key:"toolbar",fn:function(){return[t("el-button",{staticClass:"ele-btn-icon",attrs:{size:"small",type:"primary",icon:"el-icon-plus"},on:{click:function(t){e.addModuleInfo={},e.isShowNewConfig=!0}}},[e._v(" 新建 ")])]},proxy:!0},{key:"status",fn:function({row:o}){return[t("el-tag",{attrs:{type:1===o.status?"success":"danger"}},[e._v(e._s(1===o.status?"有效":"无效"))])]}},{key:"action",fn:function({row:o}){return[t("el-link",{attrs:{type:"primary",underline:!1,icon:"el-icon-plus"},on:{click:function(t){return e.handleModify(o)}}},[e._v(" 修改 ")]),t("el-popconfirm",{staticClass:"ele-action",attrs:{title:"确定要删除吗？"},on:{confirm:function(t){return e.remove(o.id)}},scopedSlots:e._u([{key:"reference",fn:function(){return[t("el-link",{attrs:{type:"danger",underline:!1,icon:"el-icon-delete"}},[e._v(" 删除 ")])]},proxy:!0}],null,!0)})]}}])})],1),t("ele-modal",{attrs:{width:"820px",title:(Object.keys(e.addModuleInfo).length?"修改":"新增")+"号池列表",visible:e.isShowNewConfig},on:{"update:visible":function(t){e.isShowNewConfig=t}}},[t("addTank",{ref:"cardRef",attrs:{addModuleInfo:e.addModuleInfo}},[t("el-button",{staticClass:"btn-add back submit",attrs:{type:"primary"},on:{click:function(t){e.isShowNewConfig=!1}}},[e._v("返回")]),t("el-button",{staticClass:"btn-add submit",attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)},l=[],s=(o(21703),o(18684));async function r(e){const t=await s.Z.post("/hnzhsl/hnslUserCardpool/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function i(e){const t=await s.Z.post("/hnzhsl/hnslUserCardpool/decode/saveBatch",e,{showLoading:!0});return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function n(e){const t=await s.Z.post("/hnzhsl/hnslUserCardpool/removeBatch",e,{showLoading:!0});return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function c(e){const t=await s.Z.post("/hnzhsl/hnslUserCardpool/decode/updateBatch",e,{showLoading:!0});return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}var d=o(10322),u=o(74952),p=o(78042),f={components:{pageSearch:u.Z,addTank:p["default"]},data(){return{columns:d.columns,searchFormConfig:d.searchFormConfig,isShowNewConfig:!1,cardRef:null,itemKey:null,addModuleInfo:{}}},methods:{handleModify(e){this.addModuleInfo={...e},this.isShowNewConfig=!0},remove(e){const t=this.$loading({lock:!0});n([e]).then((e=>{t.close(),this.$message.success(e),this.reload()})).catch((e=>{t.close(),this.$message.error(e.message)}))},handleSubmit(){const e=this.$loading({lock:!0}),{isSubmit:t,formOptions:o}=this.$refs.cardRef.handleSubmit();if(!t)return;const a={...o};delete a.schoolCode;const l=o.schoolCode.map((e=>({...a,schoolCode:e}))),s=o.schoolCode.map((e=>({...a,schoolCode:e,id:this.addModuleInfo.id})));Object.keys(this.addModuleInfo).length?c(s).then((t=>{e.close(),this.$message.success(t),this.isShowNewConfig=!1,this.reload()})).catch((t=>{e.close(),this.$message.error(t.message)})):i(l).then((t=>{e.close(),this.$message.success(t),this.isShowNewConfig=!1,this.reload()})).catch((t=>{e.close(),this.$message.error(t.message)}))},datasource({page:e,limit:t,where:o,order:a}){return r({page:e,limit:t,...o,...a})},reload(e){this.$refs.table.reload({page:1,where:e})}}},m=f,h=o(1001),b=(0,h.Z)(m,a,l,!1,null,"02e69c82",null),g=b.exports},29557:function(e,t,o){o.d(t,{K$:function(){return l},T3:function(){return s},vs:function(){return r},yO:function(){return i}});o(21703);var a=o(18684);async function l(e){const t=await a.Z.post("/hnzhsl/hnslSchool/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function s(e){const t=await a.Z.post("/hnzhsl/hnslSchool/list",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await a.Z.post("/hnzhsl/hnslSchool/save",e,{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function i(e){const t=await a.Z.post("/hnzhsl/hnslSchool/update",e,{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}},10322:function(e,t,o){o.r(t),o.d(t,{addressInfo:function(){return r},columns:function(){return a},rules:function(){return s},searchFormConfig:function(){return l}});const a=[{width:45,type:"index",columnKey:"expand",align:"center",slot:"expand"},{prop:"cardPoolNumber",label:"号池编码",align:"center",showOverflowTooltip:!0},{prop:"cardPoolName",label:"号池名称",align:"center",showOverflowTooltip:!0},{slot:"cityCode",prop:"cityCode",label:"地市",align:"center",showOverflowTooltip:!0},{slot:"status",prop:"status",label:"状态",align:"center",showOverflowTooltip:!0},{slot:"schoolCode",prop:"schoolCode",label:"学校名称",align:"center",showOverflowTooltip:!0},{columnKey:"action",label:"操作",width:220,align:"center",resizable:!1,slot:"action",showOverflowTooltip:!0}],l={labelWidth:"100px",itemStyle:{padding:"10px"},colLayout:{span:6},formItems:[{field:"cardPoolNumber",type:"input",label:"号池编码",placeholder:"请输入号池编码"},{field:"cardPoolName",type:"input",label:"号池名称",placeholder:"请输入号池名称"},{field:"schoolName",type:"input",label:"学校",placeholder:"请输入学校"},{field:"status",type:"select",label:"状态",placeholder:"请选择状态",options:[{title:"无效",value:0},{title:"有效",value:1}]}]},s={cardPoolNumber:[{required:!0,message:"请输入号池编码",trigger:"blur"}],cardPoolName:[{required:!0,message:"请输入号池名称",trigger:"blur"}],cityCode:[{required:!0,message:"请选择地市",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],schoolCode:[{required:!0,message:"请选择学校",trigger:"change"}]},r=[{value:"731",title:"长沙市"},{value:"733",title:"株洲市"},{value:"732",title:"湘潭市"},{value:"734",title:"衡阳市"},{value:"739",title:"邵阳市"},{value:"730",title:"岳阳市"},{value:"736",title:"常德市"},{value:"744",title:"张家界市"},{value:"737",title:"益阳市"},{value:"735",title:"郴州市"},{value:"746",title:"永州市"},{value:"745",title:"怀化市"},{value:"738",title:"娄底市"},{value:"743",title:"湘西市"},{value:"700",title:"全省"}]}}]);