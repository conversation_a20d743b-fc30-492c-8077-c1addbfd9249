"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9815],{19815:function(t,s,a){a.r(s),a.d(s,{default:function(){return r}});var e=function(){var t=this,s=t._self._c;return s("div",{staticClass:"order-process-log-detail"},[s("el-skeleton",{attrs:{loading:t.loading,animated:""}},[s("template",{slot:"template"},[s("div",{staticClass:"skeleton-block"},[s("el-skeleton-item",{staticStyle:{width:"30%"},attrs:{variant:"text"}}),s("el-skeleton-item",{staticStyle:{width:"50%"},attrs:{variant:"text"}}),s("el-skeleton-item",{staticStyle:{width:"100%"},attrs:{variant:"text"}}),s("el-skeleton-item",{staticStyle:{width:"100%"},attrs:{variant:"text"}}),s("el-skeleton-item",{staticStyle:{width:"100%"},attrs:{variant:"text"}})],1)]),[s("div",{staticClass:"detail-section"},[s("h3",{staticClass:"section-title"},[t._v("基本信息")]),s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("ID：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.id||"-"))])])]),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("订单号：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.orderNo||"-"))])])]),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("暂存单ID：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.sceneInstId||"-"))])])])],1),s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("87单号：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.custOrderId||"-"))])])]),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("流程节点：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.processNode||"-"))])])]),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("状态：")]),s("span",{staticClass:"item-value"},[s("el-tag",{attrs:{type:0===t.logDetail.status?"success":"danger"}},[t._v(" "+t._s(0===t.logDetail.status?"成功":"失败")+" ")])],1)])])],1),s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("创建时间：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.formatDate(t.logDetail.createTime)))])])])],1)],1),s("div",{staticClass:"detail-section"},[s("h3",{staticClass:"section-title"},[t._v("执行信息")]),s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("执行类名：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.className||"-"))])])]),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("执行方法名：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.methodName||"-"))])])]),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("执行时间(ms)：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.executionTime||"-"))])])])],1)],1),s("div",{staticClass:"detail-section"},[s("h3",{staticClass:"section-title"},[t._v("操作人信息")]),s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("操作人ID：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.operatorId||"-"))])])]),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("操作人工号：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.operatorStaffId||"-"))])])]),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"item-label"},[t._v("操作人姓名：")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.logDetail.operatorName||"-"))])])])],1)],1),s("div",{staticClass:"detail-section"},[s("h3",{staticClass:"section-title"},[t._v("请求参数")]),s("div",{staticClass:"detail-code"},[s("pre",[t._v(t._s(t.formatJson(t.logDetail.requestParams)))])])]),s("div",{staticClass:"detail-section"},[s("h3",{staticClass:"section-title"},[t._v("Http接口参数")]),s("div",{staticClass:"detail-code"},[s("pre",[t._v(t._s(t.formatJson(t.logDetail.interfaceRequestParams)))])])]),s("div",{staticClass:"detail-section"},[s("h3",{staticClass:"section-title"},[t._v("响应结果")]),s("div",{staticClass:"detail-code"},[s("pre",[t._v(t._s(t.formatJson(t.logDetail.responseResult)))])])]),1===t.logDetail.status?s("div",{staticClass:"detail-section"},[s("h3",{staticClass:"section-title"},[t._v("错误信息")]),s("div",{staticClass:"detail-code error"},[s("pre",[t._v(t._s(t.logDetail.errorMsg||"无错误信息"))])])]):t._e()]],2)],1)},l=[],i=(a(38862),{name:"OrderProcessLogDetail",props:{logDetail:{type:Object,default:()=>({})},loading:{type:Boolean,default:!1}},methods:{formatDate(t){return t||"-"},formatJson(t){if(!t)return"{}";try{const s="string"===typeof t?JSON.parse(t):t;return JSON.stringify(s,null,2)}catch(s){return t||"{}"}}}}),c=i,o=a(1001),n=(0,o.Z)(c,e,l,!1,null,"5370946e",null),r=n.exports}}]);