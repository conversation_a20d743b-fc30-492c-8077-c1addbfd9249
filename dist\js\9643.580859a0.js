"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9643,7411,8158],{37411:function(e,t,a){a.r(t),a.d(t,{default:function(){return c}});a(74916);var s=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{position:"absolute",right:"8px","margin-top":"-10px",top:"50%"},on:{click:function(e){e.stopPropagation()}}},[t("el-popover",{attrs:{width:200,trigger:"click",placement:"bottom",transition:"el-zoom-in-top"},scopedSlots:e._u([{key:"reference",fn:function(){return[t("i",{staticClass:"el-icon-_filter ele-text-secondary",staticStyle:{"font-weight":"bold"}})]},proxy:!0}]),model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[t("div",[t("div",[t("el-input",{attrs:{size:"mini",placeholder:"请输入关键字"},model:{value:e.nickname,callback:function(t){e.nickname=t},expression:"nickname"}})],1),t("div",{staticStyle:{"margin-top":"10px"}},[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.search}},[e._v(" 搜索 ")]),t("el-button",{attrs:{size:"mini"},on:{click:e.reset}},[e._v("重置")])],1)])])],1)},i=[],l={data(){return{visible:!1,nickname:""}},methods:{search(){this.visible=!1,this.$emit("search",this.nickname)},reset(){this.nickname?(this.nickname="",this.search()):this.visible=!1}}},n=l,r=a(1001),o=(0,r.Z)(n,s,i,!1,null,null,null),c=o.exports},68158:function(e,t,a){a.r(t),a.d(t,{default:function(){return c}});a(74916);var s=function(){var e=this,t=e._self._c;return t("el-card",{attrs:{shadow:"never","body-style":"padding: 22px 22px 7px 22px;"}},[t("el-form",{staticClass:"ele-form-search",attrs:{"label-width":"80px"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)},submit:function(e){e.preventDefault()}}},[t("el-row",{attrs:{gutter:15}},[t("el-col",e._b({},"el-col",e.styleResponsive?{lg:8,md:12}:{span:8},!1),[t("el-form-item",{attrs:{label:"用户账号:"}},[t("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.where.username,callback:function(t){e.$set(e.where,"username",t)},expression:"where.username"}})],1)],1),t("el-col",e._b({},"el-col",e.styleResponsive?{lg:8,md:12}:{span:8},!1),[t("el-form-item",{attrs:{label:"性别:"}},[t("el-select",{staticClass:"ele-fluid",attrs:{placeholder:"请选择",clearable:""},model:{value:e.where.sex,callback:function(t){e.$set(e.where,"sex",t)},expression:"where.sex"}},[t("el-option",{attrs:{label:"男",value:"1"}}),t("el-option",{attrs:{label:"女",value:"2"}})],1)],1)],1),e.searchExpand?t("el-col",e._b({},"el-col",e.styleResponsive?{lg:8,md:12}:{span:8},!1),[t("el-form-item",{attrs:{label:"用户名:"}},[t("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.where.nickname,callback:function(t){e.$set(e.where,"nickname",t)},expression:"where.nickname"}})],1)],1):e._e(),e.searchExpand?t("el-col",e._b({},"el-col",e.styleResponsive?{lg:8,md:12}:{span:8},!1),[t("el-form-item",{attrs:{label:"手机号:"}},[t("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.where.phone,callback:function(t){e.$set(e.where,"phone",t)},expression:"where.phone"}})],1)],1):e._e(),e.searchExpand?t("el-col",e._b({},"el-col",e.styleResponsive?{lg:8,md:12}:{span:8},!1),[t("el-form-item",{attrs:{label:"状态:"}},[t("el-select",{staticClass:"ele-fluid",attrs:{placeholder:"请选择",clearable:""},model:{value:e.where.status,callback:function(t){e.$set(e.where,"status",t)},expression:"where.status"}},[t("el-option",{attrs:{label:"正常",value:0}}),t("el-option",{attrs:{label:"冻结",value:1}})],1)],1)],1):e._e(),t("el-col",e._b({},"el-col",e.styleResponsive?{lg:8,md:12}:{span:8},!1),[t("div",{staticClass:"ele-form-actions ele-text-right"},[t("el-button",{staticClass:"ele-btn-icon",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v(" 查询 ")]),t("el-button",{on:{click:e.reset}},[e._v("重置")]),t("el-link",{attrs:{type:"primary",underline:!1},on:{click:e.toggleExpand}},[e.searchExpand?[e._v(" 收起"),t("i",{staticClass:"el-icon-arrow-up"})]:[e._v(" 展开"),t("i",{staticClass:"el-icon-arrow-down"})]],2)],1)])],1)],1)],1)},i=[],l={data(){const e={username:"",nickname:"",sex:void 0,phone:"",status:void 0};return{defaultWhere:e,where:{...e},searchExpand:!1}},computed:{styleResponsive(){return this.$store.state.theme.styleResponsive}},methods:{search(){this.$emit("search",this.where)},reset(){this.where={...this.defaultWhere},this.search()},toggleExpand(){this.searchExpand=!this.searchExpand,this.$emit("expand-change",this.searchExpand)}}},n=l,r=a(1001),o=(0,r.Z)(n,s,i,!1,null,null,null),c=o.exports},89643:function(e,t,a){a.r(t),a.d(t,{default:function(){return m}});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("search-form",{on:{search:e.onSearch,"expand-change":e.onExpandChange}}),t("el-card",{attrs:{shadow:"never"}},[t("el-alert",{staticClass:"ele-alert-border",staticStyle:{"margin-bottom":"15px"},attrs:{type:"info",closable:!1}},[t("i",{staticClass:"el-icon-info ele-text-info"}),t("span",{staticClass:"ele-text"},[t("span",[e._v(" 已选择 "),t("b",{staticClass:"ele-text-info"},[e._v(e._s(e.selection.length))]),e._v(" 项数据"),t("em")]),t("span",[e._v(" 其中冻结状态的用户有 "),t("b",[e._v(e._s(e.selection.filter((e=>1===e.status)).length)+" 个")]),t("em"),t("em")])]),t("el-link",{attrs:{type:"primary",underline:!1},on:{click:e.clearChoose}},[e._v(" 清空 ")])],1),t("ele-pro-table",{ref:"table",attrs:{"row-key":"userId",title:"基础列表",border:e.bordered,stripe:e.striped,"tools-theme":e.toolDefault?"default":"none",height:e.tableHeight,"full-height":e.tableFullHeight,columns:e.columns,datasource:e.datasource,"row-click-checked":!0,selection:e.selection,"row-click-checked-intelligent":!1,paginationClass:e.circlePagination?void 0:null,"default-sort":{prop:"createTime",order:"ascending"},"cache-key":"listBasicTable"},on:{"update:selection":function(t){e.selection=t},done:e.onDone},scopedSlots:e._u([{key:"toolkit",fn:function(){return[t("div",{staticClass:"ele-space"},[t("div",{staticClass:"list-tool-item"},[t("span",[e._v("边框")]),t("el-switch",{model:{value:e.bordered,callback:function(t){e.bordered=t},expression:"bordered"}})],1),t("div",{staticClass:"list-tool-divider"},[t("el-divider",{attrs:{direction:"vertical"}})],1),t("div",{staticClass:"list-tool-item"},[t("span",[e._v("斑马线")]),t("el-switch",{model:{value:e.striped,callback:function(t){e.striped=t},expression:"striped"}})],1),t("div",{staticClass:"list-tool-divider"},[t("el-divider",{attrs:{direction:"vertical"}})],1),t("div",{staticClass:"list-tool-item"},[t("span",[e._v("表头背景")]),t("el-switch",{model:{value:e.toolDefault,callback:function(t){e.toolDefault=t},expression:"toolDefault"}})],1),t("div",{staticClass:"list-tool-divider"},[t("el-divider",{attrs:{direction:"vertical"}})],1),t("div",{staticClass:"list-tool-item"},[t("span",[e._v("固定高度")]),t("el-switch",{on:{change:e.destroyTable},model:{value:e.fixedHeight,callback:function(t){e.fixedHeight=t},expression:"fixedHeight"}})],1),t("div",{staticClass:"list-tool-divider"},[t("el-divider",{attrs:{direction:"vertical"}})],1),t("div",{staticClass:"list-tool-item"},[t("span",[e._v("圆形分页")]),t("el-switch",{model:{value:e.circlePagination,callback:function(t){e.circlePagination=t},expression:"circlePagination"}})],1),t("div",{staticClass:"list-tool-divider"},[t("el-divider",{attrs:{direction:"vertical"}})],1),t("el-button",{staticClass:"ele-btn-icon",attrs:{size:"small",type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.openEdit()}}},[e._v(" 新建 ")]),t("el-dropdown",{on:{command:e.onDropClick},scopedSlots:e._u([{key:"dropdown",fn:function(){return[t("el-dropdown-menu",[t("el-dropdown-item",{attrs:{command:"del"}},[e._v("批量删除")]),t("el-dropdown-item",{attrs:{command:"edit"}},[e._v("批量修改")])],1)]},proxy:!0}])},[t("el-button",{staticClass:"ele-btn-icon",attrs:{size:"small",disabled:!e.selection.length}},[t("span",[e._v("批量操作")]),t("i",{staticClass:"el-icon-arrow-down"})])],1),t("div",{staticClass:"list-tool-divider"},[t("el-divider",{attrs:{direction:"vertical"}})],1)],1)]},proxy:!0},{key:"avatar",fn:function({row:a}){return[t("div",{staticStyle:{"line-height":"1"}},[a.avatar?t("el-avatar",{attrs:{src:a.avatar,size:32}}):t("el-avatar",{staticClass:"ele-bg-primary",attrs:{size:32}},[e._v(" "+e._s(a.nickname&&a.nickname.length>2?a.nickname.substring(a.nickname.length-2):a.nickname)+" ")])],1)]}},{key:"nickname",fn:function({row:a}){return[t("router-link",{attrs:{to:"/list/basic/details/"+a.userId}},[e._v(" "+e._s(a.nickname)+" ")])]}},{key:"status",fn:function({row:e}){return[t("ele-dot",{attrs:{ripple:0===e.status,type:[null,"danger"][e.status],text:["正常","冻结"][e.status]}})]}},{key:"action",fn:function({row:a}){return[t("el-link",{attrs:{type:"primary",underline:!1,icon:"el-icon-edit"},on:{click:function(t){return t.stopPropagation(),e.openEdit(a)}}},[e._v(" 修改 ")]),t("el-link",{attrs:{type:"danger",underline:!1,icon:"el-icon-delete"},on:{click:function(t){return t.stopPropagation(),e.remove(a)}}},[e._v(" 删除 ")])]}},{key:"nicknameHeader",fn:function({column:a}){return[t("span",{class:{"ele-text-primary":!!e.nicknameFilterValue}},[e._v(" "+e._s(a.label)+" ")]),t("nickname-filter",{on:{search:e.onNicknameFilter}})]}}])})],1)],1)},i=[],l=a(59477),n=a(68158),r=a(37411),o=a(45623),c={name:"ListBasic",components:{SearchForm:n["default"],NicknameFilter:r["default"]},data(){return{selection:[],searchExpand:!1,bordered:!0,striped:!1,toolDefault:!0,fixedHeight:!1,circlePagination:!0,nicknameFilterValue:"",lastWhere:{}}},computed:{tableHeight(){return this.fixedHeight?this.searchExpand?"calc(100vh - 544px)":"calc(100vh - 492px)":void 0},tableFullHeight(){return this.fixedHeight?"calc(100vh - 126px)":null},columns(){return[{columnKey:"selection",type:"selection",width:45,align:"center",fixed:"left",selectable:e=>2!==e.userId},{columnKey:"index",type:"index",width:45,align:"center",showOverflowTooltip:!0,fixed:"left"},{prop:"avatar",label:this.$t("list.basic.table.avatar"),showOverflowTooltip:!0,width:80,align:"center",slot:"avatar"},{prop:"username",label:this.$t("list.basic.table.username"),sortable:"custom",showOverflowTooltip:!0,minWidth:110},{prop:"nickname",label:this.$t("list.basic.table.nickname"),sortable:"custom",showOverflowTooltip:!0,minWidth:110,slot:"nickname",headerSlot:"nicknameHeader"},{prop:"organizationName",label:this.$t("list.basic.table.organizationName"),sortable:"custom",showOverflowTooltip:!0,show:!1},{prop:"phone",label:this.$t("list.basic.table.phone"),sortable:"custom",showOverflowTooltip:!0,minWidth:110},{columnKey:"sexName",prop:"sexName",label:this.$t("list.basic.table.sexName"),sortable:"custom",showOverflowTooltip:!0,minWidth:80,filterMultiple:!1,filters:[{text:"男",value:"男"},{text:"女",value:"女"}]},{prop:"createTime",label:this.$t("list.basic.table.createTime"),sortable:"custom",showOverflowTooltip:!0,minWidth:110,formatter:(e,t,a)=>this.$util.toDateString(a)},{prop:"status",label:this.$t("list.basic.table.status"),align:"center",sortable:"custom",width:100,resizable:!1,slot:"status",showOverflowTooltip:!0},{columnKey:"action",label:this.$t("list.basic.table.action"),width:130,align:"center",resizable:!1,slot:"action",hideInSetting:!0,showOverflowTooltip:!0,fixed:"right"}]}},methods:{datasource({page:e,limit:t,where:a,order:s,filterValue:i}){return(0,l.I0)({...a,...s,...i,page:e,limit:t})},onDone(){this.$nextTick((()=>{const e=[6,9,8];this.$refs.table.setSelectedRowKeys(e)}))},reload(e){this.$refs.table.reload({page:1,where:e})},clearChoose(){this.$refs.table.clearSelection()},openEdit(e){const t=e?"/list/basic/edit":"/list/basic/add";(0,o.BR)({key:t}),this.$nextTick((()=>{this.$router.push({path:t,query:e?{id:e.userId}:void 0})}))},onDropClick(e){"del"===e?this.$message.info("点击了批量删除"):"edit"===e&&this.$message.info("点击了批量修改")},remove(e){console.log(e),this.$message.info("点击了删除")},onExpandChange(e){this.searchExpand=e},destroyTable(){this.$refs.table.reRenderTable()},onSearch(e){this.lastWhere=e,this.doReload()},onNicknameFilter(e){this.nicknameFilterValue=e,this.doReload()},doReload(){this.nicknameFilterValue?this.reload({...this.lastWhere,nickname:this.nicknameFilterValue}):this.reload(this.lastWhere)}}},d=c,u=a(1001),p=(0,u.Z)(d,s,i,!1,null,"f1b54ff2",null),m=p.exports},59477:function(e,t,a){a.d(t,{I0:function(){return i},Nq:function(){return o},OL:function(){return u},PR:function(){return n},Zy:function(){return p},bz:function(){return d},cn:function(){return r},kX:function(){return c},mL:function(){return h},we:function(){return m},yw:function(){return l}});a(21703);var s=a(18684);async function i(e){const t=await s.Z.post("/system/user/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function l(e){const t=await s.Z.get("/system/user/list",{params:e});return 0===t.data.code&&t.data.data?t.data.data:Promise.reject(new Error(t.data.message))}async function n(e){const t=await s.Z.get("/system/user/"+e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await s.Z.post("/system/user/add",e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function o(e){const t=await s.Z.post("/system/user/update",e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function c(e){const t=await s.Z.post("/system/user/"+e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function d(e){const t=await s.Z.post("/system/user/batch",{data:e});return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function u(e){const t=await s.Z.post("/system/user/status",e);return 0===t.data.code?t.data.message:Promise.reject(new Error(t.data.message))}async function p(e,t="123456"){const a=await s.Z.post("/system/user/password",{userId:e,password:t});return 0===a.data.code?a.data.message:Promise.reject(new Error(a.data.message))}async function m(e){const t=new FormData;t.append("file",e);const a=await s.Z.post("/system/user/import",t);return 0===a.data.code?a.data.message:Promise.reject(new Error(a.data.message))}async function h(e,t,a){const i=await s.Z.get("/system/user/existence",{params:{field:e,value:t,id:a}});return 0===i.data.code?i.data.message:Promise.reject(new Error(i.data.message))}}}]);