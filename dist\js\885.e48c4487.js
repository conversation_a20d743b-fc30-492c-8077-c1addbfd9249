"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[885],{50885:function(e,t,i){i.r(t),i.d(t,{default:function(){return h}});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"custom-tree-container"},[t("el-input",{attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),t("div",{staticClass:"block"},[t("p",[e._v("可选学校")]),t("el-tree",{ref:"tree",attrs:{data:e.teamTreeLeft,"node-key":"id",props:e.def<PERSON>,"expand-on-click-node":!1,"filter-node-method":e.filterNode},scopedSlots:e._u([{key:"default",fn:function({data:i}){return t("span",{staticClass:"custom-tree-node"},[t("span",[e._v(e._s(i.cityName))]),t("span",[t("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(t){return e.append(i)}}},[e._v(" 移动 ")])],1)])}}])})],1),t("div",{staticClass:"block"},[t("p",[e._v("已选学校")]),t("el-tree",{attrs:{data:e.teamTreeRight,"node-key":"id",props:e.defaulProps,"default-expand-all":"","expand-on-click-node":!1},scopedSlots:e._u([{key:"default",fn:function({node:i,data:a}){return t("span",{staticClass:"custom-tree-node"},[t("span",[e._v(e._s(a.cityName))]),t("span",[t("el-button",{attrs:{type:"text",size:"mini"},on:{click:()=>e.remove(i,a)}},[e._v(" 删除 ")])],1)])}}])})],1),t("div",{staticClass:"buttons"},[t("el-button",{attrs:{type:"primary"},on:{click:e.toParentRes}},[e._v(" 确定 ")])],1)],1)},o=[],s=(i(38862),i(16013)),n=100,c=[{id:1,cityName:"长沙",cityCode:"731",team:[]},{id:2,cityName:"邵阳",cityCode:"739",team:[]},{id:3,cityName:"株洲",cityCode:"733",team:[]},{id:4,cityName:"湘潭",cityCode:"732",team:[]},{id:5,cityName:"衡阳",cityCode:"734",team:[]},{id:6,cityName:"岳阳",cityCode:"730",team:[]},{id:7,cityName:"常德",cityCode:"736",team:[]},{id:8,cityName:"张家界",cityCode:"744",team:[]},{id:9,cityName:"益阳",cityCode:"737",team:[]},{id:10,cityName:"郴州",cityCode:"735",team:[]},{id:11,cityName:"永州",cityCode:"746",team:[]},{id:12,cityName:"怀化",cityCode:"745",team:[]},{id:13,cityName:"娄底",cityCode:"738",team:[]},{id:14,cityName:"湘西",cityCode:"743",team:[]},{id:15,cityName:"全省",cityCode:"700",team:[]}],r={data(){return{teamTreeRight:JSON.parse(JSON.stringify(c)),teamTreeLeft:JSON.parse(JSON.stringify(c)),teamTree:JSON.parse(JSON.stringify(c)),schoolList:[],defaulProps:{children:"team"},filterText:""}},created(){this.querySchool()},watch:{filterText(e){this.$refs.tree.filter(e)}},methods:{querySchool(){(0,s.et)().then((e=>{this.loading=!1,this.schoolList=e,this.listData(e)})).catch((e=>{this.$message.error(e.message)}))},listData(e){var t=this.teamTreeLeft;t.forEach((t=>{e.forEach((function(e){t.cityCode==e.schoolCity&&t.team.push({id:n++,cityCode:e.schoolCity,schoolCode:e.schoolCode,cityName:e.schoolName})}))})),this.teamTreeLeft=JSON.parse(JSON.stringify(t)),console.log(this.teamTreeLeft)},append(e){var t=this,i=this.teamTreeRight,a=this.teamTree;a.forEach((function(e){var t,a=!0;i.forEach((function(i){t=e,i.cityCode==e.cityCode&&(a=!1)})),a&&i.push(t)})),e.team&&e.team.length>0?i.forEach((function(i){e.team.forEach((function(e){if(i.cityCode==e.cityCode){var a=i.team.filter((t=>t.schoolCode==e.schoolCode));if(0==a.length){var o=t.$refs.tree.getNode(e.id);console.log(o),o.visible&&i.team.push(e)}}}))})):i.forEach((function(t){if(t.cityCode==e.cityCode){var i=t.team.filter((t=>t.schoolCode==e.schoolCode));0==i.length&&t.team.push(e)}})),this.teamTreeRight=JSON.parse(JSON.stringify(i))},remove(e,t){const i=e.parent,a=i.data.team||i.data,o=a.findIndex((e=>e.id===t.id));a.splice(o,1)},renderContent(e,{node:t,data:i}){return'<span class="custom-tree-node"><span>{'+t.cityName+'}</span><span><el-button size="mini" type="text" on-click={ () => this.append('+i+') }>Append</el-button><el-button size="mini" type="text" on-click={ () => this.remove('+t+", "+i+") }>Delete</el-button></span></span>"},filterNode(e,t){return!e||-1!==t.cityName.indexOf(e)},toParentRes(){var e=this.teamTreeRight,t="",i=[];e.forEach((function(e){e.team.forEach((function(e){t+=e.cityName+",",i.push(e)}))})),console.log(t),this.$emit("emitToParent",t,i)}}},d=r,l=i(1001),m=(0,l.Z)(d,a,o,!1,null,null,null),h=m.exports},16013:function(e,t,i){i.d(t,{et:function(){return o}});i(21703);var a=i(18684);async function o(e){const t=await a.Z.post("/hnzhsl/hnslSchool/list",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}}}]);