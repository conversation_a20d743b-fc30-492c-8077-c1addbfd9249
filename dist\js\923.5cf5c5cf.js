"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[923,3818,9569],{74952:function(e,t,a){a.d(t,{Z:function(){return h}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-search"},[t("hl-form",e._b({scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"handle-btns"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery<PERSON>lick}},[e._v("搜索")]),t("el-button",{on:{click:e.handleResetClick}},[e._v("重置")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:"formData"}},"hl-form",e.searchFormConfig,!1))],1)},o=[],n=(a(33948),function(){var e=this,t=e._self._c;return t("div",{staticClass:"hl-form"},[t("div",{staticClass:"header"},[e._t("header")],2),t("el-form",{attrs:{"label-width":e.labelWidth,model:e.formData,rules:e.rules}},[t("el-row",[e._l(e.formItems,(function(a){return[t("el-col",e._b({key:a.label},"el-col",e.colLayout,!1),[a.isHidden?e._e():t("el-form-item",{style:e.itemStyle,attrs:{label:a.label,rules:a.rules}},["input"===a.type||"password"===a.type?[t("el-input",e._b({attrs:{placeholder:a.placeholder,"show-password":"password"===a.type},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-input",a.otherOptions,!1))]:"select"===a.type?[t("el-select",e._b({staticStyle:{width:"100%"},attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-select",a.otherOptions,!1),e._l(a.options,(function(i){var o,n,l,s;return t("el-option",{key:null!==(o=i[a.optionValue])&&void 0!==o?o:i.value,attrs:{value:null!==(n=i[a.optionValue])&&void 0!==n?n:i.value,label:null!==(l=i[a.optionLabel])&&void 0!==l?l:i.title}},[e._v(e._s(null!==(s=i[a.optionLabel])&&void 0!==s?s:i.title))])})),1)]:"datepicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"twoDatePicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"section"===a.type?[t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[0]}`],callback:function(t){e.$set(e.formData,`${a.field[0]}`,t)},expression:"formData[`${item.field[0]}`]"}},"el-input",a.otherOptions,!1))],1),t("el-col",{staticClass:"line",attrs:{span:2}},[e._v("-")]),t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[1]}`],callback:function(t){e.$set(e.formData,`${a.field[1]}`,t)},expression:"formData[`${item.field[1]}`]"}},"el-input",a.otherOptions,!1))],1)]:e._e()],2)],1)]})),e._t("footer")],2)],1)],1)}),l=[],s={props:{value:{type:Object,required:!0},formItems:{type:Array,default:()=>[]},rules:{type:Object,default:()=>{}},labelWidth:{type:String,default:"100px"},itemStyle:{type:Object,default:()=>({padding:"10px 40px"})},colLayout:{type:Object,default:()=>({xl:6,lg:8,md:12,sm:24,xs:24})}},data(){return{formData:{...this.value},pickerOptions:{shortcuts:[{text:"最近一周",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},watch:{formData:{handler(e){this.$emit("input",e)},deep:!0}}},r=s,c=a(1001),d=(0,c.Z)(r,n,l,!1,null,"3585fc65",null),u=d.exports,f={components:{HlForm:u},props:{searchFormConfig:{type:Object,reuqired:!0}},data(){return{formOriginData:{},formData:{}}},created(){var e,t;const a=null!==(e=null===(t=this.searchFormConfig)||void 0===t?void 0:t.formItems)&&void 0!==e?e:[];for(const i of a)Array.isArray(i.field)?i.field.forEach((e=>{this.formOriginData[e]=""})):this.formOriginData[i.field]="";this.formData=this.formOriginData},methods:{handleResetClick(){for(const e in this.formOriginData)this.formData[`${e}`]=this.formOriginData[e];this.$emit("resetBtnClick")},handleQueryClick(){const e={...this.formData};for(const t in e)""===e[t]&&delete e[t];this.$emit("queryBtnClick",e)}}},p=f,m=(0,c.Z)(p,i,o,!1,null,"a0a65568",null),h=m.exports},46911:function(e,t,a){a.r(t),a.d(t,{default:function(){return f}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"add-terminal"},[t("div",{staticClass:"content"},[t("el-input",{attrs:{placeholder:"请输入文章标题"},model:{value:e.info.messageContent,callback:function(t){e.$set(e.info,"messageContent",t)},expression:"info.messageContent"}}),t("tinymce-editor",{ref:"editor",attrs:{init:e.option,placeholder:"请输入文章正文"},model:{value:e.info.value,callback:function(t){e.$set(e.info,"value",t)},expression:"info.value"}}),t("div",{staticClass:"tail"},[t("span",[e._v("文章设置")]),t("el-radio",{attrs:{label:1},model:{value:e.info.radioAnn,callback:function(t){e.$set(e.info,"radioAnn",t)},expression:"info.radioAnn"}},[e._v("普通通告")]),t("div",{staticClass:"message"},[t("el-radio",{attrs:{label:2},model:{value:e.info.radioAnn,callback:function(t){e.$set(e.info,"radioAnn",t)},expression:"info.radioAnn"}},[e._v("信息中心")]),t("el-select",{attrs:{size:"mini",placeholder:"请选择栏目"},model:{value:e.info.columnValue,callback:function(t){e.$set(e.info,"columnValue",t)},expression:"info.columnValue"}},e._l(e.meaageType,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),t("el-divider"),t("div",{staticClass:"footer"},[t("el-checkbox",{model:{value:e.info.radioRem,callback:function(t){e.$set(e.info,"radioRem",t)},expression:"info.radioRem"}},[e._v("页面弹窗提醒")]),t("el-date-picker",{attrs:{size:"mini",type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.info.dateValue,callback:function(t){e.$set(e.info,"dateValue",t)},expression:"info.dateValue"}})],1),t("div",{staticClass:"address"},[t("span",[e._v("发布区域")]),t("el-select",{attrs:{size:"mini",placeholder:"请选择本地网"},model:{value:e.info.cityCode,callback:function(t){e.$set(e.info,"cityCode",t)},expression:"info.cityCode"}},[e._l(e.addressArr,(function(e){return[t("el-option",{key:e.cityCode,attrs:{label:e.cityName,value:e.cityCode}})]}))],2)],1)],1)],1),t("div",{staticClass:"btn-submit"},[e._t("default")],2)])},o=[],n=a(71210),l=a(64446),s=a(69569),r={components:{TinymceEditor:n.Z},props:{addModuleInfo:{type:Object,default:()=>{}}},data(){return{addressArr:s.addressArr,meaageType:["场景新增","政策协同","辅助功能"],option:{images_upload_handler:(e,t,a)=>{const i=new FormData;i.append("file",e.blob()),(0,l.Wi)(i).then((e=>{const a=e.url;t(a)})).catch((()=>{a("上传失败")}))},file_picker_callback:(e,t,a)=>{const i=document.createElement("input");i.setAttribute("type","file"),"image"===a.filetype?i.setAttribute("accept","image/*"):"media"===a.filetype&&i.setAttribute("accept","video/*"),i.onchange=()=>{var t;const o=null===(t=i.files)||void 0===t?void 0:t[0];if(!o)return;if("media"===a.filetype&&!o.type.startsWith("video/"))return void this.$refs.editor.alert({content:"只能选择视频文件"});if(o.size/1024/1024>20)return void this.$refs.editor.alert({content:"大小不能超过 20MB"});const n=new FormData;n.append("file",o),(0,l._j)(n).then((t=>{const a=t.url;e(a)}))},i.click()}},info:{messageContent:"",value:"",radioAnn:"",columnValue:"",radioRem:"",dateValue:"",cityCode:""}}},watch:{addModuleInfo(e){Object.keys(e).length?this.initialize(e):this.info=this.$options.data().info}},created(){Object.keys(this.addModuleInfo).length&&this.initialize(this.addModuleInfo)},methods:{initialize(e){const{messageTitle:t,messageContent:a,announcementTypeCode:i,messageTypeName:o,popupSwitch:n,cityCode:l,popupStartDate:s,popupEndDate:r}=e;this.info.messageContent=t,this.info.value=a,this.info.radioAnn=i,this.info.columnValue=o,this.info.radioRem=1===n,this.info.cityCode=l,this.info.dateValue=[s,r]},handleSubmit(){let e=!1;if(!this.info.messageContent)return this.$message({message:"标题不能为空",type:"warning"});if(!this.info.value)return this.$message({message:"内容不能为空",type:"warning"});if(!this.info.radioAnn)return this.$message({message:"公告类型不能为空",type:"warning"});e=!0;const t=this.info.dateValue[0],a=this.info.dateValue[1],i={messageTitle:this.info.messageContent,messageContent:this.info.value,announcementTypeCode:this.info.radioAnn,announcementTypeName:1===this.info.radioAnn?"普通公告":"消息中心",messageTypeCode:this.meaageType.findIndex((e=>e===this.info.columnValue)),messageTypeName:this.info.columnValue,popupSwitch:this.info.radioRem?1:2,cityCode:this.info.cityCode,popupStartDate:t,popupEndDate:a,id:this.addModuleInfo.id};return console.log(i),{data:i,isSubmit:e}}}},c=r,d=a(1001),u=(0,d.Z)(c,i,o,!1,null,"21d28de5",null),f=u.exports},17551:function(e,t,a){a.r(t),a.d(t,{default:function(){return v}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("el-card",{attrs:{shadow:"never"}},[t("pageSearch",{attrs:{searchFormConfig:e.searchFormConfig},on:{queryBtnClick:e.reload}}),t("ele-pro-table",{key:e.itemKey,ref:"table",attrs:{columns:e.columns,datasource:e.datasource,"cache-key":"playeCityModuleTable"},scopedSlots:e._u([{key:"toolbar",fn:function(){return[t("el-button",{staticClass:"ele-btn-icon",attrs:{size:"small",type:"primary",icon:"el-icon-plus"},on:{click:function(t){e.addModuleInfo={},e.isShowNewConfig=!0}}},[e._v(" 新建 ")])]},proxy:!0},{key:"action",fn:function({row:a}){return[t("el-link",{attrs:{type:"primary",underline:!1,icon:"el-icon-plus"},on:{click:function(t){return e.handleModify(a)}}},[e._v(" 修改 ")]),t("el-popconfirm",{staticClass:"ele-action",attrs:{title:"确定要删除吗？"},on:{confirm:function(t){return e.remove(a.id)}},scopedSlots:e._u([{key:"reference",fn:function(){return[t("el-link",{attrs:{type:"danger",underline:!1,icon:"el-icon-delete"}},[e._v(" 删除 ")])]},proxy:!0}],null,!0)})]}}])})],1),t("ele-modal",{attrs:{width:"820px",title:(Object.keys(e.addModuleInfo).length?"修改":"新增")+"信息中心",visible:e.isShowNewConfig},on:{"update:visible":function(t){e.isShowNewConfig=t}}},[t("addMessage",{ref:"cardRef",attrs:{addModuleInfo:e.addModuleInfo}},[t("el-button",{staticClass:"btn-add submit",attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("保存")]),t("el-button",{staticClass:"btn-add back submit",attrs:{type:"primary"},on:{click:function(t){e.isShowNewConfig=!1}}},[e._v("取消")])],1)],1)],1)},o=[],n=(a(21703),a(18684));async function l(e){const t=await n.Z.post("/hnzsx/hnzsxAppMessageCenter/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function s(e){const t=await n.Z.post("/hnzsx/hnzsxAppMessageCenter/save",e,{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await n.Z.post(`/hnzsx/hnzsxAppMessageCenter/${e}`,{},{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function c(e){const t=await n.Z.post("/hnzsx/hnzsxAppMessageCenter/update",e,{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}var d=a(69569),u=a(74952),f=a(46911),p={components:{pageSearch:u.Z,addMessage:f["default"]},data(){return{columns:d.columns,searchFormConfig:d.searchFormConfig,isShowNewConfig:!1,selection:[],cardRef:null,itemKey:null,addModuleInfo:{}}},methods:{handleModify(e){console.log(e),this.addModuleInfo={...e},this.isShowNewConfig=!0},async remove(e){await r(e),this.reload()},async handleSubmit(){const{isSubmit:e,data:t}=this.$refs.cardRef.handleSubmit();e&&(Object.keys(this.addModuleInfo).length?await c(t):await s(t),this.reload(),this.isShowNewConfig=!1)},datasource({page:e,limit:t,where:a,order:i}){return l({page:e,limit:t,...a,...i})},reload(e){this.$refs.table.reload({page:1,where:e})}}},m=p,h=a(1001),y=(0,h.Z)(m,i,o,!1,null,"723e22ca",null),v=y.exports},64446:function(e,t,a){a.d(t,{As:function(){return l},Wi:function(){return n},ZS:function(){return o},_j:function(){return s},cI:function(){return r}});a(21703);var i=a(18684);async function o(e){const t=await i.Z.post("/hnzsx/hnzsxAppGoodsDetails/queryCgrList",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function n(e){const t=await i.Z.post("/avoid/file/hnzsxUpload",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function l(e){const t=await i.Z.post("/file/decode/upload",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function s(e){const t=await i.Z.post("/avoid/file/hnzsxVideoUpload",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await i.Z.get("/download/template?fileName="+e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}},69569:function(e,t,a){a.r(t),a.d(t,{addressArr:function(){return o},columns:function(){return i},searchFormConfig:function(){return n}});const i=[{width:45,type:"index",columnKey:"expand",align:"center",slot:"expand"},{prop:"messageTitle",label:"文章标题",align:"center",showOverflowTooltip:!0},{prop:"messageTypeName",label:"信息类型",align:"center",showOverflowTooltip:!0},{prop:"status",label:"状态",align:"center",showOverflowTooltip:!0},{prop:"createdDate",label:"创建时间",align:"center",showOverflowTooltip:!0},{columnKey:"action",label:"操作",width:220,align:"center",resizable:!1,slot:"action",showOverflowTooltip:!0}],o=[{cityCode:"730",cityName:"岳阳"},{cityCode:"731",cityName:"长沙"},{cityCode:"732",cityName:"湘潭"},{cityCode:"733",cityName:"株洲"},{cityCode:"734",cityName:"衡阳"},{cityCode:"735",cityName:"郴州"},{cityCode:"736",cityName:"常德"},{cityCode:"737",cityName:"益阳"},{cityCode:"738",cityName:"娄底"},{cityCode:"739",cityName:"邵阳"},{cityCode:"743",cityName:"湘西"},{cityCode:"744",cityName:"张家界"},{cityCode:"745",cityName:"怀化"},{cityCode:"746",cityName:"永州"}],n={labelWidth:"100px",itemStyle:{padding:"10px"},colLayout:{span:6},formItems:[{field:"messageTitle",type:"input",label:"文章标题",placeholder:"请输入文章标题"},{field:"popupStartDate",type:"datepicker",label:"创建时间",otherOptions:{placeholder:"请选择创建时间","value-format":"yyyy-MM-dd"}},{field:"cityCode",type:"select",label:"发布本地网",placeholder:"请选择发布本地网",options:[{value:"730",title:"岳阳"},{value:"731",title:"长沙"},{value:"732",title:"湘潭"},{value:"733",title:"株洲"},{value:"734",title:"衡阳"},{value:"735",title:"郴州"},{value:"736",title:"常德"},{value:"737",title:"益阳"},{value:"738",title:"娄底"},{value:"739",title:"邵阳"},{value:"743",title:"湘西"},{value:"744",title:"张家界"},{value:"745",title:"怀化"},{value:"746",title:"永州"}]},{field:"status",type:"select",label:"发布状态",placeholder:"请选择发布状态",options:[{title:"是",value:1},{title:"否",value:2}]}]}}}]);