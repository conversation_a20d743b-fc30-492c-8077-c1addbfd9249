"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9055,7708],{69055:function(e,t,l){l.r(t),l.d(t,{default:function(){return u}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"add-terminal"},[t("el-form",{ref:"ruleFormRef",staticClass:"el-form",attrs:{"label-position":"right",rules:e.rules,"label-width":"140px",model:e.formOptions}},[t("el-form-item",{attrs:{label:"渠道名称",prop:"schoolName"}},[t("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入渠道名称"},model:{value:e.formOptions.schoolName,callback:function(t){e.$set(e.formOptions,"schoolName",t)},expression:"formOptions.schoolName"}})],1),t("el-form-item",{attrs:{label:"渠道编码",prop:"schoolCode"}},[t("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入渠道编码"},model:{value:e.formOptions.schoolCode,callback:function(t){e.$set(e.formOptions,"schoolCode",t)},expression:"formOptions.schoolCode"}})],1),t("el-form-item",{attrs:{label:"渠道类型",prop:"schoolGradeType"}},[t("el-select",{staticStyle:{width:"220px"},attrs:{placeholder:"请选择渠道类型"},model:{value:e.formOptions.schoolGradeType,callback:function(t){e.$set(e.formOptions,"schoolGradeType",t)},expression:"formOptions.schoolGradeType"}},[e._l(["全渠道","分公司渠道运营渠道","电渠互联网卡渠道","其他"],(function(e,l){return[t("el-option",{key:e,attrs:{label:e,value:l+1}})]}))],2)],1),t("el-form-item",{attrs:{label:"渠道地市",prop:"schoolCity"}},[t("el-select",{staticStyle:{width:"220px"},attrs:{placeholder:"请选择渠道地市"},model:{value:e.formOptions.schoolCity,callback:function(t){e.$set(e.formOptions,"schoolCity",t)},expression:"formOptions.schoolCity"}},[e._l(e.addressList,(function(e){return[t("el-option",{key:e.value,attrs:{label:e.title,value:e.value}})]}))],2)],1)],1),t("div",{staticClass:"btn-submit"},[e._t("default")],2)],1)},a=[],i=l(57708),s={components:{},props:{addModuleInfo:{type:Object,default:()=>{}}},data(){return{rules:i.rules,addressList:i.addressList,formOptions:{schoolName:"",schoolCode:"",schoolGradeType:"",schoolCity:"",channelId:""}}},watch:{addModuleInfo(e){Object.keys(e).length?this.formOptions={...e}:this.formOptions=this.$options.data().formOptions}},created(){Object.keys(this.formOptions).length?this.formOptions={...this.addModuleInfo}:this.formOptions=this.$options.data().formOptions},methods:{handleSubmit(){let e=!1;return this.$refs["ruleFormRef"].validate((t=>{e=!!t})),{isSubmit:e,formOptions:this.formOptions}}}},r=s,n=l(1001),p=(0,n.Z)(r,o,a,!1,null,"b9964ef6",null),u=p.exports},57708:function(e,t,l){l.r(t),l.d(t,{addressList:function(){return i},columns:function(){return o},rules:function(){return s},searchFormConfig:function(){return a}});const o=[{width:45,type:"index",columnKey:"expand",align:"center",slot:"expand"},{prop:"schoolName",label:"渠道名称",align:"center",showOverflowTooltip:!0},{prop:"schoolCode",label:"渠道编码",align:"center",showOverflowTooltip:!0},{prop:"schoolGradeType",label:"渠道类型",align:"center",showOverflowTooltip:!0,formatter:e=>1==e.schoolGradeType?"全渠道":2==e.schoolGradeType?"分公司渠道运营渠道":3==e.schoolGradeType?"电渠互联网卡渠道":"其他"},{prop:"status",label:"状态",align:"center",showOverflowTooltip:!0,formatter:e=>0==e.status?"禁用":1==e.status?"启用":void 0},{prop:"agentName",label:"代理商联系人",align:"center",showOverflowTooltip:!0},{columnKey:"action",label:"操作",width:300,align:"center",resizable:!1,slot:"action",showOverflowTooltip:!0}],a={labelWidth:"100px",itemStyle:{padding:"10px"},colLayout:{span:6},formItems:[{field:"schoolName",type:"input",label:"渠道名称",placeholder:"请输入渠道名称"},{field:"schoolCode",type:"input",label:"渠道编码",placeholder:"请输入渠道编码"},{field:"status",type:"select",label:"状态",placeholder:"请选择状态",options:[{value:"",title:"全部"},{value:1,title:"启用"},{value:0,title:"禁用"}]},{field:"schoolGradeType",type:"select",label:"类型",placeholder:"请选择类型",options:[{value:"",title:"全部"},{value:1,title:"全渠道"},{value:2,title:"分公司渠道运营渠道"},{value:3,title:"电渠互联网卡渠道"},{value:4,title:"其他"}]},{field:"schoolCity",type:"select",label:"地市",placeholder:"请选择地市",options:[{value:"0",title:"全部"},{value:"700",title:"全省"},{value:"730",title:"岳阳"},{value:"731",title:"长沙"},{value:"732",title:"湘潭"},{value:"733",title:"株洲"},{value:"734",title:"衡阳"},{value:"735",title:"郴州"},{value:"736",title:"常德"},{value:"737",title:"益阳"},{value:"738",title:"娄底"},{value:"739",title:"邵阳"},{value:"743",title:"湘西"},{value:"744",title:"张家界"},{value:"745",title:"怀化"},{value:"746",title:"永州"}]}]},i=[{value:"700",title:"全省"},{value:"730",title:"岳阳"},{value:"731",title:"长沙"},{value:"732",title:"湘潭"},{value:"733",title:"株洲"},{value:"734",title:"衡阳"},{value:"735",title:"郴州"},{value:"736",title:"常德"},{value:"737",title:"益阳"},{value:"738",title:"娄底"},{value:"739",title:"邵阳"},{value:"743",title:"湘西"},{value:"744",title:"张家界"},{value:"745",title:"怀化"},{value:"746",title:"永州"}],s={schoolName:[{required:!0,message:"请输入渠道名称",trigger:"blur"}],schoolCode:[{required:!0,message:"请输入渠道编码",trigger:"blur"}],schoolGradeType:[{required:!0,message:"请选择学校渠道类型",trigger:"change"}],schoolCity:[{required:!0,message:"请选择渠道地市",trigger:"change"}]}}}]);