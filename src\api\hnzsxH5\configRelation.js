import request from '@/utils/request';
import { hasPermission } from '@/utils/permission';

// 权限前缀
const permPrefix = 'hnzsxH5:configRelation:';

/**
 * 获取所有地市信息（固定地市列表）
 */
export function getAllCities() {
  // 返回固定的地市列表
  return Promise.resolve([
    { cityCode: '730', cityName: '岳阳' },
    { cityCode: '731', cityName: '长沙' },
    { cityCode: '732', cityName: '湘潭' },
    { cityCode: '733', cityName: '株洲' },
    { cityCode: '734', cityName: '衡阳' },
    { cityCode: '735', cityName: '郴州' },
    { cityCode: '736', cityName: '常德' },
    { cityCode: '737', cityName: '益阳' },
    { cityCode: '738', cityName: '娄底' },
    { cityCode: '739', cityName: '邵阳' },
    { cityCode: '743', cityName: '湘西' },
    { cityCode: '744', cityName: '张家界' },
    { cityCode: '745', cityName: '怀化' },
    { cityCode: '746', cityName: '永州' }
  ]);
}

/**
 * 获取所有模块分类（全量查询）
 */
export async function getCategories(params) {
  try {
    // 直接调用获取所有列表的接口，避免分页导致数据不完整
    const res = await request.post('/hnzsxH5/hnzsxh5-category/getAllList', params || {
      status: 1 // 有效状态
    });

    if (res.data.code === 0) {
      return {
        rows: res.data.data,
        count: res.data.data.length
      };
    }

    // 兼容老接口返回格式
    if (res.data && Array.isArray(res.data)) {
      return {
        rows: res.data,
        count: res.data.length
      };
    }

    return Promise.reject(new Error(res.data.message || '获取分类数据失败'));
  } catch (error) {
    console.error('获取分类数据异常:', error);
    return { rows: [], count: 0 };
  }
}

/**
 * 获取所有模块（全量查询）
 */
export async function getModules(params) {
  try {
    // 直接调用获取所有列表的接口，避免分页导致数据不完整
    const res = await request.post('/hnzsxH5/hnzsxh5-module/getListByParam', params || {
      status: 1 // 有效状态
    });

    if (res.data.code === 0) {
      return {
        rows: res.data.data,
        count: res.data.data.length
      };
    }

    // 兼容老接口返回格式
    if (res.data && Array.isArray(res.data)) {
      return {
        rows: res.data,
        count: res.data.length
      };
    }

    return Promise.reject(new Error(res.data.message || '获取模块数据失败'));
  } catch (error) {
    console.error('获取模块数据异常:', error);
    return { rows: [], count: 0 };
  }
}

/**
 * 获取所有商品属性类别（全量查询）
 */
export async function getAttributeTypes(params) {
  try {
    // 直接调用获取所有列表的接口，避免分页导致数据不完整
    const res = await request.post('/hnzsxH5/hnzsxh5-goods-attribute-type/getListByParam', params || {
      status: 1 // 有效状态
    });

    if (res.data.code === 0) {
      return {
        rows: res.data.data,
        count: res.data.data.length
      };
    }

    // 兼容老接口返回格式
    if (res.data && Array.isArray(res.data)) {
      return {
        rows: res.data,
        count: res.data.length
      };
    }

    return Promise.reject(new Error(res.data.message || '获取商品属性类别数据失败'));
  } catch (error) {
    console.error('获取商品属性类别数据异常:', error);
    return { rows: [], count: 0 };
  }
}

/**
 * 获取所有商品标签（全量查询）
 */
export async function getTags(params) {
  try {
    // 直接调用获取所有列表的接口，避免分页导致数据不完整
    const res = await request.post('/hnzsxH5/hnzsxh5-goods-tag/getListByParam', params || {
      status: 1, // 有效状态
      pId: 0 // 只查询父级标签
    });

    if (res.data.code === 0) {
      return {
        rows: res.data.data,
        count: res.data.data.length
      };
    }

    // 兼容老接口返回格式
    if (res.data && Array.isArray(res.data)) {
      return {
        rows: res.data,
        count: res.data.length
      };
    }

    return Promise.reject(new Error(res.data.message || '获取商品标签数据失败'));
  } catch (error) {
    console.error('获取商品标签数据异常:', error);
    return { rows: [], count: 0 };
  }
}

/**
 * 根据地市编码获取关联的模块分类
 */
export async function getCategoriesByCityCode(cityCode) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/getCategoriesByCityCode', {
    cityCode: cityCode
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据地市-分类关系ID查询关联的模块信息
 */
export async function getModulesByCityCategoryRefId(cityCategoryRefId) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/getModulesByCityCategoryRefId', {
    cityCategoryRefId: cityCategoryRefId
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 保存地市-模块分类关系
 */
export async function saveCityCategoryRef(data) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/avoid/insert', data);
  if (res.data.code === 0) {
    return res.data.message || '添加成功';
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量保存地市-模块分类关系
 */
export async function batchSaveCityCategoryRef(dataList) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/avoid/batchInsert', dataList);
  if (res.data.code === 0) {
    return res.data.message || '添加成功';
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 保存地市分类-模块关系
 */
export async function saveCityCategoryModuleRef(data) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/avoid/insert', data);
  if (res.data.code === 0) {
    return res.data.message || '添加成功';
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量保存地市分类-模块关系
 */
export async function batchSaveCityCategoryModuleRef(dataList) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/avoid/batchInsert', dataList);
  if (res.data.code === 0) {
    return res.data.message || '添加成功';
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 保存模块-商品属性类别关系
 */
export async function saveModuleAttributeTypeRef(data) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/avoid/insert', data);
  if (res.data.code === 0) {
    return res.data.message || '添加成功';
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量保存模块-商品属性类别关系
 */
export async function batchSaveModuleAttributeTypeRef(dataList) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/avoid/batchInsert', dataList);
  if (res.data.code === 0) {
    return res.data.message || '添加成功';
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 保存模块-商品标签关系
 */
export async function saveModuleTagRef(data) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/avoid/insert', data);
  if (res.data.code === 0) {
    return res.data.message || '添加成功';
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量保存模块-商品标签关系
 */
export async function batchSaveModuleTagRef(dataList) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/avoid/batchInsert', dataList);
  if (res.data.code === 0) {
    return res.data.message || '添加成功';
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取模块关联的商品属性类别信息
 * @param {number} cityCategoryModuleRefId 模块关系ID
 * @returns {Promise<Array>} 属性类型列表
 * <AUTHOR>
 * @date 2025-05-07
 */
export async function getAttributeTypesByModuleRefId(cityCategoryModuleRefId) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/getAttributeTypesByModuleRefId',
    { cityCategoryModuleRefId },
    { headers: { 'Content-Type': 'application/json' } }
  );

  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取模块关联的商品标签信息
 */
export async function getTagsByModuleRefId(cityCategoryModuleRefId) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/getTagsByModuleRefId', {
    cityCategoryModuleRefId: cityCategoryModuleRefId
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除地市-模块分类关系
 * @param {number} id 关系ID
 * @returns {Promise<string>} 删除结果消息
 * <AUTHOR>
 * @date 2025-05-08
 */
export async function deleteCityCategoryRef(id) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/delete', { id });
  if (res.data.code === 0) {
    return res.data.message || '删除成功';
  }
  return Promise.reject(new Error(res.data.message || '删除失败'));
}

/**
 * 删除地市分类-模块关系
 * @param {number} id 关系ID
 * @returns {Promise<string>} 删除结果消息
 * <AUTHOR>
 * @date 2025-05-08
 */
export async function deleteCityCategoryModuleRef(id) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/delete', { id });
  if (res.data.code === 0) {
    return res.data.message || '删除成功';
  }
  return Promise.reject(new Error(res.data.message || '删除失败'));
}

/**
 * 删除模块-商品属性类别关系
 * @param {number} id 关系ID
 * @returns {Promise<string>} 删除结果消息
 * <AUTHOR>
 * @date 2025-05-08
 */
export async function deleteModuleAttributeTypeRef(id) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/delete', { id });
  if (res.data.code === 0) {
    return res.data.message || '删除成功';
  }
  return Promise.reject(new Error(res.data.message || '删除失败'));
}

/**
 * 删除模块-商品标签关系
 * @param {number} id 关系ID
 * @returns {Promise<string>} 删除结果消息
 * <AUTHOR>
 * @date 2025-05-08
 */
export async function deleteModuleTagRef(id) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/delete', { id });
  if (res.data.code === 0) {
    return res.data.message || '删除成功';
  }
  return Promise.reject(new Error(res.data.message || '删除失败'));
}

/**
 * 批量删除地市-模块分类关系
 * @param {number[]} ids 关系ID数组
 * @returns {Promise<string>} 删除结果消息
 * <AUTHOR>
 * @date 2025-05-08
 */
export async function batchDeleteCityCategoryRef(ids) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/batchDelete', { ids });
  if (res.data.code === 0) {
    return res.data.message || '批量删除成功';
  }
  return Promise.reject(new Error(res.data.message || '批量删除失败'));
}

/**
 * 批量删除地市分类-模块关系
 * @param {number[]} ids 关系ID数组
 * @returns {Promise<string>} 删除结果消息
 * <AUTHOR>
 * @date 2025-05-08
 */
export async function batchDeleteCityCategoryModuleRef(ids) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/batchDelete', { ids });
  if (res.data.code === 0) {
    return res.data.message || '批量删除成功';
  }
  return Promise.reject(new Error(res.data.message || '批量删除失败'));
}

/**
 * 批量删除模块-商品属性类别关系
 * @param {number[]} ids 关系ID数组
 * @returns {Promise<string>} 删除结果消息
 * <AUTHOR>
 * @date 2025-05-08
 */
export async function batchDeleteModuleAttributeTypeRef(ids) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchDelete', { ids });
  if (res.data.code === 0) {
    return res.data.message || '批量删除成功';
  }
  return Promise.reject(new Error(res.data.message || '批量删除失败'));
}

/**
 * 批量删除模块-商品标签关系
 * @param {number[]} ids 关系ID数组
 * @returns {Promise<string>} 删除结果消息
 * <AUTHOR>
 * @date 2025-05-08
 */
export async function batchDeleteModuleTagRef(ids) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchDelete', { ids });
  if (res.data.code === 0) {
    return res.data.message || '批量删除成功';
  }
  return Promise.reject(new Error(res.data.message || '批量删除失败'));
}

export async function batchUpdateRank(updateList) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchUpdateRank', updateList);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

// 批量更新属性排序
export async function batchUpdateRankSx(updateList) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchUpdateRank', updateList);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量获取多个地市的分类关系
 * @param {string[]} cityCodes 地市编码数组
 * @returns {Promise<Object>} 地市编码为key，分类关系数组为value的对象
 */
export async function batchGetCategoriesByCityCodes(cityCodes) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/batchGetCategoriesByCityCodes', {
    cityCodes: cityCodes
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量获取多个地市分类关系的模块信息
 * @param {number[]} cityCategoryRefIds 地市分类关系ID数组
 * @returns {Promise<Object>} 关系ID为key，模块关系数组为value的对象
 */
export async function batchGetModulesByCityCategoryRefIds(cityCategoryRefIds) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/batchGetModulesByCityCategoryRefIds', {
    cityCategoryRefIds: cityCategoryRefIds
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量获取多个模块关系的属性类型信息
 * @param {number[]} moduleRefIds 模块关系ID数组
 * @returns {Promise<Object>} 模块关系ID为key，属性类型数组为value的对象
 */
export async function batchGetAttributeTypesByModuleRefIds(moduleRefIds) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchGetAttributeTypesByModuleRefIds', {
    moduleRefIds: moduleRefIds
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量获取多个模块关系的标签信息
 * @param {number[]} moduleRefIds 模块关系ID数组
 * @returns {Promise<Object>} 模块关系ID为key，标签数组为value的对象
 */
export async function batchGetTagsByModuleRefIds(moduleRefIds) {
  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchGetTagsByModuleRefIds', {
    moduleRefIds: moduleRefIds
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

