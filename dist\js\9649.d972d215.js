"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9649],{9649:function(e,t,n){n.r(t),n.d(t,{default:function(){return d}});var l=function(){var e=this,t=e._self._c;return t("el-card",{attrs:{shadow:"never",header:"选择成员"}},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.users,border:!0}},[t("el-table-column",{attrs:{type:"index",width:"45",align:"center"}}),t("el-table-column",{attrs:{label:"用户名"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[n.isEdit?t("el-input",{attrs:{placeholder:"请输入用户名"},model:{value:n.name,callback:function(t){e.$set(n,"name",t)},expression:"row.name"}}):t("span",[e._v(e._s(n.name))])]}}])}),t("el-table-column",{attrs:{label:"工号"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[n.isEdit?t("el-input",{attrs:{placeholder:"请输入工号"},model:{value:n.number,callback:function(t){e.$set(n,"number",t)},expression:"row.number"}}):t("span",[e._v(e._s(n.number))])]}}])}),t("el-table-column",{attrs:{label:"所属部门"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[n.isEdit?t("el-select",{staticClass:"ele-fluid",attrs:{placeholder:"请选择部门"},model:{value:n.department,callback:function(t){e.$set(n,"department",t)},expression:"row.department"}},[t("el-option",{attrs:{label:"研发部",value:"研发部"}}),t("el-option",{attrs:{label:"测试部",value:"测试部"}}),t("el-option",{attrs:{label:"产品部",value:"产品部"}})],1):t("span",[e._v(e._s(n.department))])]}}])}),t("el-table-column",{attrs:{label:"操作",width:"130px",align:"center",resizable:!1},scopedSlots:e._u([{key:"default",fn:function({row:n,$index:l}){return[n.isEdit?t("el-link",{attrs:{icon:"el-icon-check",type:"success",underline:!1},on:{click:function(t){return e.done(n,l)}}},[e._v(" 完成 ")]):t("el-link",{attrs:{icon:"el-icon-edit",type:"primary",underline:!1},on:{click:function(t){return e.edit(n,l)}}},[e._v(" 修改 ")]),t("span",{staticClass:"ele-action"},[t("el-popconfirm",{attrs:{title:"确定要删除此用户吗?"},on:{confirm:function(t){return e.remove(n,l)}},scopedSlots:e._u([{key:"reference",fn:function(){return[t("el-link",{attrs:{icon:"el-icon-delete",type:"danger",underline:!1}},[e._v(" 删除 ")])]},proxy:!0}],null,!0)})],1)]}}])})],1),t("el-button",{staticStyle:{width:"100%","margin-top":"15px"},attrs:{icon:"el-icon-plus"},on:{click:e.add}},[e._v(" 新增成员 ")])],1)},r=[],a=n(39365),s=n(4270),i={data(){return{users:[]}},methods:{add(){this.users.push({key:(0,a.Vj)(8),isEdit:!0,number:"00001",name:"John Brown",department:"研发部"})},edit(e,t){this.users[t].isEdit=!0},remove(e,t){this.users.splice(t,1)},done(e,t){this.users[t].isEdit=!1}},created(){(0,s.e)().then((e=>{this.users=e.map((e=>({...e,isEdit:!1})))})).catch((e=>{this.$message.error(e.message)}))}},o=i,u=n(1001),c=(0,u.Z)(o,l,r,!1,null,null,null),d=c.exports},4270:function(e,t,n){async function l(){const e=[{key:"1",number:"00001",name:"John Brown",department:"研发部"},{key:"2",number:"00002",name:"Jim Green",department:"产品部"},{key:"3",number:"00003",name:"Joe Black",department:"产品部"}];return e}n.d(t,{e:function(){return l}})}}]);