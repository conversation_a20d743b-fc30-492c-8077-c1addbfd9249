"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9549],{57449:function(e,n,t){t.r(n),t.d(n,{default:function(){return o}});var d=function(){var e=this,n=e._self._c;return n("div",{class:["demo-text-ellipsis ele-bg-white ele-border-lighter",{expanded:e.expanded}]},[n("div",[e._v(e._s(e.content))]),n("div",{staticClass:"demo-text-ellipsis-footer ele-border-lighter ele-bg-white",on:{click:function(n){e.expanded=!e.expanded}}},[n("i",{class:e.expanded?"el-icon-arrow-up":"el-icon-arrow-down"})])])},i=[],l={props:{content:String},data(){return{expanded:!1}}},r=l,a=t(1001),s=(0,a.Z)(r,d,i,!1,null,"12c7eb28",null),o=s.exports}}]);