"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9571],{89571:function(e,s,i){i.r(s),i.d(s,{default:function(){return p}});var t=function(){var e=this,s=e._self._c;return s("el-dialog",{attrs:{visible:e.dialogVisible,title:"选择审批人",width:"520px","custom-class":"approver-dialog"},on:{close:e.handleClose}},[s("div",{staticClass:"search-box"},[s("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入用户名搜索","prefix-icon":"el-icon-search",clearable:""},on:{input:e.handleSearch},model:{value:e.searchKeyword,callback:function(s){e.searchKeyword=s},expression:"searchKeyword"}})],1),s("div",{staticClass:"approver-list-container"},[s("el-radio-group",{staticClass:"radio-group",model:{value:e.selectedId,callback:function(s){e.selectedId=s},expression:"selectedId"}},e._l(e.filteredApproverList,(function(i){return s("div",{key:i.id,staticClass:"approver-item"},[s("el-radio",{staticClass:"approver-radio",attrs:{label:i.id}},[s("div",{staticClass:"approver-info"},[s("span",{staticClass:"approver-name"},[e._v(e._s(i.userName))]),s("span",{staticClass:"approver-phone"},[e._v(e._s(i.userPhone))])])])],1)})),0),0===e.filteredApproverList.length?s("div",{staticClass:"no-data"},[s("i",{staticClass:"el-icon-warning-outline"}),s("p",[e._v("暂无匹配的审批人")])]):e._e()],1),s("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),s("el-button",{attrs:{type:"primary",disabled:!e.selectedId},on:{click:e.handleConfirm}},[e._v("确 定")])],1)])},a=[],r=(i(33948),{name:"SelectApprover",props:{visible:{type:Boolean,required:!0},approverList:{type:Array,default:()=>[]}},data(){return{dialogVisible:!1,selectedId:null,searchKeyword:"",filteredApproverList:[]}},watch:{visible(e){this.dialogVisible=e,e?(this.filteredApproverList=[...this.approverList],this.searchKeyword=""):this.selectedId=null},dialogVisible(e){e||this.$emit("update:visible",!1)},approverList:{handler(e){this.filteredApproverList=[...e]},immediate:!0}},methods:{handleClose(){this.dialogVisible=!1,this.selectedId=null},handleConfirm(){this.$emit("select",this.selectedId),this.dialogVisible=!1},handleSearch(){this.searchKeyword?this.filteredApproverList=this.approverList.filter((e=>e.userName&&e.userName.includes(this.searchKeyword))):this.filteredApproverList=[...this.approverList]}}}),l=r,o=i(1001),d=(0,o.Z)(l,t,a,!1,null,"0993c378",null),p=d.exports}}]);