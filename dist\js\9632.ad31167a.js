"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9632,4768,7764,1962],{81618:function(e){(function(t,a){e.exports=a()})(0,(function(){function e(e){var s=[];return e.AMapUI&&s.push(t(e.AMapUI)),e.Loca&&s.push(a(e.Loca)),Promise.all(s)}function t(e){return new Promise((function(t,a){var r=[];if(e.plugins)for(var l=0;l<e.plugins.length;l+=1)-1==n.AMapUI.plugins.indexOf(e.plugins[l])&&r.push(e.plugins[l]);if(i.AMapUI===s.failed)a("前次请求 AMapUI 失败");else if(i.AMapUI===s.notload){i.AMapUI=s.loading,n.AMapUI.version=e.version||n.AMapUI.version,l=n.AMapUI.version;var c=document.body||document.head,p=document.createElement("script");p.type="text/javascript",p.src="https://webapi.amap.com/ui/"+l+"/main.js",p.onerror=function(e){i.AMapUI=s.failed,a("请求 AMapUI 失败")},p.onload=function(){if(i.AMapUI=s.loaded,r.length)window.AMapUI.loadUI(r,(function(){for(var e=0,a=r.length;e<a;e++){var s=r[e].split("/").slice(-1)[0];window.AMapUI[s]=arguments[e]}for(t();o.AMapUI.length;)o.AMapUI.splice(0,1)[0]()}));else for(t();o.AMapUI.length;)o.AMapUI.splice(0,1)[0]()},c.appendChild(p)}else i.AMapUI===s.loaded?e.version&&e.version!==n.AMapUI.version?a("不允许多个版本 AMapUI 混用"):r.length?window.AMapUI.loadUI(r,(function(){for(var e=0,a=r.length;e<a;e++){var s=r[e].split("/").slice(-1)[0];window.AMapUI[s]=arguments[e]}t()})):t():e.version&&e.version!==n.AMapUI.version?a("不允许多个版本 AMapUI 混用"):o.AMapUI.push((function(e){e?a(e):r.length?window.AMapUI.loadUI(r,(function(){for(var e=0,a=r.length;e<a;e++){var s=r[e].split("/").slice(-1)[0];window.AMapUI[s]=arguments[e]}t()})):t()}))}))}function a(e){return new Promise((function(t,a){if(i.Loca===s.failed)a("前次请求 Loca 失败");else if(i.Loca===s.notload){i.Loca=s.loading,n.Loca.version=e.version||n.Loca.version;var r=n.Loca.version,l=n.AMap.version.startsWith("2"),c=r.startsWith("2");if(l&&!c||!l&&c)a("JSAPI 与 Loca 版本不对应！！");else{l=n.key,c=document.body||document.head;var p=document.createElement("script");p.type="text/javascript",p.src="https://webapi.amap.com/loca?v="+r+"&key="+l,p.onerror=function(e){i.Loca=s.failed,a("请求 AMapUI 失败")},p.onload=function(){for(i.Loca=s.loaded,t();o.Loca.length;)o.Loca.splice(0,1)[0]()},c.appendChild(p)}}else i.Loca===s.loaded?e.version&&e.version!==n.Loca.version?a("不允许多个版本 Loca 混用"):t():e.version&&e.version!==n.Loca.version?a("不允许多个版本 Loca 混用"):o.Loca.push((function(e){e?a(e):a()}))}))}if(!window)throw Error("AMap JSAPI can only be used in Browser.");var s;(function(e){e.notload="notload",e.loading="loading",e.loaded="loaded",e.failed="failed"})(s||(s={}));var n={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},i={AMap:s.notload,AMapUI:s.notload,Loca:s.notload},o={AMap:[],AMapUI:[],Loca:[]},r=[],l=function(e){"function"==typeof e&&(i.AMap===s.loaded?e(window.AMap):r.push(e))};return{load:function(t){return new Promise((function(a,o){if(i.AMap==s.failed)o("");else if(i.AMap==s.notload){var c=t.key,p=t.version,d=t.plugins;c?(window.AMap&&"lbs.amap.com"!==location.host&&o("禁止多种API加载方式混用"),n.key=c,n.AMap.version=p||n.AMap.version,n.AMap.plugins=d||n.AMap.plugins,i.AMap=s.loading,p=document.body||document.head,window.___onAPILoaded=function(n){if(delete window.___onAPILoaded,n)i.AMap=s.failed,o(n);else for(i.AMap=s.loaded,e(t).then((function(){a(window.AMap)}))["catch"](o);r.length;)r.splice(0,1)[0]()},d=document.createElement("script"),d.type="text/javascript",d.src="https://webapi.amap.com/maps?callback=___onAPILoaded&v="+n.AMap.version+"&key="+c+"&plugin="+n.AMap.plugins.join(","),d.onerror=function(e){i.AMap=s.failed,o(e)},p.appendChild(d)):o("请填写key")}else if(i.AMap==s.loaded)if(t.key&&t.key!==n.key)o("多个不一致的 key");else if(t.version&&t.version!==n.AMap.version)o("不允许多个版本 JSAPI 混用");else{if(c=[],t.plugins)for(p=0;p<t.plugins.length;p+=1)-1==n.AMap.plugins.indexOf(t.plugins[p])&&c.push(t.plugins[p]);c.length?window.AMap.plugin(c,(function(){e(t).then((function(){a(window.AMap)}))["catch"](o)})):e(t).then((function(){a(window.AMap)}))["catch"](o)}else if(t.key&&t.key!==n.key)o("多个不一致的 key");else if(t.version&&t.version!==n.AMap.version)o("不允许多个版本 JSAPI 混用");else{var h=[];if(t.plugins)for(p=0;p<t.plugins.length;p+=1)-1==n.AMap.plugins.indexOf(t.plugins[p])&&h.push(t.plugins[p]);l((function(){h.length?window.AMap.plugin(h,(function(){e(t).then((function(){a(window.AMap)}))["catch"](o)})):e(t).then((function(){a(window.AMap)}))["catch"](o)}))}}))},reset:function(){delete window.AMap,delete window.AMapUI,delete window.Loca,n={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},i={AMap:s.notload,AMapUI:s.notload,Loca:s.notload},o={AMap:[],AMapUI:[],Loca:[]}}}}))},74768:function(e,t,a){a.r(t),a.d(t,{default:function(){return h}});var s=function(){var e=this,t=e._self._c;return t("el-card",{attrs:{shadow:"never",header:"官网底部地图"}},[t("div",{ref:"locationMap",staticStyle:{"max-width":"800px",height:"300px"}})])},n=[],i=a(81618),o=a.n(i),r=a(18816),l={data(){return{mapInsLocation:null}},computed:{darkMode(){var e,t,a;return null===(e=this.$store)||void 0===e||null===(t=e.state)||void 0===t||null===(a=t.theme)||void 0===a?void 0:a.darkMode}},mounted(){this.renderLocationMap()},methods:{renderLocationMap(){o().load({key:r.K4,version:"2.0",plugins:["AMap.InfoWindow","AMap.Marker"]}).then((e=>{const t={zoom:13,center:[114.346084,30.516215]};this.darkMode&&(t.mapStyle="amap://styles/dark"),this.mapInsLocation=new e.Map(this.$refs.locationMap,t);const a=new e.InfoWindow({content:'\n            <div style="color: #333;">\n              <div style="padding: 5px;font-size: 16px;">武汉易云智科技有限公司</div>\n              <div style="padding: 0 5px;">地址: 湖北省武汉市洪山区雄楚大道222号</div>\n              <div style="padding: 0 5px;">电话: 020-123456789</div>\n            </div>\n            <a\n              style="padding: 8px 5px 0;text-decoration: none;display: inline-block;"\n              href="//uri.amap.com/marker?position=114.346084,30.511215&name=武汉易云智科技有限公司"\n              class="ele-text-primary"\n              target="_blank"\n            >到这里去→</a>\n          '});a.open(this.mapInsLocation,[114.346084,30.511215]);const s=new e.Icon({size:new e.Size(25,34),image:"//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png",imageSize:new e.Size(25,34)}),n=new e.Marker({icon:s,position:[114.346084,30.511215],offset:new e.Pixel(-12,-28)});n.setMap(this.mapInsLocation),n.on("click",(()=>{a.open(this.mapInsLocation)}))})).catch((e=>{console.error(e)}))}},watch:{darkMode(e){this.mapInsLocation&&(e?this.mapInsLocation.setMapStyle("amap://styles/dark"):this.mapInsLocation.setMapStyle("amap://styles/normal"))}},unmounted(){this.mapInsLocation&&this.mapInsLocation.destroy()}},c=l,p=a(1001),d=(0,p.Z)(c,s,n,!1,null,null,null),h=d.exports},88663:function(e,t,a){a.r(t),a.d(t,{default:function(){return T}});var s=function(){var e=this,t=e._self._c;return t("el-card",{attrs:{shadow:"never",header:"地图位置选择器"}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("div",{staticStyle:{width:"140px","margin-right":"12px"}},[t("el-select",{staticClass:"ele-fluid",attrs:{size:"small"},model:{value:e.searchType,callback:function(t){e.searchType=t},expression:"searchType"}},[t("el-option",{attrs:{label:"POI检索模式",value:0}}),t("el-option",{attrs:{label:"关键字检索模式",value:1}})],1)],1),t("el-button",{staticClass:"ele-btn-icon",attrs:{size:"small"},on:{click:e.openMapPicker}},[e._v(" 打开地图位置选择器 ")])],1),t("div",{staticStyle:{"margin-top":"12px"}},[e._v("选择位置: "+e._s(e.result.location))]),t("div",{staticStyle:{"margin-top":"12px"}},[e._v("详细地址: "+e._s(e.result.address))]),t("div",{staticStyle:{"margin-top":"12px"}},[e._v(" 经 纬 度 : "+e._s(e.result.coordinate))]),t("ele-map-picker",{attrs:{"need-city":!0,show:e.visible,"lock-scroll":!1,"close-on-click-modal":!1,"search-type":e.searchType,"dark-mode":e.darkMode},on:{"update:show":function(t){e.visible=t},done:e.onChoose}})],1)},n=[],i=a(52084),o=a(59940),r=a(81618),l=a.n(r),c=a(32648);const p={height:{type:String,default:"450px"},center:Array,zoom:{type:Number,default:11},chooseZoom:{type:Number,default:17},poiSize:{type:Number,default:30},poiType:{type:String,default:""},poiKeywords:{type:String,default:""},poiRadius:{type:Number,default:1e3},needCity:Boolean,forceChoose:{type:Boolean,default:!0},suggestionCity:{type:String,default:"全国"},searchType:{type:Number,default:0,validator:e=>[0,1].includes(e)},searchPlaceholder:String,markerSrc:{type:String,default:"https://3gimg.qq.com/lightmap/components/locationPicker2/image/marker.png"},mapKey:String,mapVersion:{type:String,default:"2.0"},mapStyle:String,darkMode:Boolean,okText:String,tipMessage:String};function d(e,t,a,s,n,i,o,r){var l,c="function"===typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=a,c._compiled=!0),s&&(c.functional=!0),i&&(c._scopeId="data-v-"+i),o?(l=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),n&&n.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(o)},c._ssrRegister=l):n&&(l=r?function(){n.call(this,(c.functional?this.parent:this).$root.$options.shadowRoot)}:n),l)if(c.functional){c._injectStyles=l;var p=c.render;c.render=function(e,t){return l.call(t),p(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,l):[l]}return{exports:e,options:c}}const h="ele-map-picker-main-icon",u={name:"MapView",mixins:[c.u],props:p,emits:["done","map-done"],data(){return{loading:!0,poiLoading:!1,confirmLoading:!1,data:[],suggestionData:[],centerIconClass:["ele-map-picker-main-icon"],keywords:"",lastSuggestion:"",selectedSuggestion:null,isItemClickMove:!1,mapIns:null,placeSearchIns:null,autoCompleteIns:null,centerMarker:null}},computed:{searchPopClass(){const e=["ele-map-picker-search-pop"];return 0!==this.searchType&&e.push("ele-map-picker-hide"),e.join(" ")}},methods:{renderMap(){this.mapKey&&!this.mapIns&&l().load({key:this.mapKey,version:this.mapVersion,plugins:["AMap.PlaceSearch","AMap.AutoComplete"]}).then((e=>{this.destroyAll(),this.autoCompleteIns=new e.AutoComplete({city:this.suggestionCity}),this.placeSearchIns=new e.PlaceSearch({type:this.poiType,pageSize:this.poiSize,pageIndex:1});const t=(()=>this.mapStyle?this.mapStyle:this.darkMode?"amap://styles/dark":void 0)();this.mapIns=new e.Map(this.$refs.mapRef,{zoom:this.zoom,center:this.center,resizeEnable:!0,mapStyle:t}),this.mapIns.on("complete",(()=>{this.loading=!1;const{lng:e,lat:t}=this.mapIns.getCenter();this.searchPoi(e,t)})),this.mapIns.on("moveend",(()=>{if(this.isItemClickMove)this.isItemClickMove=!1;else if(0===this.searchType){this.bounceIcon();const{lng:e,lat:t}=this.mapIns.getCenter();this.searchPoi(e,t)}})),this.centerMarker=new e.Marker({icon:new e.Icon({image:this.markerSrc,size:new e.Size(26,36.5),imageSize:new e.Size(26,36.5)}),offset:new e.Pixel(-13,-36.5)}),this.$emit("map-done",this.mapIns)})).catch((e=>{console.error(e)}))},onSearch(e,t){e&&this.lastSuggestion!==e?(this.lastSuggestion=e,0!==this.searchType&&(this.poiLoading=!0),this.searchKeywords(e).then((e=>{0!==this.searchType?(this.data=e,this.poiLoading=!1,this.removeCenterMarker(),t([])):(this.suggestionData=e,t(this.suggestionData))})).catch((e=>{console.error(e),this.poiLoading=!1}))):t(this.suggestionData)},onSelect(e){this.data.length&&this.data[0].name===e.name||(this.data=[{...e,selected:!0},...this.data.map((e=>({...e,selected:!1})))]),this.setMapCenter(e.location.lng,e.location.lat,this.chooseZoom),this.selectedSuggestion=e},onChoose(e){this.isItemClickMove=!0,this.data=this.data.map((t=>({...t,selected:t===e})));const{lng:t,lat:a}=e.location;this.setMapCenter(t,a,this.chooseZoom),0===this.searchType?this.bounceIcon():this.showCenterMarker(t,a)},onSuggestionBlur(){0===this.searchType&&(this.suggestionKeyWords="")},onDone(){if(!this.authenticated)return void console.warn(c.c);const e=this.getSelected();if(!e)return this.forceChoose?void this.$message.error(this.tipMessage):(this.confirmLoading=!0,void this.getMapCenter(this.needCity).then((e=>{this.confirmLoading=!1,this.$emit("done",e)})).catch((e=>{console.error(e),this.confirmLoading=!1,this.$emit("done",{})})));const t={...e.location,name:e.name,address:e.address||""};this.needCity?(this.confirmLoading=!0,this.setMapCenter(t.lng,t.lat),this.getMapCenter(!0).then((({city:e})=>{this.confirmLoading=!1,t.city=e,this.$emit("done",t)})).catch((e=>{console.error(e),this.confirmLoading=!1,this.$emit("done",t)}))):this.$emit("done",t)},searchKeywords(e){return new Promise(((t,a)=>{this.autoCompleteIns?this.autoCompleteIns.search(e,((e,a)=>{if(!(null==a?void 0:a.tips))return void t([]);const s=a.tips.filter((e=>!!e.location)).map(((e,t)=>{const a=`${e.name}(${e.district})`;return{...e,text:a,value:a,key:e.id||a+"-"+t,address:Array.isArray(e.address)?e.address[0]:e.address}}));t(s)})):a(new Error("AutoComplete instance is null"))}))},searchPoi(e,t){this.poiLoading=!0,this.searchNearBy(e,t).then((e=>{this.selectedSuggestion?0===e.length||e[0].name!==this.selectedSuggestion.name?this.data=[{...this.selectedSuggestion,selected:!0},...e]:this.data=e.map(((e,t)=>({...e,selected:0===t}))):this.data=e,this.poiLoading=!1})).catch((e=>{console.error(e),this.poiLoading=!1,this.data=[]}))},searchNearBy(e,t){return new Promise(((a,s)=>{this.placeSearchIns?this.placeSearchIns.searchNearBy(this.poiKeywords,[e,t],this.poiRadius,((e,t)=>{var n;if("complete"===e&&(null==(n=null==t?void 0:t.poiList)?void 0:n.pois)){const e=t.poiList.pois.filter((e=>!!e.location)).map(((e,t)=>({...e,key:e.id||e.name+"-"+t})));a(e)}else"no_data"===e?a([]):s(new Error(e))})):s(new Error("PlaceSearch instance is null"))}))},getSelected(){return this.data.find((e=>e.selected))},bounceIcon(){this.centerIconClass=[h],this.$nextTick((()=>{setTimeout((()=>{this.centerIconClass=[h,"ele-map-picker-anim-bounce"]}),0)}))},removeCenterMarker(){this.centerMarker&&this.mapIns&&this.mapIns.remove(this.centerMarker)},showCenterMarker(e,t){this.centerMarker?this.mapIns?null!=e&&null!=t?(this.centerMarker.setPosition([e,t]),this.mapIns.add(this.centerMarker)):this.removeCenterMarker():console.error("map instance is null"):console.error("centerMarker is null")},setMapCenter(e,t,a){this.mapIns&&null!=e&&null!=t&&(null==a?this.mapIns.setCenter([e,t]):this.mapIns.setZoomAndCenter(a,[e,t]))},getMapCenter(e){return new Promise(((t,a)=>{if(!this.mapIns)return void a(new Error("map instance is null"));const s=this.mapIns.getCenter();e?this.mapIns.getCity((e=>{s.city=e,t(s)})):t(s)}))},changeMapStyle(e){this.mapIns&&("boolean"===typeof e?e?this.mapIns.setMapStyle("amap://styles/dark"):this.mapIns.setMapStyle("amap://styles/normal"):e&&this.mapIns.setMapStyle(e))},destroyMap(){this.mapIns&&this.mapIns.destroy(),this.centerMarker=null,this.placeSearchIns=null,this.autoCompleteIns=null,this.mapIns=null},destroyAll(){this.destroyMap(),this.data=[],this.suggestionData=[],this.keywords="",this.lastSuggestion="",this.selectedSuggestion=null,this.isItemClickMove=!1},getMapIns(){return this.mapIns}},watch:{darkMode(e){this.mapStyle||this.changeMapStyle(e)},mapStyle(e){e&&this.changeMapStyle(e)},searchType(e){if(this.keywords="",this.suggestionData=[],this.selectedSuggestion=null,this.lastSuggestion="",this.removeCenterMarker(),1===e){const e=this.getSelected();if(e){const{lng:t,lat:a}=e.location;this.showCenterMarker(t,a)}}},mapKey(){this.destroyAll(),this.renderMap()}},mounted(){this.renderMap()},unmounted(){this.destroyAll()}};var m=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t("div",{staticClass:"ele-map-picker-header"},[t("div",{staticClass:"ele-map-picker-header-search"},[t("el-autocomplete",{attrs:{size:"small","fetch-suggestions":e.onSearch,placeholder:e.searchPlaceholder||e.t("ele.map.placeholder"),"popper-class":e.searchPopClass},on:{select:e.onSelect,blur:e.onSuggestionBlur},scopedSlots:e._u([{key:"suffix",fn:function(){return[t("i",{staticClass:"el-icon-search el-input__icon"})]},proxy:!0},{key:"default",fn:function({item:a}){return[t("div",{staticClass:"ele-map-picker-suggestion-item",attrs:{title:a.text}},[e._v(" "+e._s(a.text)+" ")])]}}]),model:{value:e.keywords,callback:function(t){e.keywords=t},expression:"keywords"}})],1),t("el-button",{staticClass:"ele-btn-icon",attrs:{size:"small",type:"primary",icon:"el-icon-check",loading:e.confirmLoading},on:{click:e.onDone}},[e._v(" "+e._s(e.okText)+" ")])],1),t("div",{staticClass:"ele-map-picker-body"},[t("div",{staticClass:"ele-map-picker-main"},[t("div",{ref:"mapRef",style:{height:e.height}}),0===e.searchType?[t("i",{staticClass:"ele-map-picker-main-plus el-icon-plus"}),t("img",{class:e.centerIconClass,attrs:{src:e.markerSrc,alt:""}})]:e._e()],2),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.poiLoading,expression:"poiLoading"}],staticClass:"ele-map-picker-poi-list",style:{height:e.height}},e._l(e.data,(function(a){return t("div",{key:a.key,class:["ele-map-picker-poi-item",{active:a.selected}],on:{click:function(t){return e.onChoose(a)}}},[t("i",{staticClass:"ele-map-picker-poi-item-icon el-icon-location-outline"}),t("div",{staticClass:"ele-map-picker-poi-item-title"},[e._v(e._s(a.name))]),a.address?t("div",{staticClass:"ele-map-picker-poi-item-address"},[e._v(" "+e._s(a.address)+" ")]):e._e(),t("i",{staticClass:"el-icon-circle-check ele-map-picker-poi-item-check"})])})),0)])])},g=[],f=d(u,m,g,!1,null,null,null,null);const v=f.exports;var y=a(55410);const M={...y.$7,...p,show:Boolean,title:String,width:{type:String,default:"780px"}};function k(e,t,a,s,n,i,o,r){var l,c="function"===typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=a,c._compiled=!0),s&&(c.functional=!0),i&&(c._scopeId="data-v-"+i),o?(l=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),n&&n.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(o)},c._ssrRegister=l):n&&(l=r?function(){n.call(this,(c.functional?this.parent:this).$root.$options.shadowRoot)}:n),l)if(c.functional){c._injectStyles=l;var p=c.render;c.render=function(e,t){return l.call(t),p(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,l):[l]}return{exports:e,options:c}}const w={name:"EleMapPicker",components:{EleModal:o.Z,MapView:v},mixins:[i["default"]],props:M,inject:{proLayout:{default:null}},emits:["done","open","closed","update:show","map-done"],data(){return{enable:!1}},computed:{dialogClass(){const e=["ele-map-picker-dialog"];return this.customClass&&e.push(this.customClass),this.styleResponsive&&e.push("ele-map-picker-responsive"),e.join(" ")},aMapKey(){const e=this.$ELEADMIN;return this.mapKey||(null==e?void 0:e.mapKey)},styleResponsive(){var e,t;const a=null!=(e=this.proLayout)?e:{};return null==(t=a.styleResponsive)||t}},methods:{onOpen(){this.enable=!0,this.$emit("open")},onClosed(){this.destroyOnClose&&(this.enable=!1),this.$emit("closed")},onDone(e){this.$emit("done",e)},onMapDone(e){this.$emit("map-done",e)},updateVisible(e){this.$emit("update:show",e)}}};var I=function(){var e=this,t=e._self._c;return t("EleModal",{attrs:{visible:e.show,title:e.title||e.t("ele.map.title"),width:e.width,top:e.top,modal:e.modal,"modal-append-to-body":e.modalAppendToBody,"append-to-body":e.appendToBody,"lock-scroll":e.lockScroll,"custom-class":e.dialogClass,"close-on-click-modal":e.closeOnClickModal,"close-on-press-escape":e.closeOnPressEscape,"show-close":e.showClose,"destroy-on-close":e.destroyOnClose,movable:e.movable,"move-out":e.moveOut,"move-out-positive":e.moveOutPositive,resizable:e.resizable,maxable:e.maxable,multiple:e.multiple,fullscreen:e.fullscreen,inner:e.inner,"reset-on-close":e.resetOnClose,centered:e.centered,"mask-keep-alive":!1},on:{open:e.onOpen,closed:e.onClosed,"update:visible":e.updateVisible},scopedSlots:e._u([e._l(Object.keys(e.$scopedSlots).filter((e=>"default"!==e)),(function(t){return{key:t,fn:function(a){return[e._t(t,null,null,a||{})]}}}))],null,!0)},[e.enable?t("MapView",{ref:"mapRef",attrs:{height:e.height,center:e.center,zoom:e.zoom,chooseZoom:e.chooseZoom,poiSize:e.poiSize,poiType:e.poiType,poiKeywords:e.poiKeywords,poiRadius:e.poiRadius,needCity:e.needCity,forceChoose:e.forceChoose,suggestionCity:e.suggestionCity,searchType:e.searchType,searchPlaceholder:e.searchPlaceholder||e.t("ele.map.placeholder"),markerSrc:e.markerSrc,mapKey:e.aMapKey,mapVersion:e.mapVersion,mapStyle:e.mapStyle,darkMode:e.darkMode,okText:e.okText||e.t("ele.map.ok"),tipMessage:e.t("ele.map.message")},on:{done:e.onDone,"map-done":e.onMapDone}}):e._e()],1)},A=[],C=k(w,I,A,!1,null,null,null,null);const S=C.exports;var _={components:{EleMapPicker:S},data(){return{visible:!1,searchType:0,result:{location:"",address:"",coordinate:""}}},computed:{darkMode(){var e,t,a;return null===(e=this.$store)||void 0===e||null===(t=e.state)||void 0===t||null===(a=t.theme)||void 0===a?void 0:a.darkMode}},methods:{onChoose(e){console.log(e),this.result={location:[e.city.province,e.city.city,e.city.district].join("/"),address:e.name+" "+e.address,coordinate:e.lng+","+e.lat},this.visible=!1},openMapPicker(){this.visible=!0}}},x=_,b=a(1001),L=(0,b.Z)(x,s,n,!1,null,null,null),T=L.exports},91962:function(e,t,a){a.r(t),a.d(t,{default:function(){return h}});var s=function(){var e=this,t=e._self._c;return t("el-card",{attrs:{shadow:"never",header:"轨迹回放"}},[t("div",{ref:"trackMap",staticStyle:{"max-width":"800px",height:"300px"}}),t("div",{staticStyle:{"margin-top":"15px"}},[t("el-button",{attrs:{size:"small"},on:{click:e.startTrackAnim}},[e._v("开始移动")]),t("el-button",{attrs:{size:"small"},on:{click:e.pauseTrackAnim}},[e._v("暂停移动")]),t("el-button",{attrs:{size:"small"},on:{click:e.resumeTrackAnim}},[e._v("继续移动")])],1)])},n=[],i=a(81618),o=a.n(i),r=a(18816),l={data(){return{mapInsTrack:null,carMarker:null,lineData:[[116.478935,39.997761],[116.478939,39.997825],[116.478912,39.998549],[116.478912,39.998549],[116.478998,39.998555],[116.478998,39.998555],[116.479282,39.99856],[116.479658,39.998528],[116.480151,39.998453],[116.480784,39.998302],[116.480784,39.998302],[116.481149,39.998184],[116.481573,39.997997],[116.481863,39.997846],[116.482072,39.997718],[116.482362,39.997718],[116.483633,39.998935],[116.48367,39.998968],[116.484648,39.999861]]}},computed:{darkMode(){var e,t,a;return null===(e=this.$store)||void 0===e||null===(t=e.state)||void 0===t||null===(a=t.theme)||void 0===a?void 0:a.darkMode}},mounted(){this.renderTrackMap()},methods:{renderTrackMap(){o().load({key:r.K4,version:"2.0",plugins:["AMap.MoveAnimation","AMap.Marker","AMap.Polyline"]}).then((e=>{const t={zoom:17,center:[116.478935,39.997761]};this.darkMode&&(t.mapStyle="amap://styles/dark"),this.mapInsTrack=new e.Map(this.$refs.trackMap,t),this.carMarker=new e.Marker({map:this.mapInsTrack,position:[116.478935,39.997761],icon:"https://a.amap.com/jsapi_demos/static/demo-center-v2/car.png",offset:new e.Pixel(-13,-26)}),new e.Polyline({map:this.mapInsTrack,path:this.lineData,showDir:!0,strokeColor:"#28F",strokeOpacity:1,strokeWeight:6});const a=new e.Polyline({map:this.mapInsTrack,showDir:!0,strokeColor:"#4B5",strokeOpacity:1,strokeWeight:6});this.carMarker.on("moving",(function(e){a.setPath(e.passedPath)})),this.mapInsTrack.setFitView()})).catch((e=>{console.error(e)}))},startTrackAnim(){this.carMarker&&(this.carMarker.stopMove(),this.carMarker.moveAlong(this.lineData,{duration:200,autoRotation:!0}))},pauseTrackAnim(){this.carMarker&&this.carMarker.pauseMove()},resumeTrackAnim(){this.carMarker&&this.carMarker.resumeMove()}},watch:{darkMode(e){this.mapInsTrack&&(e?this.mapInsTrack.setMapStyle("amap://styles/dark"):this.mapInsTrack.setMapStyle("amap://styles/normal"))}},unmounted(){this.mapInsTrack&&this.mapInsTrack.destroy(),this.carMarker=null}},c=l,p=a(1001),d=(0,p.Z)(c,s,n,!1,null,null,null),h=d.exports},69632:function(e,t,a){a.r(t),a.d(t,{default:function(){return h}});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("demo-picker"),t("demo-map"),t("demo-track")],1)},n=[],i=a(88663),o=a(74768),r=a(91962),l={name:"ExtensionMap",components:{DemoPicker:i["default"],DemoMap:o["default"],DemoTrack:r["default"]}},c=l,p=a(1001),d=(0,p.Z)(c,s,n,!1,null,null,null),h=d.exports}}]);