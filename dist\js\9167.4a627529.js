"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9167,6740],{74952:function(e,t,l){l.d(t,{Z:function(){return h}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-search"},[t("hl-form",e._b({scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"handle-btns"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery<PERSON>lick}},[e._v("搜索")]),t("el-button",{on:{click:e.handleResetClick}},[e._v("重置")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:"formData"}},"hl-form",e.searchFormConfig,!1))],1)},a=[],r=(l(33948),function(){var e=this,t=e._self._c;return t("div",{staticClass:"hl-form"},[t("div",{staticClass:"header"},[e._t("header")],2),t("el-form",{attrs:{"label-width":e.labelWidth,model:e.formData,rules:e.rules}},[t("el-row",[e._l(e.formItems,(function(l){return[t("el-col",e._b({key:l.label},"el-col",e.colLayout,!1),[l.isHidden?e._e():t("el-form-item",{style:e.itemStyle,attrs:{label:l.label,rules:l.rules}},["input"===l.type||"password"===l.type?[t("el-input",e._b({attrs:{placeholder:l.placeholder,"show-password":"password"===l.type},model:{value:e.formData[`${l.field}`],callback:function(t){e.$set(e.formData,`${l.field}`,t)},expression:"formData[`${item.field}`]"}},"el-input",l.otherOptions,!1))]:"select"===l.type?[t("el-select",e._b({staticStyle:{width:"100%"},attrs:{placeholder:l.placeholder},model:{value:e.formData[`${l.field}`],callback:function(t){e.$set(e.formData,`${l.field}`,t)},expression:"formData[`${item.field}`]"}},"el-select",l.otherOptions,!1),e._l(l.options,(function(o){var a,r,i,n;return t("el-option",{key:null!==(a=o[l.optionValue])&&void 0!==a?a:o.value,attrs:{value:null!==(r=o[l.optionValue])&&void 0!==r?r:o.value,label:null!==(i=o[l.optionLabel])&&void 0!==i?i:o.title}},[e._v(e._s(null!==(n=o[l.optionLabel])&&void 0!==n?n:o.title))])})),1)]:"datepicker"===l.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},model:{value:e.formData[`${l.field}`],callback:function(t){e.$set(e.formData,`${l.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",l.otherOptions,!1))]:"twoDatePicker"===l.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.formData[`${l.field}`],callback:function(t){e.$set(e.formData,`${l.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",l.otherOptions,!1))]:"section"===l.type?[t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:l.placeholder},model:{value:e.formData[`${l.field[0]}`],callback:function(t){e.$set(e.formData,`${l.field[0]}`,t)},expression:"formData[`${item.field[0]}`]"}},"el-input",l.otherOptions,!1))],1),t("el-col",{staticClass:"line",attrs:{span:2}},[e._v("-")]),t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:l.placeholder},model:{value:e.formData[`${l.field[1]}`],callback:function(t){e.$set(e.formData,`${l.field[1]}`,t)},expression:"formData[`${item.field[1]}`]"}},"el-input",l.otherOptions,!1))],1)]:e._e()],2)],1)]})),e._t("footer")],2)],1)],1)}),i=[],n={props:{value:{type:Object,required:!0},formItems:{type:Array,default:()=>[]},rules:{type:Object,default:()=>{}},labelWidth:{type:String,default:"100px"},itemStyle:{type:Object,default:()=>({padding:"10px 40px"})},colLayout:{type:Object,default:()=>({xl:6,lg:8,md:12,sm:24,xs:24})}},data(){return{formData:{...this.value},pickerOptions:{shortcuts:[{text:"最近一周",onClick(e){const t=new Date,l=new Date;l.setTime(l.getTime()-6048e5),e.$emit("pick",[l,t])}},{text:"最近一个月",onClick(e){const t=new Date,l=new Date;l.setTime(l.getTime()-2592e6),e.$emit("pick",[l,t])}},{text:"最近三个月",onClick(e){const t=new Date,l=new Date;l.setTime(l.getTime()-7776e6),e.$emit("pick",[l,t])}}]}}},watch:{formData:{handler(e){this.$emit("input",e)},deep:!0}}},s=n,p=l(1001),c=(0,p.Z)(s,r,i,!1,null,"3585fc65",null),d=c.exports,u={components:{HlForm:d},props:{searchFormConfig:{type:Object,reuqired:!0}},data(){return{formOriginData:{},formData:{}}},created(){var e,t;const l=null!==(e=null===(t=this.searchFormConfig)||void 0===t?void 0:t.formItems)&&void 0!==e?e:[];for(const o of l)Array.isArray(o.field)?o.field.forEach((e=>{this.formOriginData[e]=""})):this.formOriginData[o.field]="";this.formData=this.formOriginData},methods:{handleResetClick(){for(const e in this.formOriginData)this.formData[`${e}`]=this.formOriginData[e];this.$emit("resetBtnClick")},handleQueryClick(){const e={...this.formData};for(const t in e)""===e[t]&&delete e[t];this.$emit("queryBtnClick",e)}}},f=u,m=(0,p.Z)(f,o,a,!1,null,"a0a65568",null),h=m.exports},79167:function(e,t,l){l.r(t),l.d(t,{default:function(){return h}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("el-card",{attrs:{shadow:"never"}},[t("pageSearch",{attrs:{searchFormConfig:e.searchFormConfig},on:{queryBtnClick:e.reload}}),t("ele-pro-table",{ref:"table",attrs:{columns:e.columns,datasource:e.datasource,"cache-key":"playeCityModuleTable"},on:{"selection-change":e.selectionLineChangeHandle},scopedSlots:e._u([{key:"toolbar",fn:function(){return[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.deriveClick}},[e._v(" 导出数据 ")])]},proxy:!0}])})],1),t("arpproverDialog",{attrs:{visible:e.isShowApproverDialog},on:{"update:visible":function(t){e.isShowApproverDialog=t},select:e.handleApproverSelect}})],1)},a=[],r=(l(21703),l(18684));async function i(e){const t=await r.Z.post("/hnzhsl/hnslStudentApproval/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function n(e={}){const t=await r.Z.post("/hnzhsl/hnslStudentApproval/outputPayList",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}var s=l(40846),p=l(36740),c=l(74952),d={components:{pageSearch:c.Z},data(){return{columns:p.columns,searchFormConfig:p.searchFormConfig,cardRef:null,selectData:[],isShowApproverDialog:!1}},methods:{async deriveClick(){this.isShowApproverDialog=!0},async handleApproverSelect(e){const t={status:"",appSettingId:e},l=await n(t);window.open(`${s.JV}?name=${l.fileName}`)},selectionLineChangeHandle(e){console.log(e),this.selectData=e},datasource({page:e,limit:t,where:l,order:o}){return i({page:e,limit:t,...l,...o,status:4})},reload(e){this.$refs.table.reload({page:1,where:e})}}},u=d,f=l(1001),m=(0,f.Z)(u,o,a,!1,null,"32945677",null),h=m.exports},40846:function(e,t,l){l.d(t,{JV:function(){return a},cn:function(){return r},km:function(){return i}});var o=l(18816);const a=`${o.CT}/download/exportDaoUsers`,r=`${o.CT}/download/template`,i=`${o.CT}/download/downloadFile`;o.CT,o.CT},36740:function(e,t,l){l.r(t),l.d(t,{columns:function(){return o},searchFormConfig:function(){return a}});const o=[{width:45,type:"selection",columnKey:"expand",align:"center",slot:"expand"},{prop:"auditDate",label:"账期",align:"center",showOverflowTooltip:!0},{prop:"cityCode",label:"地市",align:"center",showOverflowTooltip:!0},{prop:"schoolName",label:"学校",align:"center",showOverflowTooltip:!0},{prop:"userName",label:"姓名",align:"center",showOverflowTooltip:!0},{prop:"teamLevel",label:"团队角色",align:"center",showOverflowTooltip:!0},{prop:"userCard",label:"身份证号",align:"center",showOverflowTooltip:!0},{prop:"userAge",label:"年龄",align:"center",showOverflowTooltip:!0},{prop:"userPhone",label:"工号",align:"center",showOverflowTooltip:!0},{prop:"userServiceTime",label:"服务时长",align:"center",showOverflowTooltip:!0},{prop:"teamName",label:"团队名称",align:"center",showOverflowTooltip:!0},{prop:"teamCount",label:"团队人数",align:"center",showOverflowTooltip:!0},{prop:"userKpi",label:"KPI考核",align:"center",showOverflowTooltip:!0},{prop:"userDate",label:"注册时间",align:"center",showOverflowTooltip:!0},{prop:"BUSINESS_NUMBER1",label:"新装数量",align:"center",showOverflowTooltip:!0},{prop:"BUSINESS_INTEGRATION1",label:"新装积分",align:"center",showOverflowTooltip:!0},{prop:"businessNumber2",label:"维系数量",align:"center",showOverflowTooltip:!0},{prop:"businessIntegration2",label:"维系积分",align:"center",showOverflowTooltip:!0},{prop:"businessIntegration3",label:"其他积分",align:"center",showOverflowTooltip:!0},{prop:"teamIntegration",label:"团队积分",align:"center",showOverflowTooltip:!0},{prop:"money1",label:"个人服务费",align:"center",showOverflowTooltip:!0},{prop:"money2",label:"团队服务费",align:"center",showOverflowTooltip:!0},{prop:"money3",label:"应发",align:"center",showOverflowTooltip:!0},{prop:"money4",label:"个税",align:"center",showOverflowTooltip:!0},{prop:"money5",label:"实发",align:"center",showOverflowTooltip:!0},{prop:"userBankNumber",label:"银行卡号",align:"center",showOverflowTooltip:!0},{prop:"userBankName",label:"银行名称",align:"center",showOverflowTooltip:!0}],a={labelWidth:"100px",itemStyle:{padding:"10px"},colLayout:{span:8},formItems:[{field:"goodsNumber",type:"input",label:"姓名/工号",placeholder:"请输入姓名/手机号码"},{field:"schoolName",type:"input",label:"学校名称",placeholder:"请输入学校名称"},{field:"teamLevel",type:"select",label:"团队类型",placeholder:"请选择团队类型",options:[{title:"全部",value:1},{title:"核心合伙人",value:2},{title:"普通合伙人",value:3},{title:"直销员",value:4}]},{field:"auditDate",type:"datepicker",label:"账期",otherOptions:{placeholder:"请选择","value-format":"yyyy-MM-dd"}},{field:["userJF1","userJF2"],type:"section",label:"个人积分",placeholder:"请输入积分区间段"}]}}}]);