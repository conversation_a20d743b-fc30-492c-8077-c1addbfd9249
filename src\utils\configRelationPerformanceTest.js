/**
 * 关联配置性能测试工具
 * 用于对比优化前后的性能差异
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */

import {
  getCategoriesByCityCode,
  getModulesByCityCategoryRefId,
  getAttributeTypesByModuleRefId,
  getTagsByModuleRefId,
  batchGetCategoriesByCityCodes,
  batchGetModulesByCityCategoryRefIds,
  batchGetAttributeTypesByModuleRefIds,
  batchGetTagsByModuleRefIds
} from '@/api/hnzsxH5/configRelation';

/**
 * 性能测试类
 */
export class ConfigRelationPerformanceTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * 测试原有的数据加载方式
   * @param {string[]} cityCodes 地市编码数组
   * @returns {Promise<Object>} 测试结果
   */
  async testLegacyDataLoading(cityCodes) {
    console.log('开始测试原有数据加载方式...');
    const startTime = Date.now();
    let apiCallCount = 0;

    try {
      // 模拟原有的加载方式
      const cityCategories = {};
      const categoryModules = {};
      const moduleAttributesAndTags = { attributes: {}, tags: {} };

      // 第一步：逐个加载地市分类关系
      for (const cityCode of cityCodes) {
        const categoryRefs = await getCategoriesByCityCode(cityCode);
        apiCallCount++;
        cityCategories[cityCode] = categoryRefs;

        // 第二步：逐个加载模块关系
        for (const ref of categoryRefs) {
          const moduleRefs = await getModulesByCityCategoryRefId(ref.refId);
          apiCallCount++;
          categoryModules[ref.refId] = moduleRefs;

          // 第三步：逐个加载属性和标签
          for (const moduleRef of moduleRefs) {
            const [attributes, tags] = await Promise.all([
              getAttributeTypesByModuleRefId(moduleRef.refId),
              getTagsByModuleRefId(moduleRef.refId)
            ]);
            apiCallCount += 2;
            moduleAttributesAndTags.attributes[moduleRef.refId] = attributes;
            moduleAttributesAndTags.tags[moduleRef.refId] = tags;
          }
        }
      }

      const duration = Date.now() - startTime;
      const result = {
        method: 'legacy',
        duration,
        apiCallCount,
        citiesCount: cityCodes.length,
        categoriesCount: Object.values(cityCategories).reduce((sum, cats) => sum + cats.length, 0),
        modulesCount: Object.values(categoryModules).reduce((sum, mods) => sum + mods.length, 0)
      };

      console.log('原有方式测试结果:', result);
      return result;
    } catch (error) {
      console.error('原有方式测试失败:', error);
      throw error;
    }
  }

  /**
   * 测试优化后的数据加载方式
   * @param {string[]} cityCodes 地市编码数组
   * @returns {Promise<Object>} 测试结果
   */
  async testOptimizedDataLoading(cityCodes) {
    console.log('开始测试优化后数据加载方式...');
    const startTime = Date.now();
    let apiCallCount = 0;

    try {
      // 第一步：批量加载地市分类关系
      const cityCategories = await batchGetCategoriesByCityCodes(cityCodes);
      apiCallCount++;

      // 收集所有地市分类关系ID
      const cityCategoryRefIds = [];
      for (const cityCode of cityCodes) {
        const categoryRefs = cityCategories[cityCode] || [];
        for (const ref of categoryRefs) {
          cityCategoryRefIds.push(ref.refId);
        }
      }

      // 第二步：批量加载模块关系
      let categoryModules = {};
      if (cityCategoryRefIds.length > 0) {
        categoryModules = await batchGetModulesByCityCategoryRefIds(cityCategoryRefIds);
        apiCallCount++;
      }

      // 收集所有模块关系ID
      const moduleRefIds = [];
      for (const refId of cityCategoryRefIds) {
        const moduleRefs = categoryModules[refId] || [];
        for (const moduleRef of moduleRefs) {
          moduleRefIds.push(moduleRef.refId);
        }
      }

      // 第三步：批量加载属性和标签
      let moduleAttributesAndTags = { attributes: {}, tags: {} };
      if (moduleRefIds.length > 0) {
        const [attributesResult, tagsResult] = await Promise.all([
          batchGetAttributeTypesByModuleRefIds(moduleRefIds),
          batchGetTagsByModuleRefIds(moduleRefIds)
        ]);
        apiCallCount += 2;
        moduleAttributesAndTags = {
          attributes: attributesResult,
          tags: tagsResult
        };
      }

      const duration = Date.now() - startTime;
      const result = {
        method: 'optimized',
        duration,
        apiCallCount,
        citiesCount: cityCodes.length,
        categoriesCount: Object.values(cityCategories).reduce((sum, cats) => sum + cats.length, 0),
        modulesCount: Object.values(categoryModules).reduce((sum, mods) => sum + mods.length, 0)
      };

      console.log('优化后方式测试结果:', result);
      return result;
    } catch (error) {
      console.error('优化后方式测试失败，可能是批量接口未实现，降级为原有方式');
      return await this.testLegacyDataLoading(cityCodes);
    }
  }

  /**
   * 对比测试
   * @param {string[]} cityCodes 地市编码数组
   * @returns {Promise<Object>} 对比结果
   */
  async comparePerformance(cityCodes) {
    console.log(`开始性能对比测试，地市数量: ${cityCodes.length}`);

    try {
      // 测试原有方式
      const legacyResult = await this.testLegacyDataLoading(cityCodes);
      
      // 等待一秒避免接口压力
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 测试优化方式
      const optimizedResult = await this.testOptimizedDataLoading(cityCodes);

      // 计算性能提升
      const performanceImprovement = {
        timeReduction: legacyResult.duration - optimizedResult.duration,
        timeReductionPercent: ((legacyResult.duration - optimizedResult.duration) / legacyResult.duration * 100).toFixed(2),
        apiCallReduction: legacyResult.apiCallCount - optimizedResult.apiCallCount,
        apiCallReductionPercent: ((legacyResult.apiCallCount - optimizedResult.apiCallCount) / legacyResult.apiCallCount * 100).toFixed(2)
      };

      const comparisonResult = {
        legacy: legacyResult,
        optimized: optimizedResult,
        improvement: performanceImprovement
      };

      console.log('性能对比结果:', comparisonResult);
      this.testResults.push(comparisonResult);

      return comparisonResult;
    } catch (error) {
      console.error('性能对比测试失败:', error);
      throw error;
    }
  }

  /**
   * 生成性能测试报告
   * @returns {string} 测试报告
   */
  generateReport() {
    if (this.testResults.length === 0) {
      return '暂无测试结果';
    }

    let report = '# 关联配置性能优化测试报告\n\n';
    
    this.testResults.forEach((result, index) => {
      report += `## 测试 ${index + 1}\n\n`;
      report += `### 测试数据规模\n`;
      report += `- 地市数量: ${result.legacy.citiesCount}\n`;
      report += `- 分类数量: ${result.legacy.categoriesCount}\n`;
      report += `- 模块数量: ${result.legacy.modulesCount}\n\n`;
      
      report += `### 性能对比\n`;
      report += `| 指标 | 原有方式 | 优化方式 | 提升 |\n`;
      report += `|------|----------|----------|------|\n`;
      report += `| 耗时 | ${result.legacy.duration}ms | ${result.optimized.duration}ms | ${result.improvement.timeReduction}ms (${result.improvement.timeReductionPercent}%) |\n`;
      report += `| API调用次数 | ${result.legacy.apiCallCount} | ${result.optimized.apiCallCount} | 减少${result.improvement.apiCallReduction}次 (${result.improvement.apiCallReductionPercent}%) |\n\n`;
    });

    return report;
  }

  /**
   * 清除测试结果
   */
  clearResults() {
    this.testResults = [];
  }
}

// 创建测试实例
export const performanceTest = new ConfigRelationPerformanceTest();

/**
 * 快速测试函数
 * @param {string[]} cityCodes 地市编码数组，默认使用湖南省部分地市
 * @returns {Promise<Object>} 测试结果
 */
export async function quickPerformanceTest(cityCodes = ['730', '731', '732', '733', '734']) {
  return await performanceTest.comparePerformance(cityCodes);
}

/**
 * 在浏览器控制台中运行的测试函数
 */
export function runConsoleTest() {
  console.log('开始在控制台运行性能测试...');
  
  // 测试不同规模的数据
  const testCases = [
    ['730', '731'], // 2个地市
    ['730', '731', '732', '733'], // 4个地市
    ['730', '731', '732', '733', '734', '735'] // 6个地市
  ];

  testCases.forEach(async (cityCodes, index) => {
    console.log(`\n=== 测试用例 ${index + 1}: ${cityCodes.length}个地市 ===`);
    try {
      const result = await performanceTest.comparePerformance(cityCodes);
      console.log(`测试完成，性能提升: 时间减少${result.improvement.timeReductionPercent}%, API调用减少${result.improvement.apiCallReductionPercent}%`);
    } catch (error) {
      console.error(`测试用例 ${index + 1} 失败:`, error);
    }
  });

  // 生成报告
  setTimeout(() => {
    console.log('\n=== 完整测试报告 ===');
    console.log(performanceTest.generateReport());
  }, 10000); // 10秒后生成报告
}
