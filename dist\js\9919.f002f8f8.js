"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9919,3176],{74952:function(e,t,a){a.d(t,{Z:function(){return f}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-search"},[t("hl-form",e._b({scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"handle-btns"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery<PERSON>lick}},[e._v("搜索")]),t("el-button",{on:{click:e.handleResetClick}},[e._v("重置")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:"formData"}},"hl-form",e.searchFormConfig,!1))],1)},s=[],i=(a(33948),function(){var e=this,t=e._self._c;return t("div",{staticClass:"hl-form"},[t("div",{staticClass:"header"},[e._t("header")],2),t("el-form",{attrs:{"label-width":e.labelWidth,model:e.formData,rules:e.rules}},[t("el-row",[e._l(e.formItems,(function(a){return[t("el-col",e._b({key:a.label},"el-col",e.colLayout,!1),[a.isHidden?e._e():t("el-form-item",{style:e.itemStyle,attrs:{label:a.label,rules:a.rules}},["input"===a.type||"password"===a.type?[t("el-input",e._b({attrs:{placeholder:a.placeholder,"show-password":"password"===a.type},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-input",a.otherOptions,!1))]:"select"===a.type?[t("el-select",e._b({staticStyle:{width:"100%"},attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-select",a.otherOptions,!1),e._l(a.options,(function(o){var s,i,r,l;return t("el-option",{key:null!==(s=o[a.optionValue])&&void 0!==s?s:o.value,attrs:{value:null!==(i=o[a.optionValue])&&void 0!==i?i:o.value,label:null!==(r=o[a.optionLabel])&&void 0!==r?r:o.title}},[e._v(e._s(null!==(l=o[a.optionLabel])&&void 0!==l?l:o.title))])})),1)]:"datepicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"twoDatePicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"section"===a.type?[t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[0]}`],callback:function(t){e.$set(e.formData,`${a.field[0]}`,t)},expression:"formData[`${item.field[0]}`]"}},"el-input",a.otherOptions,!1))],1),t("el-col",{staticClass:"line",attrs:{span:2}},[e._v("-")]),t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[1]}`],callback:function(t){e.$set(e.formData,`${a.field[1]}`,t)},expression:"formData[`${item.field[1]}`]"}},"el-input",a.otherOptions,!1))],1)]:e._e()],2)],1)]})),e._t("footer")],2)],1)],1)}),r=[],l={props:{value:{type:Object,required:!0},formItems:{type:Array,default:()=>[]},rules:{type:Object,default:()=>{}},labelWidth:{type:String,default:"100px"},itemStyle:{type:Object,default:()=>({padding:"10px 40px"})},colLayout:{type:Object,default:()=>({xl:6,lg:8,md:12,sm:24,xs:24})}},data(){return{formData:{...this.value},pickerOptions:{shortcuts:[{text:"最近一周",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},watch:{formData:{handler(e){this.$emit("input",e)},deep:!0}}},n=l,c=a(1001),d=(0,c.Z)(n,i,r,!1,null,"3585fc65",null),u=d.exports,p={components:{HlForm:u},props:{searchFormConfig:{type:Object,reuqired:!0}},data(){return{formOriginData:{},formData:{}}},created(){var e,t;const a=null!==(e=null===(t=this.searchFormConfig)||void 0===t?void 0:t.formItems)&&void 0!==e?e:[];for(const o of a)Array.isArray(o.field)?o.field.forEach((e=>{this.formOriginData[e]=""})):this.formOriginData[o.field]="";this.formData=this.formOriginData},methods:{handleResetClick(){for(const e in this.formOriginData)this.formData[`${e}`]=this.formOriginData[e];this.$emit("resetBtnClick")},handleQueryClick(){const e={...this.formData};for(const t in e)""===e[t]&&delete e[t];this.$emit("queryBtnClick",e)}}},m=p,h=(0,c.Z)(m,o,s,!1,null,"a0a65568",null),f=h.exports},9919:function(e,t,a){a.r(t),a.d(t,{default:function(){return m}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("el-card",{attrs:{shadow:"never"}},[t("pageSearch",{ref:"searchValue",attrs:{searchFormConfig:e.searchFormConfig},on:{queryBtnClick:e.reload}}),t("ele-pro-table",{ref:"table",attrs:{columns:e.columns,datasource:e.datasource,"cache-key":"playeCityModuleTable"},on:{"selection-change":e.selectionLineChangeHandle},scopedSlots:e._u([{key:"toolbar",fn:function(){return[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(t){return e.statusClick(1)}}},[e._v(" 新增 ")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(t){return e.statusClick(2)}}},[e._v(" 导出 ")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(t){return e.statusClick(3)}}},[e._v(" 批量导入 ")])]},proxy:!0},{key:"action",fn:function({row:a}){return[t("el-popconfirm",{staticClass:"ele-action",attrs:{title:"确认将该合伙人移除熟卡二维码黑名单?"},on:{confirm:function(t){return e.remove(a.id)}},scopedSlots:e._u([{key:"reference",fn:function(){return[t("el-button",{attrs:{type:"danger",underline:!1,size:"mini"}},[e._v(" 移除 ")])]},proxy:!0}],null,!0)})]}}])})],1),t("ele-modal",{attrs:{width:"680px",title:"新增黑名单",visible:e.isBlackShow},on:{"update:visible":function(t){e.isBlackShow=t}}},[t("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{"status-icon":"","label-width":"70px"}},[t("el-form-item",[t("span",{staticStyle:{"font-size":"14px",color:"#000000","margin-right":"10px"}},[e._v("账号")]),t("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"",size:"mini"},model:{value:e.userPhone,callback:function(t){e.userPhone=t},expression:"userPhone"}}),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:e.queryUserPhone}},[e._v("查询")])],1),t("el-form-item",[t("h6",[e._v("信息确认")]),e._l(e.userLists?e.userLists:[],(function(a){return t("div",{key:a.id},[t("div",[t("span",[e._v("姓名：")]),t("span",[e._v(e._s(a.userName||""))])]),t("div",[t("span",[e._v("登录账号：")]),t("span",[e._v(e._s(a.userPhone||""))])]),t("div",[t("span",[e._v("合伙人等级：")]),t("span",[e._v(e._s(e.statusSf(a.statusSf)))])]),t("div",{staticStyle:{"margin-bottom":"15px"}},[t("span",[e._v("地市：")]),t("span",[e._v(e._s(e.cityName))]),t("span",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[e._v(e._s(e.cityCode(a.cityCode)))])])])}))],2),t("div",{staticStyle:{width:"100%","text-align":"center"}},[t("el-button",{attrs:{size:"small"},on:{click:e.closeModal}},[e._v("取消")]),t("el-button",{staticStyle:{"margin-left":"50px"},attrs:{type:"primary",size:"small"},on:{click:e.onSubmit}},[e._v("保存")])],1)],1)],1),t("ele-modal",{attrs:{width:"680px",title:"批量审批",visible:e.isShowApprove},on:{"update:visible":function(t){e.isShowApprove=t}}},[t("div",{staticClass:"approve-content"},[t("div",{staticClass:"item first"},[t("div",{staticClass:"title"},[t("span",{staticClass:"p1"},[e._v("下载模板：")]),t("p",{staticClass:"p2"},[e._v("为提高导入的成功率，请下载并使用系统提供的模板: "),t("el-button",{staticClass:"btn-approve",attrs:{size:"small",type:"primary"},on:{click:e.templateClick}},[e._v(" 下载模板 ")])],1)])]),t("div",{staticClass:"item"},[t("div",{staticClass:"title"},[t("span",{staticClass:"p1"},[e._v("上传文件：")]),t("p",{staticClass:"p2"},[e._v("仅支持:.xlsx，.xls,;文件大小:≤4M "),t("el-button",{staticClass:"btn-approve",attrs:{size:"small",type:"primary"},on:{click:e.intoClick}},[e._v(" 开始导入 ")])],1)])])]),t("input",{staticStyle:{display:"none"},attrs:{name:"uploadFile",id:"uploadFile",type:"file"},on:{change:e.uploadFile}})]),t("arpproverDialog",{attrs:{visible:e.isShowApproverDialog},on:{"update:visible":function(t){e.isShowApproverDialog=t},select:e.handleApproverSelect}})],1)},s=[],i=a(97732),r=a(40846),l=a(33176),n=a(74952),c={components:{pageSearch:n.Z},data(){return{columns:l.columns,searchFormConfig:l.searchFormConfig,cardRef:null,selectData:[],isShowApprove:!1,isBlackShow:!1,userPhone:"",cityName:"",userLists:[],city:[{cityCode:"731",cityName:"长沙"},{cityCode:"733",cityName:"株洲"},{cityCode:"732",cityName:"湘潭"},{cityCode:"734",cityName:"衡阳"},{cityCode:"739",cityName:"邵阳"},{cityCode:"730",cityName:"岳阳"},{cityCode:"736",cityName:"常德"},{cityCode:"744",cityName:"张家界"},{cityCode:"737",cityName:"益阳"},{cityCode:"735",cityName:"郴州"},{cityCode:"746",cityName:"永州"},{cityCode:"745",cityName:"怀化"},{cityCode:"738",cityName:"娄底"},{cityCode:"743",cityName:"湘西"}],where:{schoolName:"",schoolCode:"",schoolCity:""},isShowApproverDialog:!1}},methods:{queryUserPhone(){""!=this.userPhone?(0,i.g6)(this.userPhone).then((e=>{e.length<1&&this.$message.error("未查询到合伙人信息，请检查账号是否输入正确"),this.userLists=e})):this.$message.error("请输入需要查询的合伙人账号")},closeModal(){this.isBlackShow=!1,this.userPhone="",this.userLists=[]},onSubmit(){if(""==this.userPhone)return void this.$message.error("保存失败，请先查询合伙人信息");if(this.userLists.length<1)return void this.$message.error("保存失败，请先查询合伙人信息");let e=[];this.userLists.forEach((t=>{let a={userName:t.userName,userPhone:t.userPhone,cityCode:t.cityCode};e.push(a)})),(0,i.a1)(e).then((()=>{this.isBlackShow=!1,this.$message.success("保存合伙人信息成功"),this.reload()})).catch((e=>{this.$message.error(e.message)}))},statusClick(e){1==e?this.isBlackShow=!0:2==e?this.isShowApproverDialog=!0:3==e&&(this.isShowApprove=!0)},async handleApproverSelect(e){console.log(e,"==approverId"),this.selectedApproverId=e;var t=this.$refs.searchValue.formData;t.appSettingId=e;const a=await(0,i.uW)(t);window.open(`${r.JV}?name=${a.fileName}`)},cityCode(e){this.city.forEach((t=>{t.cityCode==e&&(this.cityName=t.cityName)}))},statusSf(e){return 1==e?"一级合伙人":2==e?"二级合伙人":3==e?"三级合伙人":4==e?"四级合伙人":5==e?"省级管理员":6==e?"市级管理员":void 0},remove(e){(0,i.PG)(e).then((()=>{this.$message.success("该合伙人已成功移除熟卡二维码黑名单"),this.reload()})).catch((e=>{this.$message.error(e.message)}))},async templateClick(){window.open(`${r.cn}?fileName=黑名单导入模板`)},intoClick(){document.getElementById("uploadFile").click()},uploadFile(e){console.log("111");let t=e.target.files[0];const a=this.$messageLoading("上传中..");return console.log("file",t),(0,i.tA)(t).then((e=>{"6"==e.resultCode?(window.open(`${r.JV}?name=${e.fileName}`),this.reload(),this.$message.success("导入成功")):"1"==e.resultCode?this.$message.error("导入文件大于5M"):"2"==e.resultCode?this.$message.error("导入文件格式不正确"):"3"==e.resultCode?this.$message.error("网络异常，请稍后再试"):"4"==e.resultCode?this.$message.error("文件内容为空，或者解析失败"):"5"==e.resultCode?this.$message.error("导入文件内容格式错误"):"-1"==e.resultCode||8==e.code?this.$message.error(e.msg):0==e.resultCode&&(window.open(`${r.JV}?name=`+e.fileName),this.$message.success("导入成功"),this.reload()),this.reload(),a.close(),this.isShowApprove=!1,document.getElementById("uploadFile").value=""})).catch((e=>{a.close(),this.$message.error(e.message),this.isShowApprove=!1})),!1},selectionLineChangeHandle(e){console.log(e),this.selectData=e},datasource({page:e,limit:t,where:a,order:o}){return(0,i.QJ)({page:e,limit:t,...a,...o,status:1})},reload(e){this.userLists=[],this.$refs.table.reload({page:1,where:e})}}},d=c,u=a(1001),p=(0,u.Z)(d,o,s,!1,null,"0c879273",null),m=p.exports},40846:function(e,t,a){a.d(t,{JV:function(){return s},cn:function(){return i},km:function(){return r}});var o=a(18816);const s=`${o.CT}/download/exportDaoUsers`,i=`${o.CT}/download/template`,r=`${o.CT}/download/downloadFile`;o.CT,o.CT},97732:function(e,t,a){a.d(t,{Ft:function(){return l},PG:function(){return c},QJ:function(){return n},Su:function(){return s},a1:function(){return u},bl:function(){return i},e5:function(){return r},g6:function(){return d},tA:function(){return m},uW:function(){return p}});a(21703);var o=a(18684);async function s(e){const t=await o.Z.post("/hnzhsl/hnslmaturecardqrcode/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function i(e){const t=await o.Z.post("/hnzhsl/hnslmaturecardqrcode/update",e,{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await o.Z.post("/hnzhsl/hnslmaturecardqrcode/outputMatureCardQrcodeTable",e,{},{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function l(e){const t=new FormData;t.append("file",e);const a=await o.Z.post("/hnzhsl/hnslmaturecardqrcode/decode/importMature",t,{},{showLoading:!0});return 0===a.data.code?a.data.data:Promise.reject(new Error(a.data.message))}async function n(e){const t=await o.Z.post("/hnzhsl/hnslblacklist/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function c(e){const t=await o.Z.post("/hnzhsl/hnslblacklist/"+e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function d(e){const t=await o.Z.post("/hnzhsl/hnslblacklist/getUserPhone/"+e,{});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function u(e){const t=await o.Z.post("/hnzhsl/hnslblacklist/save",e,{});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function p(e){const t=await o.Z.post("/hnzhsl/hnslblacklist/outputBlackListTable",e,{},{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function m(e){const t=new FormData;t.append("file",e);const a=await o.Z.post("/hnzhsl/hnslblacklist/decode/importBlackList",t,{},{showLoading:!0});return 0===a.data.code?a.data.data:Promise.reject(new Error(a.data.message))}},33176:function(e,t,a){a.r(t),a.d(t,{columns:function(){return o},searchFormConfig:function(){return i}});const o=[{width:45,type:"selection",columnKey:"expand",align:"center",slot:"expand"},{prop:"id",label:"ID",align:"center",showOverflowTooltip:!0},{prop:"userName",label:"合伙人姓名",align:"center",showOverflowTooltip:!0},{prop:"userPhone",label:"合伙人账号",align:"center",showOverflowTooltip:!0},{prop:"cityCode",label:"地市",align:"center",showOverflowTooltip:!0,formatter:(e,t,a)=>{const o=s.find((e=>e.code===a));return o?o.name:a}},{prop:"offlineQrcode",label:"线下预约二维码",align:"center",showOverflowTooltip:!0,formatter:e=>1==e.offlineQrcode?"开启":"关闭"},{prop:"onlineQrcode",label:"线上预约二维码",align:"center",showOverflowTooltip:!0,formatter:e=>1==e.onlineQrcode?"开启":"关闭"},{prop:"onlineActiveQrcode",label:"线上激活二维码",align:"center",showOverflowTooltip:!0,formatter:e=>1==e.onlineActiveQrcode?"开启":"关闭"},{prop:"selfActiveQrcode",label:"自助激活二维码",align:"center",showOverflowTooltip:!0,formatter:e=>1==e.selfActiveQrcode?"开启":"关闭"},{columnKey:"action",label:"操作",width:150,align:"center",resizable:!1,slot:"action",fixed:"right",showOverflowTooltip:!0}],s=[{code:"730",name:"岳阳"},{code:"731",name:"长沙"},{code:"732",name:"湘潭"},{code:"733",name:"株洲"},{code:"734",name:"衡阳"},{code:"735",name:"郴州"},{code:"736",name:"常德"},{code:"737",name:"益阳"},{code:"738",name:"娄底"},{code:"739",name:"邵阳"},{code:"743",name:"湘西"},{code:"744",name:"张家界"},{code:"745",name:"怀化"},{code:"746",name:"永州"}],i={labelWidth:"100px",itemStyle:{padding:"4px"},colLayout:{span:5},formItems:[{field:"userName",type:"input",label:"合伙人姓名",placeholder:"请输入合伙人姓名"},{field:"userPhone",type:"input",label:"合伙人账号",placeholder:"请输入合伙人账号"},{field:"cityCode",type:"select",label:"地市",placeholder:"请选择地市",options:[{value:"",title:"全部"},{value:"730",title:"岳阳"},{value:"731",title:"长沙"},{value:"732",title:"湘潭"},{value:"733",title:"株洲"},{value:"734",title:"衡阳"},{value:"735",title:"郴州"},{value:"736",title:"常德"},{value:"737",title:"益阳"},{value:"738",title:"娄底"},{value:"739",title:"邵阳"},{value:"743",title:"湘西"},{value:"744",title:"张家界"},{value:"745",title:"怀化"},{value:"746",title:"永州"}]}]}}}]);