"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9640,4175,4322],{74952:function(e,t,a){a.d(t,{Z:function(){return h}});var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-search"},[t("hl-form",e._b({scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"handle-btns"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQueryClick}},[e._v("搜索")]),t("el-button",{on:{click:e.handleResetClick}},[e._v("重置")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:"formData"}},"hl-form",e.searchFormConfig,!1))],1)},r=[],i=(a(33948),function(){var e=this,t=e._self._c;return t("div",{staticClass:"hl-form"},[t("div",{staticClass:"header"},[e._t("header")],2),t("el-form",{attrs:{"label-width":e.labelWidth,model:e.formData,rules:e.rules}},[t("el-row",[e._l(e.formItems,(function(a){return[t("el-col",e._b({key:a.label},"el-col",e.colLayout,!1),[a.isHidden?e._e():t("el-form-item",{style:e.itemStyle,attrs:{label:a.label,rules:a.rules}},["input"===a.type||"password"===a.type?[t("el-input",e._b({attrs:{placeholder:a.placeholder,"show-password":"password"===a.type},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-input",a.otherOptions,!1))]:"select"===a.type?[t("el-select",e._b({staticStyle:{width:"100%"},attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-select",a.otherOptions,!1),e._l(a.options,(function(l){var r,i,o,n;return t("el-option",{key:null!==(r=l[a.optionValue])&&void 0!==r?r:l.value,attrs:{value:null!==(i=l[a.optionValue])&&void 0!==i?i:l.value,label:null!==(o=l[a.optionLabel])&&void 0!==o?o:l.title}},[e._v(e._s(null!==(n=l[a.optionLabel])&&void 0!==n?n:l.title))])})),1)]:"datepicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"twoDatePicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"section"===a.type?[t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[0]}`],callback:function(t){e.$set(e.formData,`${a.field[0]}`,t)},expression:"formData[`${item.field[0]}`]"}},"el-input",a.otherOptions,!1))],1),t("el-col",{staticClass:"line",attrs:{span:2}},[e._v("-")]),t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[1]}`],callback:function(t){e.$set(e.formData,`${a.field[1]}`,t)},expression:"formData[`${item.field[1]}`]"}},"el-input",a.otherOptions,!1))],1)]:e._e()],2)],1)]})),e._t("footer")],2)],1)],1)}),o=[],n={props:{value:{type:Object,required:!0},formItems:{type:Array,default:()=>[]},rules:{type:Object,default:()=>{}},labelWidth:{type:String,default:"100px"},itemStyle:{type:Object,default:()=>({padding:"10px 40px"})},colLayout:{type:Object,default:()=>({xl:6,lg:8,md:12,sm:24,xs:24})}},data(){return{formData:{...this.value},pickerOptions:{shortcuts:[{text:"最近一周",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},watch:{formData:{handler(e){this.$emit("input",e)},deep:!0}}},s=n,d=a(1001),c=(0,d.Z)(s,i,o,!1,null,"3585fc65",null),u=c.exports,p={components:{HlForm:u},props:{searchFormConfig:{type:Object,reuqired:!0}},data(){return{formOriginData:{},formData:{}}},created(){var e,t;const a=null!==(e=null===(t=this.searchFormConfig)||void 0===t?void 0:t.formItems)&&void 0!==e?e:[];for(const l of a)Array.isArray(l.field)?l.field.forEach((e=>{this.formOriginData[e]=""})):this.formOriginData[l.field]="";this.formData=this.formOriginData},methods:{handleResetClick(){for(const e in this.formOriginData)this.formData[`${e}`]=this.formOriginData[e];this.$emit("resetBtnClick")},handleQueryClick(){const e={...this.formData};for(const t in e)""===e[t]&&delete e[t];this.$emit("queryBtnClick",e)}}},f=p,m=(0,d.Z)(f,l,r,!1,null,"a0a65568",null),h=m.exports},54175:function(e,t,a){a.r(t),a.d(t,{default:function(){return c}});var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"add-terminal"},[e._l(e.formAddArr,(function(a,l){return[t("el-form",{key:l,ref:"ruleFormRef",refInFor:!0,staticClass:"el-form",attrs:{"label-position":"right",rules:e.rules,"label-width":"110px",model:a}},[l?t("el-divider",{staticClass:"el-divider"}):e._e(),t("el-form-item",{attrs:{label:"设备名称",prop:"terminalName"}},[t("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入设备名称"},model:{value:a.terminalName,callback:function(t){e.$set(a,"terminalName",t)},expression:"item.terminalName"}})],1),t("el-form-item",{attrs:{label:"设备类型",prop:"terminalType"}},[t("el-select",{attrs:{placeholder:"请选择设备类型"},model:{value:a.terminalType,callback:function(t){e.$set(a,"terminalType",t)},expression:"item.terminalType"}},[e._l(["终端类型","分期设备类型"],(function(e,a){return[t("el-option",{key:e,attrs:{label:e,value:a+1}})]}))],2)],1),l?[t("div",{staticClass:"btn-content"},[t("el-button",{staticClass:"btn-add",attrs:{type:"danger"},on:{click:function(t){return e.removeClick(l)}}},[e._v("移除数据")])],1)]:[Object.keys(e.addModuleInfo).length?e._e():t("div",{staticClass:"btn-add-content"},[t("el-button",{staticClass:"btn-add",attrs:{type:"primary"},on:{click:e.addClick}},[e._v("添加数据")])],1)]],2)]})),t("div",{staticClass:"btn-submit"},[e._t("default")],2)],2)},r=[],i=a(44322),o={props:{addModuleInfo:{type:Object,default:()=>{}}},watch:{addModuleInfo(e){this.formAddArr=[{...e}]}},created(){this.formAddArr=[{...this.addModuleInfo}]},data(){return{rules:i.rules,formAddArr:[{terminalName:"",terminalType:""}]}},methods:{addClick(){this.formAddArr.push({terminalName:"",terminalType:""})},removeClick(e){this.formAddArr.splice(e,1)},handleSubmit(){var e;let t=!1;return null===(e=this.$refs["ruleFormRef"])||void 0===e||e.forEach((e=>{e.validate((e=>{t=!!e}))})),{isSubmit:t,formAddArr:this.formAddArr}}}},n=o,s=a(1001),d=(0,s.Z)(n,l,r,!1,null,"03e05491",null),c=d.exports},19640:function(e,t,a){a.r(t),a.d(t,{default:function(){return b}});var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("el-card",{attrs:{shadow:"never"}},[t("pageSearch",{attrs:{searchFormConfig:e.searchFormConfig},on:{queryBtnClick:e.reload}}),t("ele-pro-table",{key:e.itemKey,ref:"table",attrs:{columns:e.columns,datasource:e.datasource,"cache-key":"playeCityModuleTable"},scopedSlots:e._u([{key:"toolbar",fn:function(){return[t("el-button",{staticClass:"ele-btn-icon",attrs:{size:"small",type:"primary",icon:"el-icon-plus"},on:{click:function(t){e.addModuleInfo={},e.isShowNewConfig=!0}}},[e._v(" 新建 ")])]},proxy:!0},{key:"action",fn:function({row:a}){return[t("el-link",{attrs:{type:"primary",underline:!1,icon:"el-icon-plus"},on:{click:function(t){return e.handleModify(a)}}},[e._v(" 修改 ")]),t("el-popconfirm",{staticClass:"ele-action",attrs:{title:"确定要删除吗？"},on:{confirm:function(t){return e.remove(a.id)}},scopedSlots:e._u([{key:"reference",fn:function(){return[t("el-link",{attrs:{type:"danger",underline:!1,icon:"el-icon-delete"}},[e._v(" 删除 ")])]},proxy:!0}],null,!0)})]}}])})],1),t("ele-modal",{attrs:{width:"820px",title:(Object.keys(e.addModuleInfo).length?"修改":"新增")+"终端管理",visible:e.isShowNewConfig},on:{"update:visible":function(t){e.isShowNewConfig=t}}},[t("addTerminal",{ref:"cardRef",attrs:{addModuleInfo:e.addModuleInfo}},[t("el-button",{staticClass:"btn-add back submit",attrs:{type:"primary"},on:{click:function(t){e.isShowNewConfig=!1}}},[e._v("返回")]),t("el-button",{staticClass:"btn-add submit",attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)},r=[],i=(a(33948),a(21703),a(18684));async function o(e){const t=await i.Z.post("/hnzsx/hnzsxAppTerminalType/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function n(e){const t=await i.Z.post("/hnzsx/hnzsxAppTerminalType/save",e,{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function s(e){const t=await i.Z.post(`/hnzsx/hnzsxAppTerminalType/${e}`,{},{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function d(e){const t=await i.Z.post("/hnzsx/hnzsxAppTerminalType/update",e,{showLoading:!0});return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}var c=a(44322),u=a(74952),p=a(54175),f={components:{pageSearch:u.Z,addTerminal:p["default"]},data(){return{columns:c.columns,searchFormConfig:c.searchFormConfig,isShowNewConfig:!1,selection:[],cardRef:null,itemKey:null,addModuleInfo:{}}},methods:{handleModify(e){this.addModuleInfo={...e},this.isShowNewConfig=!0},async remove(e){await s(e),this.reload()},async handleSubmit(){const{isSubmit:e,formAddArr:t}=this.$refs.cardRef.handleSubmit();if(!e)return;const a=[...t];Object.keys(this.addModuleInfo).length?await d(...t):await n(a),this.reload(),this.isShowNewConfig=!1},datasource({page:e,limit:t,where:a,order:l}){return o({page:e,limit:t,...a,...l})},reload(e){this.$refs.table.reload({page:1,where:e})}}},m=f,h=a(1001),y=(0,h.Z)(m,l,r,!1,null,"5f6a10e0",null),b=y.exports},44322:function(e,t,a){a.r(t),a.d(t,{columns:function(){return l},rules:function(){return i},searchFormConfig:function(){return r}});const l=[{width:45,type:"index",columnKey:"expand",align:"center",slot:"expand"},{prop:"terminalNumber",label:"设备编码",align:"center",showOverflowTooltip:!0},{prop:"terminalName",label:"设备名称",align:"center",showOverflowTooltip:!0},{prop:"status",label:"状态",align:"center",showOverflowTooltip:!0},{prop:"createdDate",label:"创建时间",align:"center",showOverflowTooltip:!0},{columnKey:"action",label:"操作",width:220,align:"center",resizable:!1,slot:"action",showOverflowTooltip:!0}],r={labelWidth:"100px",itemStyle:{padding:"10px"},colLayout:{span:6},formItems:[{field:"terminalType",type:"select",label:"设备类型",placeholder:"请选择设备类型",options:[{title:"终端类型",value:1},{title:"分期设备类型",value:2}]},{field:"status",type:"select",label:"状态",placeholder:"请选择状态",options:[{title:"在架",value:1},{title:"下架",value:0}]},{field:"terminalName",type:"input",label:"设备名称",placeholder:"请输入设备名称"}]},i={terminalName:[{required:!0,message:"请输入设备名称",trigger:"blur"}],terminalType:[{required:!0,message:"请选择设备类型",trigger:"change"}]}}}]);