"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9310],{59310:function(e,t,o){o.r(t),o.d(t,{default:function(){return h}});var n=function(){var e=this,t=e._self._c;return t("div",{class:["login-wrapper",["","login-form-right","login-form-left"][e.direction]]},[t("el-form",{ref:"form",staticClass:"login-form ele-bg-white",attrs:{size:"large",model:e.form,rules:e.rules},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.submit.apply(null,arguments)}}},[t("h4",[e._v(e._s(e.$t("login.title")))]),t("el-form-item",{attrs:{prop:"username"}},[t("el-input",{attrs:{clearable:"","prefix-icon":"el-icon-user",placeholder:e.$t("login.username")},model:{value:e.form.username,callback:function(t){e.$set(e.form,"username",t)},expression:"form.username"}})],1),t("el-form-item",{attrs:{prop:"password"}},[t("el-input",{attrs:{"show-password":"","prefix-icon":"el-icon-lock",placeholder:e.$t("login.password")},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1),t("el-form-item",{attrs:{prop:"code"}},[t("div",{staticClass:"login-input-group"},[t("el-input",{attrs:{clearable:"","prefix-icon":"el-icon-_vercode",placeholder:e.$t("login.code")},on:{change:e.queryTenantList},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}}),e.captcha?t("img",{staticClass:"login-captcha",attrs:{alt:"",src:e.captcha},on:{click:e.changeCaptcha}}):e._e()],1)]),t("el-form-item",{attrs:{prop:"smsCode"}},[t("div",{staticClass:"login-input-group"},[t("el-input",{attrs:{clearable:"",placeholder:e.$t("login.smsCode"),"prefix-icon":"el-icon-_vercode"},model:{value:e.form.smsCode,callback:function(t){e.$set(e.form,"smsCode",t)},expression:"form.smsCode"}}),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",disabled:!!e.countdownTimer},on:{click:e.sendCode}},[e.countdownTimer?t("span",[e._v("已发送 "+e._s(e.countdownTime)+" s")]):t("span",[e._v("发送验证码")])])],1)]),e.tenantBool?t("el-form-item",{attrs:{prop:"tenantId"}},[t("el-select",{directives:[{name:"loading",rawName:"v-loading",value:e.loading1,expression:"loading1"}],staticClass:"el-from-select",attrs:{placeholder:e.$t("login.tenantId")},model:{value:e.form.tenantId,callback:function(t){e.$set(e.form,"tenantId",t)},expression:"form.tenantId"}},[t("template",{slot:"prefix"},[t("span",{staticClass:"spa-sl-icon"},[t("i",{staticClass:"el-icon-monitor"})])]),e._l(e.tenantList,(function(e){return t("el-option",{key:e.tenantId,attrs:{label:e.tenantName,value:e.tenantId,"prefix-icon":"el-icon-lock"}})}))],2)],1):e._e(),t("div",{staticClass:"el-form-item"},[t("el-checkbox",{model:{value:e.form.remember,callback:function(t){e.$set(e.form,"remember",t)},expression:"form.remember"}},[e._v(" "+e._s(e.$t("login.remember"))+" ")]),t("el-link",{staticClass:"ele-pull-right",attrs:{type:"primary",underline:!1},on:{click:function(t){return e.$router.push("/forget")}}},[e._v(" 忘记密码 ")])],1),t("div",{staticClass:"el-form-item"},[t("el-button",{staticClass:"login-btn",attrs:{size:"large",type:"primary",loading:e.loading},on:{click:e.submit}},[e._v(" "+e._s(e.loading?e.$t("login.loading"):e.$t("login.login"))+" ")])],1)],1),t("div",{staticClass:"login-copyright"},[e._v(" copyright © 2022 eleadmin.com all rights reserved. ")]),t("div",{staticStyle:{position:"absolute",right:"30px",top:"20px"}},[t("i18n-icon",{attrs:{"icon-style":{fontSize:"22px",color:"#fff",cursor:"pointer"}}})],1),t("div",{staticClass:"hidden-xs-only",staticStyle:{position:"absolute",right:"30px",bottom:"20px","z-index":"9"}},[t("el-radio-group",{attrs:{size:"mini"},model:{value:e.direction,callback:function(t){e.direction=t},expression:"direction"}},[t("el-radio-button",{attrs:{label:"2"}},[e._v("居左")]),t("el-radio-button",{attrs:{label:"0"}},[e._v("居中")]),t("el-radio-button",{attrs:{label:"1"}},[e._v("居右")])],1)],1)],1)},s=[],i=o(47859),r=o(73566),a=o(74925),l=o(33730),c={name:"Login",components:{I18nIcon:i.Z},data(){return{direction:0,loading:!1,loading1:!1,form:{username:"",password:"",smsCode:"",remember:!0,code:"",tenantId:null,v:(new Date).getTime(),codeLoading:!1,countdownTime:30,countdownTimer:null},codeLoading:!1,countdownTime:30,countdownTimer:null,captcha:"",text:"",tenantList:[],tenantBool:!1}},computed:{rules(){return{username:[{required:!0,message:this.$t("login.username"),type:"string",trigger:"blur"}],password:[{required:!0,message:this.$t("login.password"),type:"string",trigger:"blur"}],code:[{required:!0,message:this.$t("login.code"),type:"string",trigger:"blur"}],tenantId:[{required:!0,message:this.$t("login.tenantId"),type:"number",trigger:"change"},{pattern:l.XZ,message:"系统标识不正确,请联系管理员",type:"number",trigger:"change"}]}}},created(){(0,r.LP)()?this.goHome():this.changeCaptcha()},methods:{submit(){this.$refs.form.validate((e=>{if(!e)return!1;this.form.code.toLowerCase()===this.text?(this.loading=!0,(0,a.x4)(this.form).then((e=>{this.tenantList.forEach((e=>{e.tenantId==this.form.tenantId&&sessionStorage.setItem("PROJECT_NAME",e.tenantName)})),this.loading=!1,this.$message.success(e),this.goHome()})).catch((e=>{this.loading=!1,this.$message.error(e.message)}))):this.$message.error("图形验证码错误")}))},goHome(){var e,t,o;this.$router.push(null!==(e=null===(t=this.$route)||void 0===t||null===(o=t.query)||void 0===o?void 0:o.from)&&void 0!==e?e:"/").catch((()=>{}))},changeCaptcha(){(0,a.an)().then((e=>{var t,o;this.captcha=e.base64,this.text=e.text,null===(t=this.$refs)||void 0===t||null===(o=t.form)||void 0===o||o.clearValidate()})).catch((e=>{this.$message.error(e.message)}))},queryTenantList(){this.loading1=!0,(0,a.fY)(this.form).then((e=>{this.loading1=!1,this.tenantList=e,this.tenantList.length>1?this.tenantBool=!0:(this.tenantBool=!1,this.form.tenantId=this.tenantList[0].tenantId,console.log(this.form))})).catch((e=>{this.$message.error(e.message)}))},sendCode(){this.$refs.form.validate((e=>{if(!e)return!1;this.codeLoading=!0,(0,a.em)({username:this.form.username,code:this.form.code}).then((e=>{console.log(e),this.$message.success("短信验证码发送成功, 请注意查收!"),this.showImgCode=!1,this.codeLoading=!1,this.startCountdownTimer()})).catch((e=>{this.$message.error(e.message)}))}))},startCountdownTimer(){this.countdownTime=60,this.countdownTimer=setInterval((()=>{this.countdownTime<=1&&(clearInterval(this.countdownTimer),this.countdownTimer=null),this.countdownTime--}),1e3)}},destroyed(){this.countdownTimer&&clearInterval(this.countdownTimer)}},d=c,m=o(1001),u=(0,m.Z)(d,n,s,!1,null,"1fda6b5d",null),h=u.exports},33730:function(e,t,o){o.d(t,{$v:function(){return n},XZ:function(){return i},m0:function(){return s}});const n=/^1\d{10}$/,s=/^([a-zA-Z0-9_.-])+@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/,i=/^-?\d+$/}}]);