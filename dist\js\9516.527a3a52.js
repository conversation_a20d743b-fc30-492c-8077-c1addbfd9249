"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9516],{49516:function(t,e,a){a.r(e),a.d(e,{default:function(){return d}});var l=function(){var t=this,e=t._self._c;return e("el-table",{ref:"templateTable",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:""},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"55"}}),e("el-table-column",{attrs:{prop:"templateId",label:"模板ID",width:"80"}}),e("el-table-column",{attrs:{prop:"templateName",label:"模板名称"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.templateName||"未命名")+" ("+t._s(e.row.templateId)+") ")]}}])}),e("el-table-column",{attrs:{prop:"serviceOfferId",label:"模板类型"}}),e("el-table-column",{attrs:{prop:"masterPackageName",label:"主套餐名称"}}),e("el-table-column",{attrs:{prop:"status",label:"状态",width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:1===a.row.status?"success":"danger"}},[t._v(" "+t._s(1===a.row.status?"有效":"无效")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"createdDate",label:"创建时间",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.createdDate))+" ")]}}])})],1)},r=[],s=(a(74916),a(15306),{name:"TemplateSelector",props:{data:{type:Array,default:()=>[]},selectedIds:{type:Array,default:()=>[]}},data(){return{selection:[]}},computed:{tableData(){return this.data}},created(){this.$nextTick((()=>{this.selectedIds&&this.selectedIds.length>0&&this.tableData.forEach((t=>{this.selectedIds.includes(t.templateId.toString())&&this.$refs.templateTable.toggleRowSelection(t,!0)}))}))},methods:{formatDate(t){if(!t)return"-";t=t.toString().replace("T"," ");const e=new Date(t),a=e.getFullYear(),l=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0"),s=String(e.getHours()).padStart(2,"0"),n=String(e.getMinutes()).padStart(2,"0"),o=String(e.getSeconds()).padStart(2,"0");return`${a}-${l}-${r} ${s}:${n}:${o}`},handleSelectionChange(t){this.selection=t,this.$emit("selection-change",t)},getSelection(){return this.selection}}}),n=s,o=a(1001),c=(0,o.Z)(n,l,r,!1,null,"0be98da6",null),d=c.exports}}]);