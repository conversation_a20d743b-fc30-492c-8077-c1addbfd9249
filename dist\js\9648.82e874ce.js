"use strict";(self["webpackChunkhnyxs_admim_app"]=self["webpackChunkhnyxs_admim_app"]||[]).push([[9648,6487],{74952:function(e,t,a){a.d(t,{Z:function(){return h}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-search"},[t("hl-form",e._b({scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"handle-btns"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery<PERSON>lick}},[e._v("搜索")]),t("el-button",{on:{click:e.handleResetClick}},[e._v("重置")])],1)]},proxy:!0}]),model:{value:e.formData,callback:function(t){e.formData=t},expression:"formData"}},"hl-form",e.searchFormConfig,!1))],1)},l=[],r=(a(33948),function(){var e=this,t=e._self._c;return t("div",{staticClass:"hl-form"},[t("div",{staticClass:"header"},[e._t("header")],2),t("el-form",{attrs:{"label-width":e.labelWidth,model:e.formData,rules:e.rules}},[t("el-row",[e._l(e.formItems,(function(a){return[t("el-col",e._b({key:a.label},"el-col",e.colLayout,!1),[a.isHidden?e._e():t("el-form-item",{style:e.itemStyle,attrs:{label:a.label,rules:a.rules}},["input"===a.type||"password"===a.type?[t("el-input",e._b({attrs:{placeholder:a.placeholder,"show-password":"password"===a.type},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-input",a.otherOptions,!1))]:"select"===a.type?[t("el-select",e._b({staticStyle:{width:"100%"},attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-select",a.otherOptions,!1),e._l(a.options,(function(o){var l,r,n,i;return t("el-option",{key:null!==(l=o[a.optionValue])&&void 0!==l?l:o.value,attrs:{value:null!==(r=o[a.optionValue])&&void 0!==r?r:o.value,label:null!==(n=o[a.optionLabel])&&void 0!==n?n:o.title}},[e._v(e._s(null!==(i=o[a.optionLabel])&&void 0!==i?i:o.title))])})),1)]:"datepicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"twoDatePicker"===a.type?[t("el-date-picker",e._b({staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.formData[`${a.field}`],callback:function(t){e.$set(e.formData,`${a.field}`,t)},expression:"formData[`${item.field}`]"}},"el-date-picker",a.otherOptions,!1))]:"section"===a.type?[t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[0]}`],callback:function(t){e.$set(e.formData,`${a.field[0]}`,t)},expression:"formData[`${item.field[0]}`]"}},"el-input",a.otherOptions,!1))],1),t("el-col",{staticClass:"line",attrs:{span:2}},[e._v("-")]),t("el-col",{attrs:{span:11}},[t("el-input",e._b({attrs:{placeholder:a.placeholder},model:{value:e.formData[`${a.field[1]}`],callback:function(t){e.$set(e.formData,`${a.field[1]}`,t)},expression:"formData[`${item.field[1]}`]"}},"el-input",a.otherOptions,!1))],1)]:e._e()],2)],1)]})),e._t("footer")],2)],1)],1)}),n=[],i={props:{value:{type:Object,required:!0},formItems:{type:Array,default:()=>[]},rules:{type:Object,default:()=>{}},labelWidth:{type:String,default:"100px"},itemStyle:{type:Object,default:()=>({padding:"10px 40px"})},colLayout:{type:Object,default:()=>({xl:6,lg:8,md:12,sm:24,xs:24})}},data(){return{formData:{...this.value},pickerOptions:{shortcuts:[{text:"最近一周",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick(e){const t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},watch:{formData:{handler(e){this.$emit("input",e)},deep:!0}}},s=i,c=a(1001),p=(0,c.Z)(s,r,n,!1,null,"3585fc65",null),d=p.exports,u={components:{HlForm:d},props:{searchFormConfig:{type:Object,reuqired:!0}},data(){return{formOriginData:{},formData:{}}},created(){var e,t;const a=null!==(e=null===(t=this.searchFormConfig)||void 0===t?void 0:t.formItems)&&void 0!==e?e:[];for(const o of a)Array.isArray(o.field)?o.field.forEach((e=>{this.formOriginData[e]=""})):this.formOriginData[o.field]="";this.formData=this.formOriginData},methods:{handleResetClick(){for(const e in this.formOriginData)this.formData[`${e}`]=this.formOriginData[e];this.$emit("resetBtnClick")},handleQueryClick(){const e={...this.formData};for(const t in e)""===e[t]&&delete e[t];this.$emit("queryBtnClick",e)}}},f=u,m=(0,c.Z)(f,o,l,!1,null,"a0a65568",null),h=m.exports},9648:function(e,t,a){a.r(t),a.d(t,{default:function(){return y}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ele-body"},[t("el-card",{attrs:{shadow:"never"}},[t("pageSearch",{attrs:{searchFormConfig:e.searchFormConfig},on:{queryBtnClick:e.reload}}),t("ele-pro-table",{key:e.itemKey,ref:"table",attrs:{columns:e.columns,datasource:e.datasource,"cache-key":"playeCityModuleTable"},on:{"selection-change":e.selectionLineChangeHandle},scopedSlots:e._u([{key:"toolbar",fn:function(){return[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.deriveClick}},[e._v(" 批量导出 ")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.approveClick}},[e._v(" 提交审批 ")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(t){e.isShowApprove=!0}}},[e._v(" 批量提交 ")])]},proxy:!0},{key:"userKpi",fn:function({row:a}){return[t("el-select",{attrs:{placeholder:"请选择"},model:{value:a.USER_KPI,callback:function(t){e.$set(a,"USER_KPI",t)},expression:"row.USER_KPI"}},e._l(["A","B","C","D"],(function(a){return t("el-option",{key:a,attrs:{label:a,value:a},on:{change:e.changeKPIAssess}})})),1)]}}])})],1),t("ele-modal",{attrs:{width:"680px",title:"批量审批",visible:e.isShowApprove},on:{"update:visible":function(t){e.isShowApprove=t}}},[t("div",{staticClass:"approve-content"},[t("div",{staticClass:"item first"},[t("div",{staticClass:"title"},[t("span",{staticClass:"p1"},[e._v("下载模板：")]),t("p",{staticClass:"p2"},[e._v("为提高导入的成功率，请下载并使用系统提供的模板: "),t("el-button",{staticClass:"btn-approve",attrs:{size:"small",type:"primary"},on:{click:e.templateClick}},[e._v(" 下载模板 ")])],1)])]),t("div",{staticClass:"item"},[t("div",{staticClass:"title"},[t("span",{staticClass:"p1"},[e._v("上传文件：")]),t("p",{staticClass:"p2"},[e._v("仅支持:.xlsx，.xls,;文件大小:≤4M "),t("el-button",{staticClass:"btn-approve",attrs:{size:"small",type:"primary"},on:{click:e.intoClick}},[e._v(" 开始导入 ")])],1)])])])]),t("arpproverDialog",{attrs:{visible:e.isShowApproverDialog},on:{"update:visible":function(t){e.isShowApproverDialog=t},select:e.handleApproverSelect}})],1)},l=[],r=(a(21703),a(18684));async function n(e){const t=await r.Z.post("/hnzhsl/hnslSalaryReview/userReviewList",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function i(e={}){const t=await r.Z.post("/hnzhsl/hnslStudentApproval/outputSalaryStatement",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function s(e={}){const t=await r.Z.post("/hnzhsl/hnslUser/decode/saveBatch",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}var c=a(79103),p=a(40846),d=a(64446),u=a(16487),f=a(74952),m={components:{pageSearch:f.Z},data(){return{columns:u.columns,searchFormConfig:u.searchFormConfig,cardRef:null,itemKey:null,selectData:[],isShowApprove:!1,isShowApproverDialog:!1}},methods:{async templateClick(){const e=await(0,d.cI)();console.log(e),window.open(`${p.JV}?name=${e.fileName}`)},async intoClick(){const e=await(0,c.uR)();window.open(`${p.JV}?name=${e.fileName}`)},changeKPIAssess(e){console.log(e)},approveClick(){if(!this.selectData.length)return this.$message("清先选择提交审核记录");s(this.selectData)},async deriveClick(){this.isShowApproverDialog=!0},async handleApproverSelect(e){const t={status:"",appSettingId:e},a=await i(t);window.open(`${p.JV}?name=${a.fileName}`)},selectionLineChangeHandle(e){console.log(e),this.selectData=e},datasource({page:e,limit:t,where:a,order:o}){return n({page:e,limit:t,...a,...o})},reload(e){this.$refs.table.reload({page:1,where:e})}}},h=m,w=a(1001),v=(0,w.Z)(h,o,l,!1,null,"70f22e04",null),y=v.exports},40846:function(e,t,a){a.d(t,{JV:function(){return l},cn:function(){return r},km:function(){return n}});var o=a(18816);const l=`${o.CT}/download/exportDaoUsers`,r=`${o.CT}/download/template`,n=`${o.CT}/download/downloadFile`;o.CT,o.CT},79103:function(e,t,a){a.d(t,{GS:function(){return l},Po:function(){return r},fe:function(){return n},uR:function(){return i}});a(21703);var o=a(18684);async function l(e){const t=await o.Z.post("/hnzhsl/hnslStudentApproval/page",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await o.Z.post("/hnzhsl/hnslStudentApproval/updateBatch",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function n(e={}){const t=await o.Z.post("/hnzhsl/hnslStudentApproval/outputReviewList",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function i(e={}){const t=await o.Z.post("/hnzhsl/hnslStudentApproval/importAuditManagement",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}},64446:function(e,t,a){a.d(t,{As:function(){return n},Wi:function(){return r},ZS:function(){return l},_j:function(){return i},cI:function(){return s}});a(21703);var o=a(18684);async function l(e){const t=await o.Z.post("/hnzsx/hnzsxAppGoodsDetails/queryCgrList",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function r(e){const t=await o.Z.post("/avoid/file/hnzsxUpload",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function n(e){const t=await o.Z.post("/file/decode/upload",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function i(e){const t=await o.Z.post("/avoid/file/hnzsxVideoUpload",e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}async function s(e){const t=await o.Z.get("/download/template?fileName="+e);return 0===t.data.code?t.data.data:Promise.reject(new Error(t.data.message))}},16487:function(e,t,a){a.r(t),a.d(t,{columns:function(){return o},searchFormConfig:function(){return l}});const o=[{width:45,type:"selection",columnKey:"expand",align:"center",slot:"expand"},{prop:"a",label:"账期",align:"center",showOverflowTooltip:!0},{prop:"b",label:"是否可提交审批",align:"center",showOverflowTooltip:!0},{prop:"CITY_CODE",label:"地市",align:"center",showOverflowTooltip:!0},{prop:"SCHOOL_NAME",label:"学校名称",align:"center",showOverflowTooltip:!0},{prop:"USER_NAME",label:"学子名称",align:"center",showOverflowTooltip:!0},{prop:"TEAM_LEVEL",label:"团队角色",align:"center",showOverflowTooltip:!0},{prop:"USER_CARD",label:"身份证号",align:"center",showOverflowTooltip:!0},{prop:"USER_AGE",label:"年龄",align:"center",showOverflowTooltip:!0},{prop:"USER_PHONE",label:"工号",align:"center",showOverflowTooltip:!0},{prop:"USER_SERVICE_TIME",label:"服务时长",align:"center",showOverflowTooltip:!0},{prop:"TEAM_NAME",label:"团队名称",align:"center",showOverflowTooltip:!0},{prop:"TEAM_COUNT",label:"团队人数",align:"center",showOverflowTooltip:!0},{prop:"USER_KPI",label:"KPI考核",align:"center",slot:"userKpi",width:"180",showOverflowTooltip:!0},{prop:"USER_DATE",label:"注册时间",align:"center",showOverflowTooltip:!0},{prop:"BUSINESS_NUMBER1",label:"新装数量",align:"center",showOverflowTooltip:!0},{prop:"BUSINESS_INTEGRATION1",label:"新装积分",align:"center",showOverflowTooltip:!0},{prop:"businessNumber2",label:"维系数量",align:"center",showOverflowTooltip:!0},{prop:"businessIntegration2",label:"维系积分",align:"center",showOverflowTooltip:!0},{prop:"businessIntegration3",label:"其他积分",align:"center",showOverflowTooltip:!0},{prop:"teamIntegration",label:"团队积分",align:"center",showOverflowTooltip:!0},{prop:"money1",label:"个人服务费",align:"center",showOverflowTooltip:!0},{prop:"money2",label:"团队服务费",align:"center",showOverflowTooltip:!0},{prop:"money3",label:"应发",align:"center",showOverflowTooltip:!0},{prop:"money4",label:"个税",align:"center",showOverflowTooltip:!0},{prop:"money5",label:"实发",align:"center",showOverflowTooltip:!0},{prop:"userBankNumber",label:"银行卡号",align:"center",showOverflowTooltip:!0},{prop:"userBankName",label:"银行名称",align:"center",showOverflowTooltip:!0}],l={labelWidth:"100px",itemStyle:{padding:"10px"},colLayout:{span:6},formItems:[{field:"schoolName",type:"input",label:"学校名称",placeholder:"请输入学校名称"},{field:"teamLevel",type:"select",label:"角色",placeholder:"请选择角色",options:[{title:"全部",value:1},{title:"核心合伙人",value:2},{title:"普通合伙人",value:3},{title:"直销员",value:4}]},{field:"userName",type:"input",label:"学子",placeholder:"请输入姓名/手机号码"},{field:"createdDate",type:"datepicker",label:"数据时间",otherOptions:{placeholder:"请选择数据时间","value-format":"yyyy-MM-dd"}},{field:["userJF1","userJF2"],type:"section",label:"个人积分",placeholder:"请输入积分区间段"},{field:["teamJF1","teamJF2"],type:"section",label:"团队积分",placeholder:"请输入积分区间段"},{field:"approvalStatus",type:"select",label:"是否可提交",placeholder:"请选择",options:[{title:"全部",value:""},{title:"是",value:2},{title:"否",value:3}]}]}}}]);